package com.xk.goods.infrastructure.data.po.serial;

import com.xk.goods.domain.model.serial.entity.SerialGroupCategoryEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 卡密组类目表
 * @TableName g_serial_group_category
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({@AutoMapper(target = SerialGroupCategoryEntity.class)})
public class GSerialGroupCategory {
    /**
     * 卡密组类目id
     */
    private Long serialGroupCategoryId;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 卡密组类型：1球队卡密组；2自定义卡密组
     */
    private Integer groupType;

    /**
     * 模块类型：1球队；2动漫
     */
    private Integer blockType;

    /**
     * 是否显示（0：不显示，1：显示）
     */
    private Integer isShow;

    /**
     * 状态（0：不正常，1：正常）
     */
    private Integer status;

    /**
     * 是否删除（0：不删除，1：已删除）
     */
    private Integer deleted;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}