package com.xk.goods.infrastructure.data.po.score;

import java.util.Date;

import com.myco.framework.cache.CacheLevel;
import com.myco.framework.cache.annotations.Cache;
import com.myco.framework.cache.annotations.KeyProperty;
import com.myco.framework.cache.annotations.MiniTable;
import com.xk.goods.domain.model.score.entity.ScoreRuleEntity;
import com.xk.goods.infrastructure.convertor.score.ScoreRuleTypeEnumConvertor;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品评分规则表
 *
 * @TableName g_score_rule
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Cache(level = CacheLevel.REDIS)
@MiniTable
@AutoMappers({@AutoMapper(target = ScoreRuleEntity.class,
        uses = {CommonStatusEnumConvertor.class, ScoreRuleTypeEnumConvertor.class})})
public class GScoreRule {
    /**
     * 规则类型 1-基础 2-福盒 3-边锋盒子 4-错卡密 5-原盒
     */
    @KeyProperty
    private Integer scoreRuleType;

    /**
     * 卡商7日消费额加分
     */
    private Integer corpPayment7dayRule;

    /**
     * 24小时销售金额分数加分
     */
    private Integer saleAmount24hRule;

    /**
     * 累计销售金额分数加分
     */
    private Integer saleAmountTotalRule;

    /**
     * 24小时购买人数分数加分
     */
    private Integer buyerCount24hRule;

    /**
     * 累计购买人数分数加分
     */
    private Integer buyerCountTotalRule;

    /**
     * 半小时销售数量加分
     */
    private Integer saleCount30mRule;

    /**
     * 累计销售数量加分
     */
    private Long saleCountTotalRule;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;
}
