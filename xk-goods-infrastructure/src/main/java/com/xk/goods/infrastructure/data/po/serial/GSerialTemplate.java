package com.xk.goods.infrastructure.data.po.serial;

import com.xk.goods.domain.model.template.entity.SerialTemplateEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

import java.util.Date;

/**
 * 自定义卡密模板表
 * @TableName g_serial_template
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({@AutoMapper(target = SerialTemplateEntity.class)})
public class GSerialTemplate {
    /**
     * 卡密模板id
     */
    private Long serialTemplateId;

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 卡密模板名称
     */
    private String name;

    /**
     * 卡密数量
     */
    private Integer num;

    /**
     * 卡密模板：卡密条目通过逗号拼接；不同组合通过分号拼接；
     */
    private String serialTemplate;

    /**
     * 是否删除（0：不删除，1：已删除）
     */
    private Integer deleted;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

}