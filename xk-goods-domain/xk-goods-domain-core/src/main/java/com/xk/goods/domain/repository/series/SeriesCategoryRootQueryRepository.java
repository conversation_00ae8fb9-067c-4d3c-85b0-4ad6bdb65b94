package com.xk.goods.domain.repository.series;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.goods.domain.model.series.entity.SeriesCategoryEntity;
import com.xk.goods.domain.model.series.id.SeriesCategoryIdentifier;

import reactor.core.publisher.Mono;

public interface SeriesCategoryRootQueryRepository extends IQueryRepository {

    /**
     * 根据id获取系列
     * 
     * @param identifier identifier
     * @return Mono<SeriesCategoryEntity>
     */
    Mono<SeriesCategoryEntity> getById(SeriesCategoryIdentifier identifier);

    Mono<SeriesCategoryEntity> searchSeriesCategoryByName(String name);
}
