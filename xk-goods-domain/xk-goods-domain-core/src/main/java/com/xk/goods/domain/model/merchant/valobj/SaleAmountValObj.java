package com.xk.goods.domain.model.merchant.valobj;

import com.xk.goods.domain.model.merchant.id.MerchantProductIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaleAmountValObj {

    private MerchantProductIdentifier identifier;

    /**
     * 时间段
     */
    private String timeStamp;

    private Long amount;
}
