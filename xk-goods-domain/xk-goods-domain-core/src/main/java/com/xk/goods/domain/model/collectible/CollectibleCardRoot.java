package com.xk.goods.domain.model.collectible;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.goods.domain.model.collectible.entity.CollectibleCardEntity;
import com.xk.goods.domain.model.collectible.id.CollectibleCardIdentifier;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import reactor.core.publisher.Mono;

@Getter
public class CollectibleCardRoot extends DomainRoot<CollectibleCardIdentifier> {

    private final CollectibleCardEntity collectibleCardEntity;

    @Builder
    public CollectibleCardRoot(@NonNull CollectibleCardIdentifier identifier,
            CollectibleCardEntity collectibleCardEntity) {
        super(identifier);
        this.collectibleCardEntity = collectibleCardEntity;
    }

    @Override
    public Validatable<CollectibleCardIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

    public Mono<Void> initDefault() {
        this.collectibleCardEntity.setDefault();
        return checkSave();
    }

    public Mono<Void> checkSave() {
        return Mono.empty();
    }

    public Mono<Void> checkRemove() {
        return Mono.empty();
    }
}
