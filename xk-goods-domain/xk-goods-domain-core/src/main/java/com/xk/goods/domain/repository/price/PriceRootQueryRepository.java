package com.xk.goods.domain.repository.price;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.price.PriceRoot;
import com.xk.goods.domain.model.price.id.PriceIdentifier;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface PriceRootQueryRepository extends IQueryRepository {

    /**
     * 获取root
     * 
     * @param identifier identifier
     * @return Mono<PriceRoot>
     */
    Mono<PriceRoot> getRoot(PriceIdentifier identifier);

    /**
     * 根据商品id获取价格
     * 
     * @param goodsIdentifier goodsIdentifier
     * @return Flux<PriceRoot>
     */
    Flux<PriceRoot> searchByGoodsId(GoodsIdentifier goodsIdentifier);
}
