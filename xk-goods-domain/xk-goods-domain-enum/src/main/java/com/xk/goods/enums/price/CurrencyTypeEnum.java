package com.xk.goods.enums.price;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */

@Getter
@RequiredArgsConstructor
public enum CurrencyTypeEnum {

    CNY(1, "人民币"),
    USD(2, "美元"),
    ;

    private static final Map<Integer, CurrencyTypeEnum> MAP;

    static {
        MAP = Arrays.stream(CurrencyTypeEnum.values())
                .collect(Collectors.toMap(CurrencyTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static CurrencyTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
