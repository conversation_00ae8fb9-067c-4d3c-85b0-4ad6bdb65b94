package com.xk.goods.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ListingStatusEnum {

    UP(1, "上架"),
    DOWN(2, "下架"),
    TIME_OUT_DOWN(3,"到期下架");

    private static final Map<Integer, ListingStatusEnum> MAP;

    static {
        MAP = Arrays.stream(ListingStatusEnum.values())
                .collect(Collectors.toMap(ListingStatusEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static ListingStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
