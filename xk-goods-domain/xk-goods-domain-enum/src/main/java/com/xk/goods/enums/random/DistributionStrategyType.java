package com.xk.goods.enums.random;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分发策略类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistributionStrategyType {

    /**
     * 伪随机分发策略 对特殊物品进行控制分布，避免扎堆
     */
    PSEUDO_RANDOM("PSEUDO_RANDOM", "伪随机分发策略"),

    /**
     * 简单随机分发策略 完全随机打乱，不考虑特殊物品分布
     */
    SIMPLE_RANDOM("SIMPLE_RANDOM", "简单随机分发策略");

    /**
     * 策略代码
     */
    private final String code;

    /**
     * 策略名称
     */
    private final String name;


    private static final Map<String, DistributionStrategyType> MAP =
            Arrays.stream(DistributionStrategyType.values())
                    .collect(Collectors.toMap(Enum::name, enumValue -> enumValue));

    public static DistributionStrategyType getByCode(String name) {
        return MAP.get(name);
    }
}
