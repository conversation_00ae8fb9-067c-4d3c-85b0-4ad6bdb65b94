package com.xk.goods.enums.merchant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@RequiredArgsConstructor
public enum ProductRandomTypeEnum {
    TEAM_RANDOM_PLAYER(1, "选队/随机球员","随机球员"),
    TEAM_RANDOM_CARD(2, "选队/随机卡种","随机卡种"),
    NON_TEAM_RANDOM_CARD_UNNUMBERED(10, "非选队/随机卡种(不带编)","随机卡种(不带编)"),
    NON_TEAM_RANDOM_CARD_NUMBERED(11, "非选队/随机卡种(带编)","随机卡种(带编)"),
    NON_TEAM_RANDOM_TEAM(12, "非选队/随机球队","随机球队"),
    NON_TEAM_RANDOM_PLAYER(13, "非选队/随机球员","随机球员")
    ;

    private static final Map<Integer, ProductRandomTypeEnum> MAP;

    static {
        MAP = Arrays.stream(ProductRandomTypeEnum.values())
                .collect(Collectors.toMap(ProductRandomTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;
    private final String desc;

    public static ProductRandomTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
