package com.xk.goods.enums.merchant;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ProductAuditStatusEnum {
    AUDITING(1, "审核中"),
    AUDITED(2, "已审核"),
    UN_PASS(3, "未通过");
    
    private static final Map<Integer, ProductAuditStatusEnum> MAP;

    static {
        MAP = Arrays.stream(ProductAuditStatusEnum.values())
                .collect(Collectors.toMap(ProductAuditStatusEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static ProductAuditStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
