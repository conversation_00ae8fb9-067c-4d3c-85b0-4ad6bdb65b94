package com.xk.goods.enums.score;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */

@Getter
@RequiredArgsConstructor
public enum ScoreRuleTypeEnum {

    BASIC(1, "基础"),
    FORTUNE_BOX(2, "福盒"),
    EDGE_BOX(3, "边锋盒子"),
    RUBBED_CARD_PACK(4, "搓卡密"),
    ORIGINAL_BOX(5, "原盒");

    private static final Map<Integer, ScoreRuleTypeEnum> MAP;

    static {
        MAP = Arrays.stream(ScoreRuleTypeEnum.values())
                .collect(Collectors.toMap(ScoreRuleTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static ScoreRuleTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
