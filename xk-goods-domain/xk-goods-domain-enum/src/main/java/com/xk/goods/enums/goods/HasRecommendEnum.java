package com.xk.goods.enums.goods;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@RequiredArgsConstructor
public enum HasRecommendEnum {
    RECOMMEND(1, "推荐"),
    NOT_RECOMMEND(2, "不推荐"),
    ;
    private static final Map<Integer, HasRecommendEnum> MAP;

    static {
        MAP = Arrays.stream(HasRecommendEnum.values())
                .collect(Collectors.toMap(HasRecommendEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static HasRecommendEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
