package com.xk.goods.enums.serial;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@RequiredArgsConstructor
public enum ShowEnum {
    NOT_SHOW(0, "不显示"),
    SHOW(1, "显示"),
    ;
    private static final Map<Integer, ShowEnum> MAP;

    static {
        MAP = Arrays.stream(ShowEnum.values())
                .collect(Collectors.toMap(ShowEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static ShowEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
