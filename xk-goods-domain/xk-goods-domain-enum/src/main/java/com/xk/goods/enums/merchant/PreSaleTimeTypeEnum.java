package com.xk.goods.enums.merchant;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum PreSaleTimeTypeEnum {
    TIME(1, "具体时间"),
    INTERVAL_DATE(2, "上架后预售时长");
    
    private static final Map<Integer, PreSaleTimeTypeEnum> MAP;

    static {
        MAP = Arrays.stream(PreSaleTimeTypeEnum.values())
                .collect(Collectors.toMap(PreSaleTimeTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static PreSaleTimeTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
