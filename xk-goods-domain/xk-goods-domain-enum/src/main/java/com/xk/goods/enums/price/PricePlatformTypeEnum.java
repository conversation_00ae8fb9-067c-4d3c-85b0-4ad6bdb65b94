package com.xk.goods.enums.price;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum PricePlatformTypeEnum {

    IOS(1, "ios"),
    ANDROID(2, "安卓"),
    HARMONY_OS(3,"鸿蒙");

    private static final Map<Integer, PricePlatformTypeEnum> MAP;

    static {
        MAP = Arrays.stream(PricePlatformTypeEnum.values())
                .collect(Collectors.toMap(PricePlatformTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static PricePlatformTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
