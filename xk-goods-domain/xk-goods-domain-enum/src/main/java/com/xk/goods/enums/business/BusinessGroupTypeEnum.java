package com.xk.goods.enums.business;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum BusinessGroupTypeEnum {

    MEMBER(1, "球员"),
    SERIES(2, "系列"),
    GOODS(3, "商品"),
    GIFT(4, "赠品报告"),
    COLOR(5, "卡密特效"),
    ;

    private static final Map<Integer, BusinessGroupTypeEnum> MAP;

    static {
        MAP = Arrays.stream(BusinessGroupTypeEnum.values())
                .collect(Collectors.toMap(BusinessGroupTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static BusinessGroupTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
