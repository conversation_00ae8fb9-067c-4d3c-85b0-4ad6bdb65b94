package com.xk.goods.enums.random;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物品类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistributionItemType {
    /**
     * 普通物品 - 可以随机分布
     */
    NORMAL("NORMAL", "普通物品"),

    /**
     * 特殊物品 - 需要控制分布，避免扎堆
     */
    SPECIAL("SPECIAL", "特殊物品");

    private final String name;
    private final String description;

    private static final Map<String, DistributionItemType> MAP =
            Arrays.stream(DistributionItemType.values())
                    .collect(Collectors.toMap(Enum::name, enumValue -> enumValue));

    public static DistributionItemType getByCode(String name) {
        return MAP.get(name);
    }
}
