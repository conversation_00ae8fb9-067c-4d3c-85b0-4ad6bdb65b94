package com.xk.goods.enums.merchant;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum MerchantProductStatusEnum {
    AUDITING(1, "审核中"),
    AUDITED(2, "已审核"),
    UN_PASS(3, "未通过"),
    WAIT_PUBLICITY(4, "待公示"),
    WAIT_REPORT(5,"待录入"),
    FINISHED(6, "已完成"),
    NOT_SOLD_OUT_OR_REFUND(7, "未售罄/已退款"),
    RECYCLE(8, "回收站"),
    REPORTED(9, "已录入")
    ;
    
    private static final Map<Integer, MerchantProductStatusEnum> MAP;

    static {
        MAP = Arrays.stream(MerchantProductStatusEnum.values())
                .collect(Collectors.toMap(MerchantProductStatusEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static MerchantProductStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
