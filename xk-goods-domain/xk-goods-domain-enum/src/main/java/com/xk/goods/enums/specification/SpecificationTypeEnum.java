package com.xk.goods.enums.specification;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum SpecificationTypeEnum {

    PRODUCT(1, "商品类型"),
    GIFT(2, "赠品类型"),
    ;

    private static final Map<Integer, SpecificationTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SpecificationTypeEnum.values())
                .collect(Collectors.toMap(SpecificationTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static SpecificationTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
