package com.xk.goods.enums.random;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: killer
 **/
@Getter
@AllArgsConstructor
public enum DistributionItemStatusEnum {
    /**
     * 未使用
     */
    UNUSED(0),
    /**
     * 已使用
     */
    USED(1),

    ;

    private final Integer value;

    private static final Map<Integer, DistributionItemStatusEnum> MAP =
            Arrays.stream(DistributionItemStatusEnum.values()).collect(
                    Collectors.toMap(DistributionItemStatusEnum::getValue, enumValue -> enumValue));

    public static DistributionItemStatusEnum getByCode(Integer value) {
        return MAP.get(value);
    }
}
