package com.xk.goods.enums.price;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */

@Getter
@RequiredArgsConstructor
public enum DefaultPriceTypeEnum {

    FIRST(1, "第一个"),
    LOWEST(2, "最低价"),
    HIGHEST(3, "最高价"),
    ;

    private static final Map<Integer, DefaultPriceTypeEnum> MAP;

    static {
        MAP = Arrays.stream(DefaultPriceTypeEnum.values())
                .collect(Collectors.toMap(DefaultPriceTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static DefaultPriceTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
