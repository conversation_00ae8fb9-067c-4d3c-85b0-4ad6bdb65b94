package com.xk.goods.enums.price;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PriceTypeEnum {

    COST_PRICE(1,"成本价"),
    SALE_PRICE(2,"销售价"),
    ;

    private final Integer code;

    private final String msg;

    private static final Map<Integer, PriceTypeEnum> MAP;

    static {
        MAP = Arrays.stream(PriceTypeEnum.values())
                .collect(Collectors.toMap(PriceTypeEnum::getCode, enumValue -> enumValue));
    }

    public static PriceTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
