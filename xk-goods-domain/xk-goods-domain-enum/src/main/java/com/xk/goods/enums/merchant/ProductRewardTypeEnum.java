package com.xk.goods.enums.merchant;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ProductRewardTypeEnum {
    COUPON(1, "优惠券"),
    CUSTOM_INPUT(2, "自定义输入");

    private static final Map<Integer, ProductRewardTypeEnum> MAP;

    static {
        MAP = Arrays.stream(ProductRewardTypeEnum.values())
                .collect(Collectors.toMap(ProductRewardTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static ProductRewardTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
