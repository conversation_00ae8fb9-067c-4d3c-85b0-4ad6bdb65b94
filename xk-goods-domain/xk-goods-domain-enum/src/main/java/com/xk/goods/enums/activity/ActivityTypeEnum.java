package com.xk.goods.enums.activity;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ActivityTypeEnum {

    DISCOUNT(1, "满减"),
    FIRST_BUY(2, "首购优惠"),
    REMAIN_RANDOM(3, "剩余随机"),
    COUPON(4, "优惠券"),
    ;

    private static final Map<Integer, ActivityTypeEnum> MAP;

    static {
        MAP = Arrays.stream(ActivityTypeEnum.values())
                .collect(Collectors.toMap(ActivityTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static ActivityTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
