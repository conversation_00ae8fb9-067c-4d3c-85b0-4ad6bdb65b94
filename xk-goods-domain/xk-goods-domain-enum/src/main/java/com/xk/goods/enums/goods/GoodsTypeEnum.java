package com.xk.goods.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum GoodsTypeEnum {
    MALL_PRODUCT(1, "商城商品"),
    MATERIAL_PRODUCT(2, "物料商品"),
    COLLECTIBLE_CARD(3, "收藏卡"),
    MERCHANT_PRODUCT(4, "商家商品")
    ;
    private static final Map<Integer, GoodsTypeEnum> MAP;

    static {
        MAP = Arrays.stream(GoodsTypeEnum.values())
                .collect(Collectors.toMap(GoodsTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static GoodsTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
