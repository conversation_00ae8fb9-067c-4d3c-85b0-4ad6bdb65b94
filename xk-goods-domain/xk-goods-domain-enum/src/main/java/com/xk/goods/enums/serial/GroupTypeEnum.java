package com.xk.goods.enums.serial;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@RequiredArgsConstructor
public enum GroupTypeEnum {
    ORIGINAL(1, "原盒卡密组"),
    SPECIAL(2, "自定义卡密组"),
    ;
    private static final Map<Integer, GroupTypeEnum> MAP;

    static {
        MAP = Arrays.stream(GroupTypeEnum.values())
                .collect(Collectors.toMap(GroupTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static GroupTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
