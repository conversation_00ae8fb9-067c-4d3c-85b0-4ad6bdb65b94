package com.xk.infrastructure.repository.statistics;

import com.myco.mydata.domain.model.Identifier;
import com.xk.domain.model.statistics.StatisticsEntity;
import com.xk.domain.model.statistics.StatisticsIdentifier;
import com.xk.domain.model.statistics.StatisticsRoot;
import com.xk.domain.repository.statistics.StatisticsRootRepository;
import com.xk.infrastructure.cache.dao.statistics.StatisticsDao;
import com.xk.infrastructure.cache.key.statistics.StatisticsKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Slf4j
@Repository
@RequiredArgsConstructor
public class StatisticsRootRepositoryImpl implements StatisticsRootRepository {

    private final StatisticsDao statisticsDao;


    @Override
    public Mono<Void> save(StatisticsRoot root) {
        return null;
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(StatisticsRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(StatisticsRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(StatisticsRoot root) {
        return Mono.fromRunnable((()->{
            StatisticsIdentifier identifier = root.getIdentifier();
            statisticsDao.delValue(StatisticsKey.builder()
                    .countBusiType(identifier.getStatisticsBusiType())
                    .key(identifier.getKey())
                            .field(identifier.getField())
                    .build());
        }));
    }

    @Override
    public Mono<Void> removeByKey(StatisticsRoot root) {
        return Mono.fromRunnable((()->{
            StatisticsIdentifier identifier = root.getIdentifier();
            statisticsDao.delKey(StatisticsKey.builder()
                    .countBusiType(identifier.getStatisticsBusiType())
                    .key(identifier.getKey())
                    .build());
        }));
    }

    @Override
    public Mono<Long> increment(StatisticsRoot root) {

        return Mono.fromCallable(()->{
            StatisticsEntity entity = root.getStatisticsEntity();
            StatisticsKey key = StatisticsKey.builder()
                    .countBusiType(entity.getStatisticsBusiType())
                    .key(entity.getKey())
                    .field(entity.getField())
                    .build();
            return statisticsDao.incrValue(key, entity.getValue());
        });
    }
}
