package com.xk.infrastructure.repository.auth.item;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.domain.model.auth.item.ItemEntity;
import com.xk.domain.repository.auth.item.ItemRootQueryRepository;
import com.xk.infrastructure.data.persistence.auth.item.AuthItemMapper;
import com.xk.infrastructure.data.po.auth.item.AuthItem;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Slf4j
@Repository
@RequiredArgsConstructor
public class ItemRootQueryRepositoryImpl implements ItemRootQueryRepository {

    private final AuthItemMapper authItemMapper;

    private final Converter converter;

    @Override
    public Mono<ItemEntity> findById(StringIdentifier identifier) {
        return this.getById(identifier, (iden) -> authItemMapper.selectByPrimaryKey(
                AuthItem.builder().itemId(iden.id()).build()), ItemEntity.class, converter::convert);
    }

    @Override
    public Flux<ItemEntity> searchPager(Pagination pagination) {
        return this.search(pagination, authItemMapper::selectPager, ItemEntity.class, this.converter::convert);
    }

    @Override
    public Flux<ItemEntity> searchByPlatform(ItemEntity itemEntity) {
        return find(() -> authItemMapper.searchByPlatform(converter.convert(itemEntity, AuthItem.class)), ItemEntity.class, converter::convert);
    }

}
