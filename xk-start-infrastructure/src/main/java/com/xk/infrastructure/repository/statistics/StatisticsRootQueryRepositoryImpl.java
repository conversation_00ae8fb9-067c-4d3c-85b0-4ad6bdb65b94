package com.xk.infrastructure.repository.statistics;

import com.myco.mydata.commons.util.StringUtils;
import com.xk.domain.model.statistics.StatisticsEntity;
import com.xk.domain.model.statistics.StatisticsIdentifier;
import com.xk.domain.repository.statistics.StatisticsRootQueryRepository;
import com.xk.infrastructure.cache.dao.statistics.StatisticsDao;
import com.xk.infrastructure.cache.key.statistics.StatisticsKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Slf4j
@Repository
@RequiredArgsConstructor
public class StatisticsRootQueryRepositoryImpl implements StatisticsRootQueryRepository {

    private final StatisticsDao statisticsDao;

    @Override
    public Mono<StatisticsEntity> findById(StatisticsIdentifier identifier) {
        return Mono.defer(()->{
            String value = statisticsDao.getValue(StatisticsKey.builder()
                    .countBusiType(identifier.getStatisticsBusiType())
                    .key(identifier.getKey())
                    .field(identifier.getField())
                    .build());
            if (StringUtils.isBlank(value)){
                return Mono.empty();
            }
            return Mono.just(StatisticsEntity.builder()
                    .statisticsBusiType(identifier.getStatisticsBusiType())
                    .key(identifier.getKey())
                    .field(identifier.getField())
                    .value(Long.parseLong(value))
                    .build());
        });
    }

    @Override
    public Flux<StatisticsEntity> findByKey(StatisticsEntity key) {
        return Mono.fromCallable(()->{
                    return statisticsDao.getAllValue(StatisticsKey.builder()
                    .countBusiType(key.getStatisticsBusiType())
                    .key(key.getKey())
                    .build());
        }).flatMapMany(map->Flux.fromIterable(map.entrySet()))
                .map(entry->{
                    return StatisticsEntity.builder()
                            .statisticsBusiType(key.getStatisticsBusiType())
                            .key(key.getKey())
                            .field(entry.getKey())
                            .value(Long.parseLong(entry.getValue()))
                            .build();
                });
    }
}
