package com.xk.infrastructure.repository.auth.item;

import com.myco.mydata.domain.model.Identifier;
import com.xk.domain.model.auth.item.ItemRoot;
import com.xk.domain.repository.auth.item.ItemRootRepository;
import com.xk.infrastructure.data.persistence.auth.item.AuthItemMapper;
import com.xk.infrastructure.data.po.auth.item.AuthItem;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Slf4j
@Repository
@RequiredArgsConstructor
public class ItemRootRepositoryImpl implements ItemRootRepository {

    private final AuthItemMapper authItemMapper;

    private final Converter converter;

    @Override
    public Mono<Void> save(ItemRoot root) {
        return this.save(root.getItemEntity(), AuthItem.class, converter::convert, authItemMapper::insertSelective);

    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(ItemRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(ItemRoot root) {
        return this.update(root.getItemEntity(), AuthItem.class, converter::convert, authItemMapper::updateByPrimaryKeySelective);

    }

    @Override
    public Mono<Void> remove(ItemRoot root) {
        return this.remove(root.getIdentifier(), AuthItem.class, (data,clazz)->{
            return AuthItem.builder().itemId(data.id()).build();
        }, authItemMapper::deleteByPrimaryKey);

    }
}
