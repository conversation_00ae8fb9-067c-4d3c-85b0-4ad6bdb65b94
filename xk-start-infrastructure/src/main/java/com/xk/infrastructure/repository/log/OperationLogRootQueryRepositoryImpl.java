package com.xk.infrastructure.repository.log;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.domain.model.log.OperationLogEntity;
import com.xk.domain.repository.log.OperationLogRootQueryRepository;
import com.xk.infrastructure.data.persistence.log.COperationLogMapper;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class OperationLogRootQueryRepositoryImpl implements OperationLogRootQueryRepository {

    private final Converter converter;
    private final COperationLogMapper cOperationLogMapper;


    @Override
    public Flux<OperationLogEntity> searchPager(Pagination pagination) {
        return this.search(pagination, cOperationLogMapper::selectByPage, OperationLogEntity.class, converter::convert);
    }
}
