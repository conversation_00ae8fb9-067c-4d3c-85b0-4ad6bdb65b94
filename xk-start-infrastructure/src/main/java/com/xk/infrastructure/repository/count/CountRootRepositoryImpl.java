package com.xk.infrastructure.repository.count;

import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.domain.model.count.CountEntity;
import com.xk.domain.model.count.CountRoot;
import com.xk.domain.repository.count.CountRootRepository;
import com.xk.infrastructure.cache.dao.count.CountDao;
import com.xk.infrastructure.cache.key.count.CountKey;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Slf4j
@Repository
@RequiredArgsConstructor
public class CountRootRepositoryImpl implements CountRootRepository {

    private final CountDao countDao;

    @Override
    public Mono<Void> save(CountRoot root) {
        return null;
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(CountRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(CountRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(CountRoot root) {
        return Mono.fromCallable(() -> {
            CountEntity countEntity = root.getCountEntity();
            countDao.delKey(CountKey.builder().countBusiTypeEnum(countEntity.getCountBusiType())
                    .key(countEntity.getKey()).build());
            return null;
        });
    }

    @Override
    public Mono<Long> incr(CountRoot root) {
        return Mono.fromCallable(() -> {
            CountEntity entity = root.getCountEntity();
            CountKey countKey = CountKey.builder().countBusiTypeEnum(entity.getCountBusiType())
                    .key(entity.getKey()).build();
            if (entity.getIsExpired()) {
                countKey.setTtl(entity.getExpireTime());
                countKey.setIsExpire(true);
                // //需要过期
                // String value = countDao.getValue(countKey);
                // if (value == null){
                // countKey.setTtl(entity.getExpireTime());
                // countKey.setIsExpire(true);
                // countDao.addValue(countKey, "0");
                // }
            } else {
                countKey.setIsExpire(false);
            }
            return countDao.incrValue(countKey, entity.getIncrement());
        });
    }
}
