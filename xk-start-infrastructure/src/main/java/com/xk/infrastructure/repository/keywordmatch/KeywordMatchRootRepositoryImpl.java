package com.xk.infrastructure.repository.keywordmatch;

import com.myco.mydata.domain.model.Identifier;
import com.xk.domain.model.keywordmatch.KeywordMatchRoot;
import com.xk.domain.repository.keywordmatch.KeywordMatchRootRepository;
import com.xk.infrastructure.data.persistence.keywordmatch.KeywordMatchMapper;
import com.xk.infrastructure.data.po.keywordmatch.TKeywordMatch;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/24 13:45
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class KeywordMatchRootRepositoryImpl implements KeywordMatchRootRepository {

    private final KeywordMatchMapper keywordMatchMapper;
    private final Converter converter;

    @Override
    public Mono<Void> save(KeywordMatchRoot root) {
        return this.save(root.getKeywordMatchEntity(), TKeywordMatch.class, converter::convert, keywordMatchMapper::insertSelective);
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(KeywordMatchRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(KeywordMatchRoot root) {
        return this.update(root.getKeywordMatchEntity(), TKeywordMatch.class, this.converter::convert, keywordMatchMapper::updateByPrimaryKeySelective);
    }

    @Override
    public Mono<Void> remove(KeywordMatchRoot root) {
        return this.remove(root.getIdentifier(), TKeywordMatch.class, (data,clazz)->{
            return TKeywordMatch.builder().busiId(data.getBusiId()).busiType(data.getBusiType().getCode()).build();
        }, keywordMatchMapper::deleteByPrimaryKey);
    }
}
