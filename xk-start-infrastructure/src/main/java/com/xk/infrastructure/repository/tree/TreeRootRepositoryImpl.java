package com.xk.infrastructure.repository.tree;

import com.myco.mydata.domain.model.Identifier;
import com.xk.domain.model.tree.TreeNode;
import com.xk.domain.model.tree.TreeRoot;
import com.xk.domain.repository.tree.TreeRootRepository;
import com.xk.infrastructure.data.persistence.tree.TTreeNodeMapper;
import com.xk.infrastructure.data.po.tree.TTreeNode;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class TreeRootRepositoryImpl implements TreeRootRepository {

    private final TTreeNodeMapper tTreeNodeMapper;
    private final Converter converter;

    @Override
    public Mono<Void> save(TreeRoot root) {
        return this.save(root.getNode(), TTreeNode.class, this.converter::convert, this.tTreeNodeMapper::insertSelective);
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(TreeRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(TreeRoot root) {
        return this.update(root.getNode(), TTreeNode.class, this.converter::convert, this.tTreeNodeMapper::updateByPrimaryKeySelective);
    }

    @Override
    public Mono<Void> remove(TreeRoot root) {
        return this.remove(TreeNode.builder().nodeId(root.getIdentifier().id()).build(), TTreeNode.class, this.converter::convert, this.tTreeNodeMapper::deleteByPrimaryKey);
    }

}
