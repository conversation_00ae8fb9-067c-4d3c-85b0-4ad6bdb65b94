package com.xk.infrastructure.convertor.sensitive;

import com.myco.mydata.commons.util.StringUtils;
import com.xk.domain.model.sensitive.enums.SensitiveStatusEnum;

/**
 * <AUTHOR>
 */
public class SensitiveStatusEnumConvertor {

    public static SensitiveStatusEnum map(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return SensitiveStatusEnum.valueOf(name);
    }

    public static String map(SensitiveStatusEnum sensitiveStatusEnum) {
        if (sensitiveStatusEnum == null) {
            return null;
        }
        return sensitiveStatusEnum.name();
    }

}
