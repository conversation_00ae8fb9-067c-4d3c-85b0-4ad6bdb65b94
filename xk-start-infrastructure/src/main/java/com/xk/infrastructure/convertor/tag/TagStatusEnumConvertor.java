package com.xk.infrastructure.convertor.tag;

import com.xk.domain.model.tag.TagStatusEnum;

/**
 * <AUTHOR>
 */
public class TagStatusEnumConvertor {

    public static TagStatusEnum map(Integer value) {
        if (value == null) {
            return null;
        }
        return TagStatusEnum.getByStatus(value);
    }

    public static Integer map(TagStatusEnum tagStatusEnum) {
        if (tagStatusEnum == null) {
            return null;
        }
        return tagStatusEnum.getStatus();
    }

}
