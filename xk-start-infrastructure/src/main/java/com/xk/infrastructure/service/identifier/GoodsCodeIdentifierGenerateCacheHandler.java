package com.xk.infrastructure.service.identifier;

import com.myco.mydata.commons.util.StringUtils;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateHandler;
import com.xk.infrastructure.cache.dao.sequence.GoodsCodeRandomLetterDao;
import com.xk.infrastructure.cache.dao.sequence.GoodsCodeSequenceDao;
import com.xk.infrastructure.cache.key.sequence.GoodsCodeRandomLetterKey;
import com.xk.infrastructure.cache.key.sequence.GoodsCodeSequenceKey;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;
import java.util.Random;

/**
 * @description: GoodsCodeIdentifierGenerateCacheHandler
 * <AUTHOR>
 * @Date 2024/10/30 10:58
 */
//@Repository
@RequiredArgsConstructor
public class GoodsCodeIdentifierGenerateCacheHandler implements IdentifierGenerateHandler {

    private final GoodsCodeSequenceDao goodsCodeSequenceDao;
    private final GoodsCodeRandomLetterDao goodsCodeRandomLetterDao;

    private final static String uppercaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    @Override
    public Serializable generateIdentifier(IdentifierRoot identifierRoot) {
        //查询随机值
        GoodsCodeRandomLetterKey letterKey = new GoodsCodeRandomLetterKey();
        String value = goodsCodeRandomLetterDao.getValue(letterKey);
        if (StringUtils.isBlank(value)){
            //随机一个
            value = getRandomLetter();
            goodsCodeRandomLetterDao.addValue(letterKey, value);
        }

        String gameLetter = "";
        if (identifierRoot.getIds() != null && identifierRoot.getIds().length > 0){
            gameLetter = identifierRoot.getIds()[0].toString();
        }

        while (true){
            //递增
            Long incrValue = goodsCodeSequenceDao.incrValue(GoodsCodeSequenceKey.getKeyObject(identifierRoot, value));
            if (incrValue > 9999){
                value = getRandomLetter();
                goodsCodeRandomLetterDao.addValue(letterKey, value);
            }else {
                return gameLetter + value + String.format("%04d", incrValue);
            }
        }
    }

    @Override
    public IdentifierGenerateEnum getIdentifierGenerateType() {
        return null;
    }


    private String getRandomLetter(){
        // 创建一个Random对象
        Random random = new Random();
        // 创建一个StringBuilder对象来构建最终的字符串
        StringBuilder randomLetters = new StringBuilder(3);
        // 生成三个随机的大写字母
        for (int i = 0; i < 3; i++) {
            // 生成一个随机索引
            int index = random.nextInt(uppercaseLetters.length());
            // 将随机字母添加到StringBuilder中
            randomLetters.append(uppercaseLetters.charAt(index));
        }
        return randomLetters.toString();
    }
}
