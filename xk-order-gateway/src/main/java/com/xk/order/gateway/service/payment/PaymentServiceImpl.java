package com.xk.order.gateway.service.payment;

import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.model.payment.entity.RefundEntity;
import com.xk.order.domain.service.payment.PaymentService;
import com.xk.tp.interfaces.dto.pay.RefundOrderDto;
import com.xk.tp.interfaces.service.pay.PayService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class PaymentServiceImpl implements PaymentService {

    private final PayService payService;

    @Override
    public Mono<Boolean> refundPayment(Mono<PaymentEntity> orderNoMono) {
        return orderNoMono.flatMap(paymentEntity -> {
            return payService.refundOrder(Mono.just(RefundOrderDto.builder()
                    .orderNo(paymentEntity.getOrderNo()).payNo(paymentEntity.getPayNo()).build()));
        });
    }
}
