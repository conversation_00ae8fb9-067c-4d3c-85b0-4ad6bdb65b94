package com.xk.tp.application.action.query.auth;

import com.myco.mydata.application.handler.query.IActionQuery;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuthNotifyThirdPlatformQuery implements IActionQuery {
    private String authFlowId;

    private Long userId;
}
