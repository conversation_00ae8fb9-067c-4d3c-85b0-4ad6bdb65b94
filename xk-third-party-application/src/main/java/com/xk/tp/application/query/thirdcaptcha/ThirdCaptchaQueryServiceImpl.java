package com.xk.tp.application.query.thirdcaptcha;

import java.util.List;

import org.springframework.stereotype.Service;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.tp.application.commons.XkThirdPartyApplicationErrorEnum;
import com.xk.tp.application.support.XkThirdPartyApplicationException;
import com.xk.tp.domain.model.access.AccessAccountExtValObj;
import com.xk.tp.domain.model.access.AccessEntity;
import com.xk.tp.domain.model.thirdcaptcha.ThirdCaptchaEntity;
import com.xk.tp.domain.model.thirdcaptcha.ThirdCaptchaIdentifier;
import com.xk.tp.domain.model.thirdcaptcha.ThirdCaptchaRoot;
import com.xk.tp.domain.model.thirdcaptcha.ThirdCheckCaptchaEntity;
import com.xk.tp.domain.service.access.AccessRootService;
import com.xk.tp.domain.service.thirdcaptcha.ThirdCaptchaRootService;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import com.xk.tp.enums.access.AccessPlatformTypeEnum;
import com.xk.tp.enums.access.BusinessGroupEnum;
import com.xk.tp.enums.thirdcaptcha.CaptchaTypeEnum;
import com.xk.tp.interfaces.dto.req.thirdcaptcha.ThirdCheckCaptchaReqDto;
import com.xk.tp.interfaces.query.thirdcaptcha.ThirdCaptchaQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/1 17:30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdCaptchaQueryServiceImpl implements ThirdCaptchaQueryService {

    private final ThirdCaptchaRootService thirdCaptchaRootService;

    private final AccessRootService accessRootService;

    /**
     * 验证码
     *
     * @param dtoMono
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> checkCaptcha(Mono<ThirdCheckCaptchaReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {

            AccessEntity accessEntity = AccessEntity.builder()
                    .businessType(BusinessTypeEnum.getByValue(dto.getBusinessType()))
                    .businessGroup(BusinessGroupEnum.CAPTCHA)
                    .status(AccessDomainStatusEnum.ENABLED.getStatus())
                    .build();
            return accessRootService.findAccessAccountByGroup(accessEntity)
                    .collectList()
                    .map(List::getFirst)
                    .flatMap(accessAccountEntity -> {
                        //查询配置
                        return accessRootService.findAccessAccountExtByAccessAccountExt(AccessAccountExtValObj.builder()
                                        .accessAccountId(accessAccountEntity.getAccessAccountId()).build())
                                .flatMap(ext -> {
                                    ThirdCaptchaEntity captchaEntity = ThirdCaptchaEntity.builder()
                                            .accessAccountId(accessAccountEntity.getAccessAccountId())
                                            .channelType(accessAccountEntity.getChannelType())
                                            .platformType(AccessPlatformTypeEnum.getEnumByValue(
                                                    accessAccountEntity.getPlatformType()))
                                            .config(ext)
                                            .build();
                                    ThirdCheckCaptchaEntity thirdCheckCaptcha = ThirdCheckCaptchaEntity.builder()
                                            .captchaType(CaptchaTypeEnum.valueOf(dto.getCaptchaType()))
                                            .ticket(dto.getTicket())
                                            .userIp(dto.getUserIp())
                                            .randstr(dto.getRandstr())
                                            .build();
                                    ThirdCaptchaRoot captchaRoot = ThirdCaptchaRoot.builder()
                                            .identifier(ThirdCaptchaIdentifier.builder()
                                                    .accessAccountId(accessAccountEntity.getAccessAccountId())
                                                    .build())
                                            .thirdValidateCode(captchaEntity)
                                            .thirdCheckCaptcha(thirdCheckCaptcha)
                                            .build();

                                    return thirdCaptchaRootService.checkCaptcha(captchaRoot)
                                            .flatMap(status -> {
                                                if (status) {
                                                    return Mono.empty();
                                                }
                                                return Mono.error(new XkThirdPartyApplicationException(
                                                        XkThirdPartyApplicationErrorEnum.CAPTCHA_FAIL));
                                            });

                                });
                    });

        });
    }
}
