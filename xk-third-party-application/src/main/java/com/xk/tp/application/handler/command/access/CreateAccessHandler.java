package com.xk.tp.application.handler.command.access;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.tp.application.action.command.access.CreateAccessCommand;
import com.xk.tp.domain.model.access.AccessEntity;
import com.xk.tp.domain.model.access.AccessIdentifier;
import com.xk.tp.domain.model.access.AccessRoot;
import com.xk.tp.domain.repository.access.AccessRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Component
@RequiredArgsConstructor
public class CreateAccessHandler implements IActionCommandHandler<CreateAccessCommand, Void> {

    private final AccessRootRepository accessRootRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateAccessCommand> command) {
        return this.execute(command, AccessEntity.class,
                this.converter::convert,
                (accessEntity) -> AccessRoot.builder()
                        .identifier(AccessIdentifier.builder().accessId(accessEntity.getAccessId()).build())
                        .accessEntity(accessEntity)
                        .build(),
                accessRootRepository::save
        );
    }
}
