package com.xk.tp.application.query.access;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.tp.application.action.query.access.*;
import com.xk.tp.enums.access.*;
import com.xk.tp.infrastructure.commons.util.BeanUtil;
import com.xk.tp.interfaces.dto.req.access.*;
import com.xk.tp.interfaces.dto.res.access.*;
import com.xk.tp.interfaces.query.access.AccessQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> date 2024/07/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccessQueryServiceImpl implements AccessQueryService {

    private final ActionQueryDispatcher<IActionQuery> dispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    @BusiCode
    public Mono<Pagination> searchAccess(Mono<AccessPagerReqDto> dtoMono) {
        return dispatcher.executeQuery(dtoMono, AccessSearchQuery.class, Pagination.class);
    }

    @Override
    public Mono<List<AccessResDto>> searchAccessList(Mono<AccessListReqDto> dtoMono) {
        return queryManyDispatcher
                .executeQuery(dtoMono, AccessSearchListQuery.class, AccessResDto.class)
                .collectList();
    }

    @Override
    public Mono<List<AccessResDto>> searchAccessListByNoLogin(
            Mono<AccessListNoLoginReqDto> dtoMono) {
        return queryManyDispatcher
                .executeQuery(dtoMono, AccessByGroupSearchQuery.class, AccessResDto.class)
                .collectList();
    }

    @Override
    @BusiCode
    public Mono<AccessResDto> searchAccessDetail(Mono<AccessIdReqDto> dtoMono) {
        return dispatcher.executeQuery(dtoMono, AccessInfoQuery.class, AccessResDto.class);
    }

    @Override
    @BusiCode
    public Mono<AccessExtResDto> searchAccessExtDetail(Mono<AccessExtIdReqDto> dto) {
        return dispatcher.executeQuery(dto, AccessExtInfoQuery.class, AccessExtResDto.class);
    }

    @Override
    @BusiCode
    public Mono<AccessResDto> searchAccessDetailByNoLogin(Mono<AccessIdByNoLoginReqDto> dto) {
        return dispatcher.executeQuery(dto, AccessInfoQuery.class, AccessResDto.class);
    }

    @Override
    @BusiCode
    public Mono<List<AccessExtResDto>> searchAccessExtList(Mono<AccessExtReqDto> dtoMono) {
        return queryManyDispatcher
                .executeQuery(dtoMono, AccessExtSearchListQuery.class, AccessExtResDto.class)
                .collectList().flatMap(list -> {
                    // 针对游戏配置要有过滤处理
                    if (list.isEmpty()) {
                        return Mono.just(list);
                    }
                    return dtoMono.flatMap(accessExtReqDto -> {
                        return queryDispatcher
                                .executeQuery(
                                        Mono.just(AccessInfoQuery.builder()
                                                .accessId(accessExtReqDto.getAccessId()).build()),
                                        AccessInfoQuery.class, AccessResDto.class)
                                .flatMap(accessResDto -> {
                                    if (Objects.equals(accessResDto.getBusinessGroup(),
                                            BusinessGroupEnum.GAME_API.getCode())) {
                                        List<String> strings = Arrays.asList(
                                                GameAccessConfigEnum.upDownStatus.getCode(),
                                                GameAccessConfigEnum.initFee.getCode(),
                                                GameAccessConfigEnum.renewFee.getCode(),
                                                GameAccessConfigEnum.renewMonth.getCode());
                                        List<AccessExtResDto> extResDtos = list.stream()
                                                .filter(accessExtResDto -> !strings
                                                        .contains(accessExtResDto.getFieldKey()))
                                                .toList();
                                        return Mono.just(extResDtos);
                                    }
                                    return Mono.just(list);
                                });
                    });
                });
    }

    @Override
    @BusiCode
    public Mono<List<AccessAccountResDto>> searchAccessAccountList(
            Mono<AccessAccountReqDto> dtoMono) {
        return queryManyDispatcher.executeQuery(dtoMono, AccessAccountSearchListQuery.class,
                AccessAccountResDto.class).flatMap(accessAccountResDto -> {
                    // 查询密钥配置
                    AccessAccountExtReqDto extReqDto = AccessAccountExtReqDto.builder()
                            .accessAccountId(accessAccountResDto.getAccessAccountId()).build();
                    return queryManyDispatcher
                            .executeQuery(Mono.just(extReqDto), AccessAccountExtInfoListQuery.class,
                                    AccessAccountExtResDto.class)
                            .collectList().map(accessAccountExtResDtos -> {
                                AccessAccountResDto.AccessAccountExtObjResDto extObjResDto =
                                        builderAccessAccountExtToResDto(accessAccountExtResDtos);
                                accessAccountResDto.setAccountExtObj(extObjResDto);
                                return accessAccountResDto;
                            });
                }).collectList();
    }


    @BusiCode
    @Override
    public Mono<List<AccessInterfaceAuthInfoResDto>> queryAccessInterfaceAuthInfoByCorp(
            Mono<QueryAccessInterfaceAuthInfoReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            AccessInterfaceAuthInfoByCorpQuery query =
                    AccessInterfaceAuthInfoByCorpQuery.builder().corpId(dto.getCorpId()).build();
            return queryManyDispatcher.process(Mono.just(query),
                    AccessInterfaceAuthInfoByCorpQuery.class, AccessInterfaceAuthInfoResDto.class)
                    .collectList();
        });
    }

    @Override
    @BusiCode
    public Mono<List<AccessInterfaceAuthInfoResDto>> queryAccessInterfaceAuthInfoByCorpNoLogin(
            Mono<QueryAccessInterfaceAuthInfoNoLoginReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            AccessInterfaceAuthInfoByCorpQuery query =
                    AccessInterfaceAuthInfoByCorpQuery.builder().corpId(dto.getCorpId()).build();
            return queryManyDispatcher.process(Mono.just(query),
                    AccessInterfaceAuthInfoByCorpQuery.class, AccessInterfaceAuthInfoResDto.class)
                    .collectList();
        });
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchAccessTpGame(Mono<AccessTpGamePagerReqDto> dtoMono) {
        return this.queryDispatcher.executeQuery(dtoMono, AccessTpGameSearchQuery.class,
                Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<List<QueryTagRelationRspDto>> queryTagRelation(
            Mono<TagRelationListQueryReqDto> dtoMono) {
        return dtoMono.flatMapMany(dto -> {
            AccessTagRelationByTagIdQuery relationQuery =
                    AccessTagRelationByTagIdQuery.builder().tagId(dto.getTagId()).build();

            return this.queryManyDispatcher
                    .process(Mono.just(relationQuery), AccessTagRelationByTagIdQuery.class,
                            TagRelationRspDto.class)
                    .filter(tagRelationRspDto -> Objects.equals(tagRelationRspDto.getAccessId(),
                            dto.getAccessId())) // 过滤 accessId
                    .flatMap(tagRelationRspDto -> {
                        QueryTagRelationRspDto queryTagRelationRspDto =
                                new QueryTagRelationRspDto();
                        queryTagRelationRspDto.setTagId(tagRelationRspDto.getTagId());
                        queryTagRelationRspDto.setAccessId(tagRelationRspDto.getAccessId());
                        AccessTagInfoQuery tagInfoQuery =
                                AccessTagInfoQuery.builder().tpTagId(tagRelationRspDto.getTpTagId())
                                        .accessId(dto.getAccessId()).build();

                        return this.queryDispatcher.process(Mono.just(tagInfoQuery),
                                AccessTagInfoQuery.class, AccessTagRspDto.class)
                                .map(accessTagRspDto -> {

                                    queryTagRelationRspDto
                                            .setTpTagName(accessTagRspDto.getTpTagName());
                                    queryTagRelationRspDto.setTpTagId(accessTagRspDto.getTpTagId());
                                    return queryTagRelationRspDto;
                                });
                    });
        }).collectList();
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchAccessTag(Mono<AccessTagPagerReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> this.queryDispatcher
                .executeQuery(Mono.just(dto), AccessTagSearchQuery.class, Pagination.class)
                .flatMap(pagination -> {
                    List<AccessTagRspDto> accessTagRspDtos = pagination.getRecords();

                    return Flux.fromIterable(accessTagRspDtos).flatMap(accessTagRspDto -> {
                        AccessTagPagerRspDto accessTagPagerRspDto = new AccessTagPagerRspDto();
                        accessTagPagerRspDto.setTpTagName(accessTagRspDto.getTpTagName());
                        accessTagPagerRspDto.setTpTagId(accessTagRspDto.getTpTagId());
                        accessTagPagerRspDto.setAccessId(accessTagRspDto.getAccessId());
                        accessTagPagerRspDto.setTagId(dto.getTagId());
                        accessTagPagerRspDto.setRelationStatus(
                                AccessTagRelationStatusEnum.NON_RELATION.getCode());

                        AccessTagRelationByIdQuery relationByIdQuery =
                                AccessTagRelationByIdQuery.builder().tagId(dto.getTagId())
                                        .accessId(accessTagRspDto.getAccessId())
                                        .tpTagId(accessTagRspDto.getTpTagId()).build();

                        return this.queryDispatcher
                                .process(Mono.just(relationByIdQuery),
                                        AccessTagRelationByIdQuery.class, TagRelationRspDto.class)
                                .hasElement().map(hasElement -> {
                                    if (hasElement) {
                                        accessTagPagerRspDto.setRelationStatus(
                                                AccessTagRelationStatusEnum.RELATION.getCode());
                                    }
                                    return accessTagPagerRspDto;
                                });
                    }).collectList().map(accessTagPagerRspDtos -> {
                        pagination.setRecords(accessTagPagerRspDtos);
                        return pagination;
                    });
                }));
    }


    private AccessAccountResDto.AccessAccountExtObjResDto builderAccessAccountExtToResDto(
            List<AccessAccountExtResDto> accessAccountExtResDtos) {
        return BeanUtil.mapTo(
                accessAccountExtResDtos.stream()
                        .collect(Collectors.toMap(AccessAccountExtResDto::getFieldKey,
                                AccessAccountExtResDto::getFiledValue)),
                AccessAccountResDto.AccessAccountExtObjResDto.class);
    }

    /**
     * 根据通道和三方标签查询关联关系
     *
     * @param dtoMono dtoMono
     * @return Mono<List < TagRelationRspDto>>
     */
    @Override
    @BusiCode
    public Mono<List<TagRelationRspDto>> queryTagRelationByAccessTpTag(
            Mono<TagRelationByAccessTpTagReqDto> dtoMono) {
        return queryManyDispatcher.executeQuery(dtoMono, AccessTagRelationByAccessTpTagQuery.class,
                TagRelationRspDto.class).collectList();
    }

    /**
     * 根据id查询通道游戏
     *
     * @param dtoMono dtoMono
     * @return Mono<AccessGameResDto>
     */
    @Override
    @BusiCode
    public Mono<AccessGameResDto> queryAccessGameById(Mono<AccessGameIdReqDto> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, AccessGameInfoByIdQuery.class,
                AccessGameResDto.class);
    }

    /**
     * 根据id查询通道游戏
     *
     * @param dtoMono dtoMono
     * @return Mono<AccessGameResDto>
     */
    @Override
    @BusiCode
    public Mono<AccessGameResDto> queryAccessGameByDdGameId(Mono<DdGameIdReqDto> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, AccessGameByGameIdQuery.class,
                AccessGameResDto.class);
    }

    @BusiCode
    @Override
    public Mono<AccessExtResDto> queryAccessExtInfo(Mono<AccessExtNoSessionReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            AccessExtInfoQuery query = AccessExtInfoQuery.builder().accessId(dto.getAccessId())
                    .fieldKey(dto.getFieldKey()).build();
            return this.queryDispatcher.process(Mono.just(query), AccessExtInfoQuery.class,
                    AccessExtResDto.class);
        });
    }

    @BusiCode
    @Override
    public Mono<List<AccessInterfaceAuthInfoResDto>> queryAccessInterfaceAuthInfoByAccessId(
            Mono<AccessIdReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            AccessInterfaceAuthInfoByAccessIdQuery query = AccessInterfaceAuthInfoByAccessIdQuery
                    .builder().accessId(dto.getAccessId()).build();
            return queryManyDispatcher
                    .process(Mono.just(query), AccessInterfaceAuthInfoByAccessIdQuery.class,
                            AccessInterfaceAuthInfoResDto.class)
                    .collectList();
        });
    }

    @BusiCode
    @Override
    public Mono<List<AccessResDto>> searchBlackAccessList(Mono<RequireSessionDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            AccessSearchListQuery accessSearchListQuery = AccessSearchListQuery.builder()
                    .businessGroup(BusinessGroupEnum.BLACK_ACCOUNT.getCode())
                    .businessType(BusinessTypeEnum.BLACK_LIST.getValue()).build();
            return this.queryManyDispatcher.process(Mono.just(accessSearchListQuery),
                    AccessSearchListQuery.class, AccessResDto.class).collectList();
        });
    }

    @Override
    @BusiCode
    public Mono<List<AccessAccountResDto>> searchAccessAndAccountList(
            Mono<AccessAccountListReqDto> dtoMono) {
        return dtoMono.flatMap(accessAccountListReqDto -> {
            AccessByGroupSearchQuery searchListQuery = AccessByGroupSearchQuery.builder()
                    .businessGroup(accessAccountListReqDto.getBusinessGroup())
                    .businessType(accessAccountListReqDto.getBusinessType())
                    .status(AccessDomainStatusEnum.ENABLED.getStatus()).build();
            return queryManyDispatcher.process(Mono.just(searchListQuery),
                    AccessByGroupSearchQuery.class, AccessResDto.class).flatMap(accessResDto -> {
                        // 查询账号
                        AccessAccountSearchListQuery accountSearchListQuery =
                                AccessAccountSearchListQuery.builder()
                                        .accessId(accessResDto.getAccessId())
                                        .status(AccessDomainStatusEnum.ENABLED.getStatus()).build();
                        return queryManyDispatcher.process(Mono.just(accountSearchListQuery),
                                AccessAccountSearchListQuery.class, AccessAccountResDto.class)
                                .map(accessAccountResDto -> {
                                    accessAccountResDto
                                            .setPlatformType(accessResDto.getPlatformType());
                                    accessAccountResDto
                                            .setChannelType(accessResDto.getChannelType());
                                    accessAccountResDto
                                            .setBusinessType(accessResDto.getBusinessType());
                                    accessAccountResDto
                                            .setBusinessGroup(accessResDto.getBusinessGroup());
                                    return accessAccountResDto;
                                });
                    }).collectList();
        });
    }
}
