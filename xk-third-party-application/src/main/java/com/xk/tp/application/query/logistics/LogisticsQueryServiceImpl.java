package com.xk.tp.application.query.logistics;

import java.util.List;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.tp.application.action.query.logistics.LogisticsDetailQuery;
import com.xk.tp.application.convertor.access.AccessExtResDtoToGameAccessExtResDtoConvertor;
import com.xk.tp.interfaces.dto.req.logistics.LogisticsReqDto;
import com.xk.tp.interfaces.dto.res.logistics.LogisticsDetailRspDto;
import com.xk.tp.interfaces.query.logistics.LogisticsQueryService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> date 2024/07/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsQueryServiceImpl implements LogisticsQueryService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;
    private final AccessExtResDtoToGameAccessExtResDtoConvertor accessExtResDtoToGameAccessExtResDtoConvertor;
    private final Converter converter;

    @BusiCode
    @Override
    public Mono<List<LogisticsDetailRspDto>> detail(Mono<LogisticsReqDto> dtoMono) {
        return queryManyDispatcher
                .executeQuery(dtoMono, LogisticsDetailQuery.class, LogisticsDetailRspDto.class)
                .collectList();
    }
}
