package com.xk.tp.application.handler.query.logistics;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.xk.tp.application.action.query.logistics.LogisticsDetailQuery;
import com.xk.tp.domain.model.logistics.LogisticsCorpEntity;
import com.xk.tp.domain.model.logistics.LogisticsRoot;
import com.xk.tp.domain.model.logistics.id.LogisticsIdentifier;
import com.xk.tp.domain.service.logistics.LogisticsService;
import com.xk.tp.interfaces.dto.res.logistics.LogisticsDetailRspDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class LogisticsDetailQueryHandler
        implements IActionQueryManyHandler<LogisticsDetailQuery, LogisticsDetailRspDto> {

    private final LogisticsService logisticsService;

    @Override
    public Flux<LogisticsDetailRspDto> execute(Mono<LogisticsDetailQuery> queryMono) {
        return queryMono
                .flatMapMany(
                        logisticsDetailQuery -> logisticsService
                                .detail(Mono.just(LogisticsRoot.builder()
                                        .identifier(LogisticsIdentifier.builder()
                                                .logisticsNo(logisticsDetailQuery.getLogisticsNo())
                                                .logisticsCorpName(logisticsDetailQuery
                                                        .getLogisticsCorpName())
                                                .build())
                                        .logisticsCorpEntity(LogisticsCorpEntity.builder()
                                                .logisticsNo(logisticsDetailQuery.getLogisticsNo())
                                                .logisticsCorpName(
                                                        logisticsDetailQuery.getLogisticsCorpName())
                                                .receivingMobile(
                                                        logisticsDetailQuery.getReceivingMobile())
                                                .build())
                                        .build()))
                                .flatMap(logisticsDetailValObj -> Mono.just(LogisticsDetailRspDto
                                        .builder()
                                        .trackDescribe(logisticsDetailValObj.getTrackDescribe())
                                        .trackTime(logisticsDetailValObj.getTrackTime()).build())));
    }
}

