package com.xk.tp.application.action.query.logistics;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.tp.interfaces.dto.req.logistics.LogisticsReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = LogisticsReqDto.class)})
public class LogisticsDetailQuery implements IActionQueryMany {

    /**
     * 物流公司名称
     */
    private String logisticsCorpName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 收货人手机号
     */
    private String receivingMobile;

}
