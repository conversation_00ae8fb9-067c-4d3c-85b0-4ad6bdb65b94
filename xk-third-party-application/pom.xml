<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.tp</groupId>
        <artifactId>xk-third-party</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-third-party-application</artifactId>
    <packaging>jar</packaging>
    <name>xk-third-party-application</name>
    <description>xk-third-party-application</description>
    <properties>
        <mapstruct-plus.mapperPackage>com.xk.tp.application.convertor.auto</mapstruct-plus.mapperPackage>
        <mapstruct-plus.adapterPackage>com.xk.tp.application.convertor.adapter</mapstruct-plus.adapterPackage>
        <mapstruct-plus.autoConfigPackage>com.xk.tp.application.config</mapstruct-plus.autoConfigPackage>
        <mapstruct-plus.adapterClassName>XkThirdPartyApplicationConverterMapperAdapter</mapstruct-plus.adapterClassName>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.tp</groupId>
            <artifactId>xk-third-party-domain-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.tp</groupId>
            <artifactId>xk-third-party-interfaces</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.tp</groupId>
            <artifactId>xk-third-party-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.tp</groupId>
            <artifactId>xk-third-party-infrastructure</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
