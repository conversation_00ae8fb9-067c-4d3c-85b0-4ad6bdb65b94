package com.xk.message.server;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Lazy;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.event.message.MessageNoticeEvent;

@SpringBootTest(classes = XkMessageServer.class)
class XkMessageServerTests {

    @Autowired
    @Lazy
    private EventRootService eventRootService;

    @Test
    void contextLoads() {}

    @Test
    public void testStationMessage() {
        MessageNoticeEvent goodsNoticeEvent = MessageNoticeEvent.builder()
                .identifier(EventRoot.getCommonsDomainEventIdentifier(MessageNoticeEvent.class))
                .receiverCode(865L)
                .busiId("153766788").platformType(PlatformTypeEnum.PC_BOSS_OMS.getValue())
                .businessType(BusinessTypeEnum.XING_KA.getValue()).msgBusiScene(5)
                .receiverPhone("***********").params(List.of("123456")).build();
        EventRoot eventRoot =
                EventRoot.builder().isQueue(false).domainEvent(goodsNoticeEvent).build();
        eventRootService.publisheByMono(eventRoot).block();
    }
}
