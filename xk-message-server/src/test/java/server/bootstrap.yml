server:
  port: 8180
spring:
  profiles:
    active: local
  application:
    name: xkMessage
  zookeeper:
    enabled: false
    connectionString: 127.0.0.1:2181
  devtools:
    livereload:
      port: 35731
  cloud:
    loadbalancer:
      cache:
        enabled: true
        ttl: 90
        capacity: 3000
    discovery:
      enabled: true
    nacos:
      config:
        enabled: true
        namespace: ${spring.cloud.nacos.discovery.namespace}
        #group: ${spring.application.name}
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        import-check:
          enabled: false
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        #group: ${spring.application.name}
        group: DEFAULT_GROUP
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        namespace: ${spring.profiles.active}
        enabled: true

management:
  endpoints:
    jmx:
      exposure:
        include: '*'
    web:
      exposure:
        include: '*'
        exclude: configprops
  info:
    env:
      enabled: true

---
spring:
  config:
    activate:
      on-profile: pro
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
        encode: utf-8
        username: 'nacos'
        password: 'nacos'
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: pro
        username: 'nacos'
        password: 'nacos'

---
spring:
  config:
    activate:
      on-profile: dev
  cloud:
    nacos:
      config:
        server-addr: localhost:8848
        encode: utf-8
        username: 'nacos'
        password: 'nacos'
      discovery:
        server-addr: localhost:8848
        username: 'nacos'
        password: 'nacos'
---
spring:
  config:
    activate:
      on-profile: local
  cloud:
    nacos:
      config:
        server-addr: localhost:8848
        encode: utf-8
        username: 'nacos'
        password: 'nacos'
      discovery:
        server-addr: localhost:8848
        username: 'nacos'
        password: 'nacos'