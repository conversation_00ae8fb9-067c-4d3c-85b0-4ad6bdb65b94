package com.xk.message.server.endpoints;

import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.*;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.message.interfaces.dto.req.record.RecordQueryPagerReqDto;
import com.xk.message.interfaces.dto.req.validate.CheckValidateCodeReqDto;
import com.xk.message.interfaces.dto.req.validate.ValidateCodeIdReqDto;
import com.xk.message.interfaces.query.record.MessageRecordQueryService;
import com.xk.message.interfaces.query.validate.ValidateCodeQueryService;

@Configuration(proxyBeanMethods = false)
public class XkMessageQueryRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON;

    static {
        ACCEPT_JSON = RequestPredicates.accept(MediaType.APPLICATION_JSON);
    }

    @Bean
    public RouterFunction<ServerResponse> messageRecordQueryServiceRouter(
            MessageRecordQueryService messageRecordQueryService) {
        return nest(RequestPredicates.path("/message/record/query"), route()
                .POST("/queryMallStationMessageRecord", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, RecordQueryPagerReqDto.class,
                                messageRecordQueryService::queryMallStationMessageRecord))
                .POST("/queryCorpStationMessageRecord", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, RecordQueryPagerReqDto.class,
                                messageRecordQueryService::queryCorpStationMessageRecord))
                .build());
    }

    @Bean
    public RouterFunction<ServerResponse> validateQueryRouter(
            ValidateCodeQueryService validateQueryService) {
        return RouterFunctions.nest(RequestPredicates.path("/message/validate/query"),
                RouterFunctions.route().POST("/getValidateExpireTime", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, ValidateCodeIdReqDto.class,
                                validateQueryService::getValidateExpireTime))
                        .POST("/checkValidateCode", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request,
                                        CheckValidateCodeReqDto.class,
                                        validateQueryService::checkValidateCode))
                        .build());
    }
}
