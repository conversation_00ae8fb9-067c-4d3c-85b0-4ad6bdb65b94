package com.xk.message.server.listener.message;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;
import com.xk.domain.event.message.MessageNoticeEvent;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

/**
 * <AUTHOR>
 */
@ConsumerListener
@RequiredArgsConstructor
public class MessageNoticeListener extends AbstractDispatchMessageListener<MessageNoticeEvent>
        implements MessageListenerConcurrently {

    private final EventRootService eventRootService;

    @Override
    public void doProcessMessage(MessageNoticeEvent event) throws Throwable {
        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(true).build();
        eventRootService.handler(eventRoot);
    }
}
