package com.xk.message.server.listener.sms;

import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;
import com.xk.tp.domain.event.sms.SendSmsResultEvent;

import lombok.RequiredArgsConstructor;

@ConsumerListener
@RequiredArgsConstructor
public class SendSmsResultListener extends AbstractDispatchMessageListener<SendSmsResultEvent>
        implements MessageListenerConcurrently {

    private final EventRootService eventRootService;

    @Override
    public void doProcessMessage(SendSmsResultEvent event) throws Throwable {
        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(true).build();
        eventRootService.handler(eventRoot);
    }
}
