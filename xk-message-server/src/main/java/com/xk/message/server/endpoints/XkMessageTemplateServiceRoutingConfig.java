package com.xk.message.server.endpoints;

import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.message.interfaces.dto.req.template.*;
import com.xk.message.interfaces.service.template.MessageTemplateService;

@Configuration(proxyBeanMethods = false)
public class XkMessageTemplateServiceRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON;

    static {
        ACCEPT_JSON = RequestPredicates.accept(MediaType.APPLICATION_JSON);
    }

    @Bean
    public RouterFunction<ServerResponse> messageTemplateServiceRouter(
            MessageTemplateService messageTemplateService) {
        return nest(RequestPredicates.path("/message/template"), route()
                .POST("/createBusinessMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                CreateBusinessMessageTemplateReqDto.class,
                                messageTemplateService::createBusinessMessageTemplate))
                .POST("/updateBusinessMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                UpdateBusinessMessageTemplateReqDto.class,
                                messageTemplateService::updateBusinessMessageTemplate))
                .POST("/deletedBusinessMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                BusinessMessageTemplateIdentifierReqDto.class,
                                messageTemplateService::deletedBusinessMessageTemplate))
                .POST("/createImMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                CreateImMessageTemplateReqDto.class,
                                messageTemplateService::createImMessageTemplate))
                .POST("/updateImMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                UpdateImMessageTemplateReqDto.class,
                                messageTemplateService::updateImMessageTemplate))
                .POST("/createShortMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                CreateShortMessageTemplateReqDto.class,
                                messageTemplateService::createShortMessageTemplate))
                .POST("/updateShortMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                UpdateShortMessageTemplateReqDto.class,
                                messageTemplateService::updateShortMessageTemplate))
                .POST("/deletedShortMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                DeletedShortMessageTemplateReqDto.class,
                                messageTemplateService::deletedShortMessageTemplate))
                .POST("/createStationMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                CreateStationMessageTemplateReqDto.class,
                                messageTemplateService::createStationMessageTemplate))
                .POST("/updateStationMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                UpdateStationMessageTemplateReqDto.class,
                                messageTemplateService::updateStationMessageTemplate))
                .POST("/commitOpenShortMessage", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                BusinessMessageTemplateIdentifierReqDto.class,
                                messageTemplateService::commitOpenShortMessage))
                .POST("/commitCloseShortMessage", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                BusinessMessageTemplateIdentifierReqDto.class,
                                messageTemplateService::commitCloseShortMessage))
                .POST("/commitOpenStationMessage", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                BusinessMessageTemplateIdentifierReqDto.class,
                                messageTemplateService::commitOpenStationMessage))
                .POST("/commitCloseStationMessage", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                BusinessMessageTemplateIdentifierReqDto.class,
                                messageTemplateService::commitCloseStationMessage))
                .build());
    }
}
