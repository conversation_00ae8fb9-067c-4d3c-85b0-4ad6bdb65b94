package com.xk.message.server.config;

import com.myco.framework.annotation.NodeExecutorScan;
import com.myco.framework.configure.FactoryBeanNodeExecutor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author: killer
 **/
@Configuration(proxyBeanMethods = false)
@ComponentScan({"com.xk.message.server.listener"
        , "com.xk.message.server.schedule"
        , "com.xk.message.server.endpoints"})
@NodeExecutorScan
public class XkMessageServerConfig {

    @Bean(FactoryBeanNodeExecutor.DEFAULT_FACTORY_NODE_EXECUTOR_BEAN)
    @ConditionalOnMissingBean(name = FactoryBeanNodeExecutor.DEFAULT_FACTORY_NODE_EXECUTOR_BEAN)
    public FactoryBeanNodeExecutor defaultFactoryBeanNodeExecutor() {
        return FactoryBeanNodeExecutor.builder()
                .node(FactoryBeanNodeExecutor.FactoryNode.builder().configNode("ncs").localPrefix("application.ncs").build())
                .node(FactoryBeanNodeExecutor.FactoryNode.builder().configNode("os").localPrefix("application.os").build())
                .node(FactoryBeanNodeExecutor.FactoryNode.builder().configNode("jms").localPrefix("application.jms").build())
                .node(FactoryBeanNodeExecutor.FactoryNode.builder().configNode("redis").localPrefix("application.redis").build())
                .node(FactoryBeanNodeExecutor.FactoryNode.builder().configNode("scheduling").localPrefix("application.scheduling").build())
                //.node(FactoryBeanNodeExecutor.FactoryNode.builder().configNode("http").localPrefix("application.http").singleton(true).build())
                .build();
    }

}
