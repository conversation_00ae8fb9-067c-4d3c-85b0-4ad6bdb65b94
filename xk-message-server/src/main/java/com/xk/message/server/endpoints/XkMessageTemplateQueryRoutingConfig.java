package com.xk.message.server.endpoints;

import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.message.interfaces.dto.req.template.BusinessMessageTemplateIdentifierReqDto;
import com.xk.message.interfaces.dto.req.template.BusinessMessageTemplatePagerReqDto;
import com.xk.message.interfaces.dto.req.template.TemplateBySceneReqDto;
import com.xk.message.interfaces.query.template.MessageTemplateQueryService;

@Configuration(proxyBeanMethods = false)
public class XkMessageTemplateQueryRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON;

    static {
        ACCEPT_JSON = RequestPredicates.accept(MediaType.APPLICATION_JSON);
    }

    @Bean
    public RouterFunction<ServerResponse> messageTemplateQueryServiceRouter(
            MessageTemplateQueryService messageTemplateQueryService) {
        return nest(RequestPredicates.path("/message/template/query"), route()
                .POST("/queryPager", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                BusinessMessageTemplatePagerReqDto.class,
                                messageTemplateQueryService::queryPager))
                .POST("/queryById", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                BusinessMessageTemplateIdentifierReqDto.class,
                                messageTemplateQueryService::queryById))
                .POST("/queryShortMessageTemplateList", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                BusinessMessageTemplateIdentifierReqDto.class,
                                messageTemplateQueryService::queryShortMessageTemplateList))
                .POST("/queryImMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                BusinessMessageTemplateIdentifierReqDto.class,
                                messageTemplateQueryService::queryImMessageTemplate))
                .POST("/queryStationMessageTemplate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request,
                                BusinessMessageTemplateIdentifierReqDto.class,
                                messageTemplateQueryService::queryStationMessageTemplate))
                .POST("/queryTemplateByScene", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, TemplateBySceneReqDto.class,
                                messageTemplateQueryService::queryTemplateByScene))

                .build());
    }
}
