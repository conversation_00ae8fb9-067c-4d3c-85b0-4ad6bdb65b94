package com.xk.message.server.support;

import com.xk.message.server.commons.XkMessageServerErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.wrapper.ServerWrapperThrowable;

/**
 * @author: killer
 **/
public class XkMessageServerException extends ServerWrapperThrowable {

    public XkMessageServerException(XkMessageServerErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

    public XkMessageServerException(XkMessageServerErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }
}
