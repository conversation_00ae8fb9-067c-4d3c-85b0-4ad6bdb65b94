package com.xk.message.server.endpoints;

import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.*;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.message.interfaces.dto.req.record.RecordReadPageReqDto;
import com.xk.message.interfaces.dto.req.validate.SendValidateCodeReqDto;
import com.xk.message.interfaces.dto.req.validate.ValidateCodeCountIdReqDto;
import com.xk.message.interfaces.service.record.MessageRecordService;
import com.xk.message.interfaces.service.validate.ValidateCodeService;

@Configuration(proxyBeanMethods = false)
public class XkMessageServiceRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON;

    static {
        ACCEPT_JSON = RequestPredicates.accept(MediaType.APPLICATION_JSON);
    }

    @Bean
    public RouterFunction<ServerResponse> validateServiceRouter(
            ValidateCodeService validateService) {
        return RouterFunctions.nest(RequestPredicates.path("/message/validate"),
                RouterFunctions.route().POST("/sendValidateCode", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, SendValidateCodeReqDto.class,
                            validateService::sendValidateCode);
                }).POST("/resetValidateCount", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, ValidateCodeCountIdReqDto.class,
                            validateService::resetValidateCount);
                }).build());
    }

    @Bean
    public RouterFunction<ServerResponse> messageRecordServiceRouter(
            MessageRecordService messageRecordService) {
        return nest(
                RequestPredicates.path("/message/record"), route()
                        .POST("/read/page", ACCEPT_JSON, request -> WebFluxHandler.handler(request,
                                RecordReadPageReqDto.class, messageRecordService::commitPageRead))
                        .build());
    }
}
