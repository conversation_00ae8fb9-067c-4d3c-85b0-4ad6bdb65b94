package com.xk.domain.event.order;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_ORDER,
        domainName = DomainNameEnum.ORDER)
public abstract class AbstractOrderDomainEvent extends AbstractCommonsDomainEvent {

    public AbstractOrderDomainEvent(@NonNull Long identifier, Map<String, Object> context) {
        super(identifier, context);

    }

}
