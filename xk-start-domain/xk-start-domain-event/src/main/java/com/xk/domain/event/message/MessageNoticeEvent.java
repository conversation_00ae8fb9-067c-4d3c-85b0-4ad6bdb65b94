package com.xk.domain.event.message;

import java.util.List;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_MESSAGE,
        domainName = DomainNameEnum.MESSAGE)
public class MessageNoticeEvent extends AbstractCommonsDomainEvent {

    private final String busiId;

    private final Integer msgBusiScene;

    private final Integer platformType;

    private final Integer businessType;

    private final Long receiverCode;

    private final String receiverPhone;

    private final Long corpInfoId;

    private final List<String> params;

    @Builder
    public MessageNoticeEvent(@NonNull Long identifier, Map<String, Object> context, String busiId,
                              Integer msgBusiScene, Integer platformType, Integer businessType, Long receiverCode,
                              String receiverPhone, Long corpInfoId, List<String> params) {
        super(identifier, context);

        this.busiId = busiId;
        this.msgBusiScene = msgBusiScene;
        this.platformType = platformType;
        this.businessType = businessType;
        this.receiverCode = receiverCode;
        this.receiverPhone = receiverPhone;
        this.corpInfoId = corpInfoId;
        this.params = params;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }


}
