package com.xk.domain.event.auth;

import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * 支付完成事件
 */
@Getter
@EventDefinition(appName = AppNameEnum.YD_AUTH, domainName = DomainNameEnum.AUTH)
public class AuthSecureEvent extends AbstractCommonsDomainEvent {

    private final String uri;

    private final String sessionId;


    @Builder
    public AuthSecureEvent(@NonNull Long identifier, Map<String, Object> context, String uri,
            String sessionId) {
        super(identifier, context);
        this.uri = uri;
        this.sessionId = sessionId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
