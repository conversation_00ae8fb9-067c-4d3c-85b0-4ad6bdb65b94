package com.xk.domain.event.auth;

import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.enums.auth.AuthFinishTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * 认证完成事件
 */
@Getter
@EventDefinition(appName = AppNameEnum.YD_THIRD_PARTY, domainName = DomainNameEnum.THIRD_PARTY)
public class AuthFinishEvent extends AbstractCommonsDomainEvent {

    private final String authFlowId;

    private final Long userId;

    private final Integer status;

    /**
     * 真实姓名
     */
    private final String realName;

    /**
     * 身份证号
     */
    private final String idCard;

    private final AuthFinishTypeEnum authFinishType;

    private final String remark;

    @Builder
    public AuthFinishEvent(@NonNull Long identifier, Map<String, Object> context, String authFlowId,
            Long userId, Integer status, String realName, String idCard,
            AuthFinishTypeEnum authFinishType, String remark) {
        super(identifier, context);
        this.authFlowId = authFlowId;
        this.userId = userId;
        this.status = status;
        this.realName = realName;
        this.idCard = idCard;
        this.authFinishType = authFinishType;
        this.remark = remark;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).authFlowId(authFlowId).status(status).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
