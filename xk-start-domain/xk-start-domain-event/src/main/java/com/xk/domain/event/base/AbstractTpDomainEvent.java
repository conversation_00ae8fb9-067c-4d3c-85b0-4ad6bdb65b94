package com.xk.domain.event.base;

import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;

import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_THIRD_PARTY,
        domainName = DomainNameEnum.THIRD_PARTY)
public abstract class AbstractTpDomainEvent extends AbstractCommonsDomainEvent {

    public AbstractTpDomainEvent(@NonNull Long identifier, Map<String, Object> context) {
        super(identifier, context);
    }
}
