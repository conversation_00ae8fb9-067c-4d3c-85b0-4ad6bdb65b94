package com.xk.domain.event.cate;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public class DeletedCateEvent extends AbstractCateDomainEvent {

    private final Long cateId;

    private final String groupBusinessType;

    @Builder
    public DeletedCateEvent(@NonNull Long identifier, Map<String, Object> context, Long cateId, String groupBusinessType) {
        super(identifier, context);
        this.cateId = cateId;
        this.groupBusinessType = groupBusinessType;
    }


    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).cateId(this.cateId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }

}
