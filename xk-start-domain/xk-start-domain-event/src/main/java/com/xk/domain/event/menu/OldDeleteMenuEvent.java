package com.xk.domain.event.menu;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public class OldDeleteMenuEvent extends AbstractMenuDomainEvent {

    private final Long menuId;

    private final String groupBusinessType;

    @Builder
    public OldDeleteMenuEvent(@NonNull Long identifier, Map<String, Object> context, Long menuId, String groupBusinessType) {
        super(identifier, context);
        this.menuId = menuId;
        this.groupBusinessType = groupBusinessType;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).menuId(this.menuId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
