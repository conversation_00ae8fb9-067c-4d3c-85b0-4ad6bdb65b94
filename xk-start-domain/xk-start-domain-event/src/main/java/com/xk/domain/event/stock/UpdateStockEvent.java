package com.xk.domain.event.stock;

import java.util.Date;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.enums.stock.StockBusinessTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_GOODS, domainName = DomainNameEnum.GOODS)
public class UpdateStockEvent extends AbstractCommonsDomainEvent {

    private final Long stockId;
    private final StockBusinessTypeEnum stockBusinessType;
    private final Date updateTime;

    @Builder
    public UpdateStockEvent(@NonNull Long identifier, Map<String, Object> context, Long stockId,
            StockBusinessTypeEnum stockBusinessType, Date updateTime) {
        super(identifier, context);
        this.stockId = stockId;
        this.stockBusinessType = stockBusinessType;
        this.updateTime = updateTime;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
