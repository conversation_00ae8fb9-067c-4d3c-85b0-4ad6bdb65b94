package com.xk.domain.event.cate;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_CONFIG,
        domainName = DomainNameEnum.CONFIG)
public abstract class AbstractCateDomainEvent extends AbstractCommonsDomainEvent {

    public AbstractCateDomainEvent(@NonNull Long identifier, Map<String, Object> context) {
        super(identifier, context);

    }

}
