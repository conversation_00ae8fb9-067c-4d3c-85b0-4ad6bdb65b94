package com.xk.domain.event.menu;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * @author: killer
 **/
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_CONFIG,
        domainName = DomainNameEnum.CONFIG)
public abstract class AbstractMenuDomainEvent extends AbstractCommonsDomainEvent {
    protected AbstractMenuDomainEvent(@NonNull Long identifier, Map<String, Object> context) {
        super(identifier, context);
    }
}
