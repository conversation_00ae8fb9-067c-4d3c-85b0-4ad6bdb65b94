package com.xk.domain.event.menu;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public class CreateMenuEvent extends AbstractMenuDomainEvent {

    private final String menuName;

    private final Integer sort;

    private final Integer status;

    private final Long menuId;

    private final String groupBusinessType;

    private final Long parentId;

    @Builder
    public CreateMenuEvent(@NonNull Long identifier, Map<String, Object> context, String menuName, Integer sort, Integer status, Long menuId, String groupBusinessType, Long parentId) {
        super(identifier, context);
        this.menuName = menuName;
        this.sort = sort;
        this.status = status;
        this.menuId = menuId;
        this.groupBusinessType = groupBusinessType;
        this.parentId = parentId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).menuId(this.menuId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
