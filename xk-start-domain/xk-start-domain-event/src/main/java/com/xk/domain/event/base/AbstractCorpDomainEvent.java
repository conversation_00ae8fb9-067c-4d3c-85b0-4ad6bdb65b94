package com.xk.domain.event.base;

import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;

import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_CORP,
        domainName = DomainNameEnum.CORP)
public abstract class AbstractCorpDomainEvent extends AbstractCommonsDomainEvent {

    private final Long corpInfoId;

    public AbstractCorpDomainEvent(@NonNull Long identifier, Map<String, Object> context, Long corpInfoId) {
        super(identifier, context);
        this.corpInfoId = corpInfoId;
    }

}
