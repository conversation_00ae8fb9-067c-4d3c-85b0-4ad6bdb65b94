package com.xk.domain.event.stock;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.enums.stock.StockBusinessTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_GOODS, domainName = DomainNameEnum.GOODS)
public class UpdateRemainStockEvent extends AbstractCommonsDomainEvent {

    private final Date updateTime;
    private final Long stockId;
    /**
     * 扣减数量
     */
    private final BigDecimal count;
    private final StockBusinessTypeEnum stockBusinessType;

    @Builder
    public UpdateRemainStockEvent(@NonNull Long identifier, Map<String, Object> context,
            Date updateTime, Long stockId, BigDecimal count,
            StockBusinessTypeEnum stockBusinessType) {
        super(identifier, context);
        this.updateTime = updateTime;
        this.stockId = stockId;
        this.count = count;
        this.stockBusinessType = stockBusinessType;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
