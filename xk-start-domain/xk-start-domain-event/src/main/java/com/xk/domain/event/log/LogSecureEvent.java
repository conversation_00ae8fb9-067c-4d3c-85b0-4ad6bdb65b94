package com.xk.domain.event.log;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * 支付完成事件
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_AUTH,
        domainName = DomainNameEnum.AUTH)
public class LogSecureEvent extends AbstractCommonsDomainEvent {

    private final String uri;

    private final String sessionId;

    private final String params;

    @Builder
    public LogSecureEvent(@NonNull Long identifier, Map<String, Object> context, String uri, String sessionId,
                          String params) {
        super(identifier, context);
        this.uri = uri;
        this.sessionId = sessionId;
        this.params = params;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return null;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
