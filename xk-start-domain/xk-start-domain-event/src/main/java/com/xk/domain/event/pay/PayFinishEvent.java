package com.xk.domain.event.pay;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Date;
import java.util.Map;

/**
 * 支付完成事件
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_THIRD_PARTY,
        domainName = DomainNameEnum.THIRD_PARTY)
public class PayFinishEvent extends AbstractCommonsDomainEvent {

    private final String orderNo;

    private final String payNo;

    private final Integer status;

    private final Long amount;

    private final Integer channelType;

    private final Integer platformType;

    private final String device;

    private final String tradeStatus;

    private final String remark;

    private final Long accessAccountId;

    private final Date payTime;

    @Builder
    public PayFinishEvent(@NonNull Long identifier, Map<String, Object> context, String orderNo, String payNo,
                          Integer status, Long amount, Integer channelType, Integer platformType, String device,
                          String tradeStatus, String remark, Long accessAccountId, Date payTime) {
        super(identifier, context);
        this.orderNo = orderNo;
        this.payNo = payNo;
        this.status = status;
        this.amount = amount;
        this.channelType = channelType;
        this.platformType = platformType;
        this.device = device;
        this.tradeStatus = tradeStatus;
        this.remark = remark;
        this.accessAccountId = accessAccountId;
        this.payTime = payTime;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).orderNo(orderNo).payNo(payNo).status(status).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
