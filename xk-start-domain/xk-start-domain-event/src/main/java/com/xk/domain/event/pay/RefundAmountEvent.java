package com.xk.domain.event.pay;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * 支付完成事件
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_THIRD_PARTY,
        domainName = DomainNameEnum.THIRD_PARTY)
public class RefundAmountEvent extends AbstractCommonsDomainEvent {

    private final String refundOrderId;
    private final Integer status;

    @Builder
    public RefundAmountEvent(@NonNull Long identifier, Map<String, Object> context, String refundOrderId,
                             Integer status) {
        super(identifier, context);
        this.refundOrderId = refundOrderId;
        this.status = status;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).refundOrderId(refundOrderId).status(status).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
