package com.xk.domain.event.validate;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * 请求限制校验事件
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_USER,
        domainName = DomainNameEnum.USER)
public class RequestLimitValidateEvent extends AbstractCommonsDomainEvent {


    private final Integer type;

    private final String value;

    private final String path;

    private final Long expireTime;

    @Builder
    public RequestLimitValidateEvent(@NonNull Long identifier, Map<String, Object> context, Integer type, String value, String path, Long expireTime) {
        super(identifier, context);

        this.type = type;
        this.value = value;
        this.path = path;
        this.expireTime = expireTime;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
