package com.xk.domain.event.sensitive;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * 支付完成事件
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_CONFIG,
        domainName = DomainNameEnum.CONFIG)
public class UploadSensitiveConfigEvent extends AbstractCommonsDomainEvent {

    /**
     * 敏感词
     */
    private final String sensitiveName;

    /**
     * 敏感等级:警告级WARNING(后台记录，替换类型为空)、屏蔽级MASK(替换内容)、禁发级FORBBIDEN(不允许提交，替换类型强制为ALL)
     */
    private final String sensitiveLevel;

    /**
     * 替换类型:  部分替换、全部替换
     */
    private final  String replaceType;

    /**
     * 替换用词
     */
    private final String replaceWord;

    /**
     * 敏感词用途(发言、昵称、文本 多个用,号分隔)
     */
    private final String sensitiveUse;

    /**
     * 状态
     */
    private final String status;

    private final Long createId;

    @Builder
    public UploadSensitiveConfigEvent(@NonNull Long identifier, Map<String, Object> context, String sensitiveName, String sensitiveLevel, String replaceType, String replaceWord, String sensitiveUse, String status, Long createId) {
        super(identifier, context);
        this.sensitiveName = sensitiveName;
        this.sensitiveLevel = sensitiveLevel;
        this.replaceType = replaceType;
        this.replaceWord = replaceWord;
        this.sensitiveUse = sensitiveUse;
        this.status = status;
        this.createId = createId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
