package com.xk.domain.event.tree;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public class UpdateTreeNodeEvent extends AbstractTreeNodeDomainEvent {

    private final Long nodeId;

    private final String name;

    private final Integer sort;

    private final Integer status;

    private final Integer heightLimit;

    private final Long businessId;

    private final Long businessGroupId;

    private final String groupBusinessType;

    @Builder
    public UpdateTreeNodeEvent(@NonNull Long identifier, Map<String, Object> context, Long nodeId, String name, Integer sort, Integer status, Integer heightLimit, Long businessId, Long businessGroupId, String groupBusinessType) {
        super(identifier, context);
        this.nodeId = nodeId;
        this.name = name;
        this.sort = sort;
        this.status = status;
        this.heightLimit = heightLimit;
        this.businessId = businessId;
        this.businessGroupId = businessGroupId;
        this.groupBusinessType = groupBusinessType;
    }


    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).nodeId(this.nodeId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }

}
