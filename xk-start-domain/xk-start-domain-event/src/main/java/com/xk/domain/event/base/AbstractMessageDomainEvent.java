package com.xk.domain.event.base;

import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;

import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_MESSAGE, domainName = DomainNameEnum.MESSAGE)
public abstract class AbstractMessageDomainEvent extends AbstractCommonsDomainEvent {
    public AbstractMessageDomainEvent(@NonNull Long identifier, Map<String, Object> context) {
        super(identifier, context);
    }
}
