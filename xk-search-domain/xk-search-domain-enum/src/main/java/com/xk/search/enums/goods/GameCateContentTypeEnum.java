package com.xk.search.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Component
@Getter
@AllArgsConstructor
public enum GameCateContentTypeEnum {

    TAG(1, "标签类型"), INPUT(2, "输入类型"),;

    private static final Map<Integer, GameCateContentTypeEnum> MAP;

    static {
        MAP = Arrays.stream(GameCateContentTypeEnum.values()).collect(
                Collectors.toMap(GameCateContentTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static GameCateContentTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }


}
