package com.xk.search.enums.retry;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RetryBizTypeEnum {
    NON(0, "空类型"),
    MALL_PRODUCT(1, "商城商品"),
    MATERIAL_PRODUCT(2, "物料商品"),
    COLLECTIBLE_CARD(3, "收藏卡"),
    MERCHANT_PRODUCT(4, "商家商品"),
    CORP(5, "商家"),
    CORP_FOLLOW(6, "商家-粉丝数"),
    ORDER(7, "订单"), ORDER_LOGISTICS(8, "物流订单")
    ;

    private static final Map<Integer, RetryBizTypeEnum> MAP;

    static {
        MAP = Arrays.stream(RetryBizTypeEnum.values())
                .collect(Collectors.toMap(RetryBizTypeEnum::getCode, enumValue -> enumValue));
    }

    private Integer code;
    private String msg;

    public static RetryBizTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
