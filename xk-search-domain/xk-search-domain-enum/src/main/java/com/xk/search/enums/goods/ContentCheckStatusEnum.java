package com.xk.search.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ContentCheckStatusEnum {
    WAIT_CHECK(0, "内容待审核"), CHECK_PASS(1, "内容审核通过"), CHECK_FAIL(2, "内容审核不通过"),;

    private static final Map<Integer, ContentCheckStatusEnum> MAP;

    static {
        MAP = Arrays.stream(ContentCheckStatusEnum.values())
                .collect(Collectors.toMap(ContentCheckStatusEnum::getCode, enumValue -> enumValue));
    }

    private Integer code;
    private String msg;

    public static ContentCheckStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }


}
