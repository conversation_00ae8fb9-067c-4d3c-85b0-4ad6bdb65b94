package com.xk.search.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ProductTypeEnum {

    NON(0, "空类型"),
    FORTUNE_BOX(1, "福盒"),
    EDGE_BOX(2, "边锋盒子"),
    RUBBED_CARD_PACK(3, "搓卡密"),
    ORIGINAL_BOX(4, "原盒");
    
    private static final Map<Integer, ProductTypeEnum> MAP;

    static {
        MAP = Arrays.stream(ProductTypeEnum.values())
                .collect(Collectors.toMap(ProductTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static ProductTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
