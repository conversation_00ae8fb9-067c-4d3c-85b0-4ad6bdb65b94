package com.xk.search.enums.search;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<PERSON> liu<PERSON>ong
 * @Date 2024/8/15 18:22
 */
@Getter
@AllArgsConstructor
public enum SearchIndexTypeEnum {

    GOODS("goods", "商品搜索", SearchDataSourceEnum.GOODS),
    ORDER_MERCHANT("orderMerchant", "商家商品订单搜索", SearchDataSourceEnum.ORDER_MERCHANT),
    ORDER_MATERIAL("orderMaterial", "物料订单搜索", SearchDataSourceEnum.ORDER_MATERIAL),
    ORDER_MALL("orderMall", "商城商品订单搜索", SearchDataSourceEnum.ORDER_MALL),
    ORDER_LOGISTICS("orderLogistics", "物流订单搜索",SearchDataSourceEnum.ORDER_LOGISTICS),
    CORP("corp", "商家搜索", SearchDataSourceEnum.CORP),

    ;

    private static final Map<String, SearchIndexTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SearchIndexTypeEnum.values())
                .collect(Collectors.toMap(SearchIndexTypeEnum::getType, enumValue -> enumValue));
    }

    private final String type;
    private final String msg;
    private final SearchDataSourceEnum searchDataSource;

    public static SearchIndexTypeEnum getType(String type) {
        return MAP.get(type);
    }

}
