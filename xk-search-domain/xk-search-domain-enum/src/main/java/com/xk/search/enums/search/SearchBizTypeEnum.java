package com.xk.search.enums.search;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SearchBizTypeEnum {

    GOODS_MALL("idx:goods:mall", "商城商品"),
    GOODS_MATERIAL("idx:goods:material", "物料商品"),
    GOODS_MERCHANT("idx:goods:merchant", "商家商品"),
    COLLECTIBLE_CARD("idx:collectible:card", "收藏卡"),
    CORP("idx:corp", "商家"),
    ORDER_MATERIAL("idx:order:material", "物料商品订单"),
    ORDER_MALL("idx:order:mall", "物料商品订单"),
    ORDER_MERCHANT("idx:order:merchant", "商家商品订单"),
    ORDER_LOGISTICS("idx:order:logistics", "物流订单")
    ;

    private static final Map<String, SearchBizTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SearchBizTypeEnum.values())
                .collect(Collectors.toMap(SearchBizTypeEnum::getCode, enumValue -> enumValue));
    }

    private final String code;
    private final String msg;

    public static SearchBizTypeEnum getByCode(String code) {
        return MAP.get(code);
    }
}
