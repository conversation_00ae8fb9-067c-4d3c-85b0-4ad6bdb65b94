package com.xk.search.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> 商品数据状态 启用和禁用
 */

@Getter
@AllArgsConstructor
public enum GoodsStatusEnum {
    OFF(0, "禁用"), ON(1, "启用"),;

    private static final Map<Integer, GoodsStatusEnum> MAP;

    static {
        MAP = Arrays.stream(GoodsStatusEnum.values())
                .collect(Collectors.toMap(GoodsStatusEnum::getCode, enumValue -> enumValue));
    }

    private Integer code;
    private String msg;

    public static GoodsStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
