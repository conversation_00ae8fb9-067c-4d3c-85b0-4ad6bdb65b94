package com.xk.search.enums.order;

import com.xk.search.enums.goods.ProductTypeEnum;
import com.xk.search.enums.search.SearchIndexTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Getter
@AllArgsConstructor
public enum OrderProductTypeEnum {

    NON(0, 0, "NON", "空类型"),
    MALL_PRODUCT(1, 1, "MALL_PRODUCT", "商城商品"),
    MATERIAL_PRODUCT(2, 2, "MATERIAL_PRODUCT", "物料商品"),
    MERCHANT_PRODUCT(3, 3, "MERCHANT_PRODUCT", "商家商品"),
    FORTUNE_BOX(4, ProductTypeEnum.FORTUNE_BOX.getCode(), "FORTUNE_BOX", "福盒"),
    EDGE_BOX(5, ProductTypeEnum.EDGE_BOX.getCode(), "EDGE_BOX","边锋盒子"),
    RUBBED_CARD_PACK(6, ProductTypeEnum.RUBBED_CARD_PACK.getCode(),"RUBBED_CARD_PACK", "搓卡密"),
    ORIGINAL_BOX(7, ProductTypeEnum.ORIGINAL_BOX.getCode(), "ORIGINAL_BOX", "原盒");

    private static final Map<String, SearchIndexTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SearchIndexTypeEnum.values())
                .collect(Collectors.toMap(SearchIndexTypeEnum::getType, enumValue -> enumValue));
    }

    private final Integer type;
    private final Integer productType;
    private final String msg;
    private final String description;

    public static SearchIndexTypeEnum getType(String type) {
        return MAP.get(type);
    }

}
