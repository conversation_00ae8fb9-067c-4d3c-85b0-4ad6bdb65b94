package com.xk.search.enums.search;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TairSearchKeyEnum {

    SIZE("size", "指定查询返回的文档数量"),
    REPLY_WITH_KEYS_CURSOR("reply_with_keys_cursor", "指定是否返回每个索引下一轮查询的游标信息"),
    KEYS_CURSOR("keys_cursor", "指定本次查询的各索引的游标，可以将上轮查询返回的keys_cursor作为本次查询的输入，实现分页查询"),
    SORT("sort", "结果排序，但不支持指定数组类型的字段"),
    ORDER("order", "返回object的排序规则"),
    DESC("desc", "降序"),
    ASC("asc", "升序"),
    QUERY("query", "聚合查询语句"),
    MATCH("match", "基础的匹配查询"),
    TERM("term", "词根查询，不会对查询内容进行拆分"),
    TERMS("terms", "多词根查询"),
    RANGE("range", "范围查询"),
    WILDCARD("wildcard", "通配符查询"),
    PREFIX("prefix", "前缀查询"),
    BOOL("bool", "复合查询"),
    MATCH_ALL("match_all", "查询全部文档"),
    MUST("must", "查询结果需匹配must列表中所有查询子句"),
    SHOULD("should", "查询结果需匹配should列表中任意一个查询子句（表示或）"),
    MINIMUM_SHOULD_MATCH("minimum_should_match", "查询语句会通过分词器拆分成多个词根，您可以通过本参数指定至少需要匹配多少个词根。该参数在未开启模糊匹配且operator为OR时生效"),
    OPERATOR("operator", "指定通过分词器拆分的词根之间的关系，取值为AND和OR（默认）"),
    AND("and", "且的关系"),
    OR("or", "或的关系"),
    FROM("from", "指定开始返回的目标文档起点，默认为0，表示从第一个查到的文档开始返回。"),
    ;

    private final String key;

    private final String msg;


}
