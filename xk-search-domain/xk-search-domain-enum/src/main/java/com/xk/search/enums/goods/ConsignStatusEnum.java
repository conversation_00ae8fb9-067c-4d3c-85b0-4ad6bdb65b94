package com.xk.search.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ConsignStatusEnum {
    CONSIGN_NARMAL(1, "代挂未到期"), CONSIGN_EXPIRED(2, "代挂过期"),;

    private static final Map<Integer, ConsignStatusEnum> MAP;

    static {
        MAP = Arrays.stream(ConsignStatusEnum.values())
                .collect(Collectors.toMap(ConsignStatusEnum::getCode, enumValue -> enumValue));
    }

    private Integer code;
    private String msg;

    public static ConsignStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }


}
