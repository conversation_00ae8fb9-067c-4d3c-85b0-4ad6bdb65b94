package com.xk.search.enums.order;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchIndexTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderTypeSearchEnum {

    MALL_PRODUCT(1, "商城商品", SearchBizTypeEnum.ORDER_MALL,
            SearchIndexTypeEnum.ORDER_MALL), MATERIAL_PRODUCT(2, "物料商品",
                    SearchBizTypeEnum.ORDER_MATERIAL,
                    SearchIndexTypeEnum.ORDER_MATERIAL), MERCHANT_PRODUCT(3, "商家商品",
                            SearchBizTypeEnum.ORDER_MERCHANT, SearchIndexTypeEnum.ORDER_MERCHANT);

    private static final Map<Integer, OrderTypeSearchEnum> MAP;

    static {
        MAP = Arrays.stream(OrderTypeSearchEnum.values())
                .collect(Collectors.toMap(OrderTypeSearchEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;
    private final SearchBizTypeEnum searchBizTypeEnum;
    private final SearchIndexTypeEnum searchIndexTypeEnum;

    public static OrderTypeSearchEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
