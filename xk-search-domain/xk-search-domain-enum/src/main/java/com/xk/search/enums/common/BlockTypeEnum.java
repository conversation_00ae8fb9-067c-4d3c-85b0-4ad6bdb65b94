package com.xk.search.enums.common;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum BlockTypeEnum {

    NON(0, "空类型"),
    STAR_PLAYER(1, "球星"),
    ANIMATION(2, "动漫"),
    ;
    private static final Map<Integer, BlockTypeEnum> MAP;

    static {
        MAP = Arrays.stream(BlockTypeEnum.values())
                .collect(Collectors.toMap(BlockTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static BlockTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
