package com.xk.search.enums.retry;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum OptTypeEnum {
    CREATE("create", "创建"),
    DELETE("delete", "删除");

    private static final Map<String, OptTypeEnum> MAP;

    static {
        MAP = Arrays.stream(OptTypeEnum.values())
                .collect(Collectors.toMap(OptTypeEnum::getCode, enumValue -> enumValue));
    }

    private String code;
    private String msg;

    public static OptTypeEnum getByCode(String code) {
        return MAP.get(code);
    }


}
