package com.xk.search.enums.search;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Component
@Getter
@AllArgsConstructor
public enum SerachIndexEnum {

    GOODS_INDEX(1, "goods", "商品索引"),;

    private static final Map<Integer, SerachIndexEnum> MAP;

    static {
        MAP = Arrays.stream(SerachIndexEnum.values())
                .collect(Collectors.toMap(SerachIndexEnum::getType, enumValue -> enumValue));
    }

    private final Integer type;
    private final String code;
    private final String desc;

    public static SerachIndexEnum getType(Integer type) {
        return MAP.get(type);
    }

}
