package com.xk.search.enums.search;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SearchTypeEnum {

    TEXT("1", "文本类型", "text"),
    TAG("2", "标签类型", "keyword"),
    NUMBER("3", "数字类型", "long"),
    CUSTOMIZE("4", "自定义查询", "*"),
    TAGS("5", "标签类型", "keyword[]"),
    RANGE("6", "范围查询", "*"),
    NUMBERS("7", "数字类型", "long[]"),
    AGG_S("8", "聚合查询", "*"),
    ;

    private static final Map<String, SearchTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SearchTypeEnum.values())
                .collect(Collectors.toMap(SearchTypeEnum::getType, enumValue -> enumValue));
    }

    private final String type;
    private final String msg;
    private final String tairType;

    public static SearchTypeEnum getByType(String type) {
        return MAP.get(type);
    }

}
