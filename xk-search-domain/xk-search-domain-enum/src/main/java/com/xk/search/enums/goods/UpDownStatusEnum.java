package com.xk.search.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum UpDownStatusEnum {
    DOWN(0, "下架"), UP(1, "上架"),;

    private static final Map<Integer, UpDownStatusEnum> MAP;

    static {
        MAP = Arrays.stream(UpDownStatusEnum.values())
                .collect(Collectors.toMap(UpDownStatusEnum::getCode, enumValue -> enumValue));
    }

    private Integer code;
    private String msg;

    public static UpDownStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }


}
