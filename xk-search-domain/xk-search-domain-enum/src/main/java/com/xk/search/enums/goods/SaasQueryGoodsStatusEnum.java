package com.xk.search.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum SaasQueryGoodsStatusEnum {

    WAIT_CHECK(1, "待审核", "upTime", UpDownStatusEnum.UP.getCode(),
            ContentCheckStatusEnum.WAIT_CHECK.getCode(), null), ON_DANDAN_SALE(2, "已上架",
                    "saasContentCheckTime", UpDownStatusEnum.UP.getCode(),
                    ContentCheckStatusEnum.CHECK_PASS.getCode(), null), DANDAN_SALED(3, "售出下架",
                            "saleTime", UpDownStatusEnum.DOWN.getCode(), null,
                            GoodsSaleStatusEnum.SALE.getCode()), DANDAN_DOWN(4, "下架", "createTime",
                                    UpDownStatusEnum.DOWN.getCode(), null, null),;

    private static final Map<Integer, SaasQueryGoodsStatusEnum> MAP;

    static {
        MAP = Arrays.stream(SaasQueryGoodsStatusEnum.values()).collect(
                Collectors.toMap(SaasQueryGoodsStatusEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;
    private final String sortField;
    private final Integer upDownStatus;
    private final Integer contentCheckStatus;
    private final Integer saleStatus;

    public static SaasQueryGoodsStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
