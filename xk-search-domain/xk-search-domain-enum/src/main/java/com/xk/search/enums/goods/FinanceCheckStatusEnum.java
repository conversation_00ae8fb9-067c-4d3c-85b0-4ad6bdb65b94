package com.xk.search.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum FinanceCheckStatusEnum {
    WAIT_CHECK(0, "财务待审核"), CHECK_PASS(1, "财务审核通过"), CHECK_FAIL(2, "财务审核不通过"),;

    private static final Map<Integer, FinanceCheckStatusEnum> MAP;

    static {
        MAP = Arrays.stream(FinanceCheckStatusEnum.values())
                .collect(Collectors.toMap(FinanceCheckStatusEnum::getCode, enumValue -> enumValue));
    }

    private Integer code;
    private String msg;

    public static FinanceCheckStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }


}
