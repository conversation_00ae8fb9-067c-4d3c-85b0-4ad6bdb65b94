package com.xk.search.enums.search;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Component
@Getter
@AllArgsConstructor
public enum SerachIndexPrefixEnum {

    PREFIX_IDX("idx:", "全局索引前缀"),;

    private static final Map<String, SerachIndexPrefixEnum> MAP;

    static {
        MAP = Arrays.stream(SerachIndexPrefixEnum.values()).collect(
                Collectors.toMap(SerachIndexPrefixEnum::getPrefix, enumValue -> enumValue));
    }

    private final String prefix;
    private final String desc;

    public static SerachIndexPrefixEnum getType(String type) {
        return MAP.get(type);
    }

}
