package com.xk.search.enums.search;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TairKeyEnum {

    // index 索引名称
    MAPPINGS("mappings", "映射内容 下面为json格式"), SOURCE("_source", "存储原始文档"), PROPERTIES("properties",
            "映射的字段集合"), INDEX("index", "是否将该字段设置为索引字段 默认为true"),
    // keyword 不可被拆分字符串
    // | text 字符串，且可通过分词器解析，存入索引 | long 长整型，您可以将时间点转为Unix时间戳，存入该数据类型中 | integer 整型
    // | double 双精度浮点型，但不支持NaN、Inf、-Inf等特殊的数据类型
    TYPE("type", "字段的数据类型，同时支持数组类型"), SETTINGS("settings", "配置索引设置"), ANALYSIS("analyzer",
            "解析text存入索引的分词器"),

    ;

    private final String key;

    private final String msg;


}
