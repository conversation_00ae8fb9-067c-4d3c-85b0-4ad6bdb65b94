package com.xk.search.enums.search;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum OrderTypeEnum {

    ORDER_MALL(1, SearchIndexTypeEnum.ORDER_MALL, SearchBizTypeEnum.ORDER_MALL),
    ORDER_MATERIAL(2, SearchIndexTypeEnum.ORDER_MATERIAL, SearchBizTypeEnum.ORDER_MATERIAL),
    ORDER_MERCHANT(3, SearchIndexTypeEnum.ORDER_MERCHANT, SearchBizTypeEnum.ORDER_MERCHANT),
    ;

    private static final Map<Integer, OrderTypeEnum> MAP;

    static {
        MAP = Arrays.stream(OrderTypeEnum.values())
                .collect(Collectors.toMap(OrderTypeEnum::getType, enumValue -> enumValue));
    }

    private final Integer type;
    private final SearchIndexTypeEnum searchIndexTypeEnum;
    private final SearchBizTypeEnum searchBizTypeEnum;

    public static OrderTypeEnum getByType(Integer type) {
        return MAP.get(type);
    }

}
