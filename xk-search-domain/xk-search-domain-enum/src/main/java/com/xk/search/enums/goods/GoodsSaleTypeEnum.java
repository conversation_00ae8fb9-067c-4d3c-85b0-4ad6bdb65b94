package com.xk.search.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum GoodsSaleTypeEnum {

    CONSIGN(1, "代挂"), INSURANCE(2, "采购");

    private static final Map<Integer, GoodsSaleTypeEnum> MAP;

    static {
        MAP = Arrays.stream(GoodsSaleTypeEnum.values())
                .collect(Collectors.toMap(GoodsSaleTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static GoodsSaleTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
