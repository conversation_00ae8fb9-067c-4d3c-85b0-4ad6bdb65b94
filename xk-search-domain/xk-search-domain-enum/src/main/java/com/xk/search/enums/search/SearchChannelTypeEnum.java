package com.xk.search.enums.search;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<PERSON> liucaihong
 * @Date 2024/8/6 15:14
 */
@Getter
@AllArgsConstructor
public enum SearchChannelTypeEnum {

    REDIS_SEARCH(1, "redis_search"),
    ES_SEARCH(2, "elasticsearch"),
    TAIR_SEARCH(3, "tair_search"),;

    private static final Map<Integer, SearchChannelTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SearchChannelTypeEnum.values())
                .collect(Collectors.toMap(SearchChannelTypeEnum::getType, enumValue -> enumValue));
    }

    private Integer type;
    private String msg;

    public static SearchChannelTypeEnum getByType(Integer type) {
        return MAP.get(type);
    }

}
