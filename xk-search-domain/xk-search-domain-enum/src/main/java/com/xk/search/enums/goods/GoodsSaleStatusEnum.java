package com.xk.search.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum GoodsSaleStatusEnum {

    UN_SALE(0, "未售出"), SALE(1, "已售出"),

    ;

    private static final Map<Integer, GoodsSaleStatusEnum> MAP;

    static {
        MAP = Arrays.stream(GoodsSaleStatusEnum.values())
                .collect(Collectors.toMap(GoodsSaleStatusEnum::getCode, enumValue -> enumValue));
    }

    private Integer code;
    private String msg;

    public static GoodsSaleStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
