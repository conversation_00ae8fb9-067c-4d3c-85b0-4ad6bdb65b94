package com.xk.search.domain.model.order.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.search.domain.model.search.annotation.SearchField;
import com.xk.search.enums.search.SearchTypeEnum;

import lombok.*;

/**
 * 订单
 * <AUTHOR>
 * @Date 2024/8/6 14:29
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderSearchEntity implements Entity<StringIdentifier> {

    /**
     * 订单ID
     */
    @SearchField(searchType = SearchTypeEnum.TAG)
    private String orderNo;

    /**
     * 订单类型
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Integer orderType;

    /**
     * 板块类型
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Integer blockType;

    /**
     * 实付金额
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Long payAmount;

    /**
     * 创建时间
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Long createTime;

    /**
     * 订单状态
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Integer orderStatus;

    /**
     * 物流订单状态
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Integer logisticsOrderStatus;

    /**
     * 物流公司名
     */
    @SearchField(searchType = SearchTypeEnum.TAG)
    private String logisticsCorpName;

    /**
     * 获奖状态 0-未获奖 1-已获奖
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Integer prizeStatus;

    /**
     * 物流单号
     */
    @SearchField(searchType = SearchTypeEnum.TAG)
    private String logisticsNo;

    /**
     * 物流订单ID
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Long logisticsOrderId;

    /**
     * 取消截止时间
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Long cancelDeadlineTime;

    /**
     * 订单商品总数
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Integer orderTotalBuyCount;

    @SearchField(searchType = SearchTypeEnum.RANGE, hasIndex = false,
            queryFieldValue = "createTime", rangeIsLte = true)
    private String deleteTime;

    @SearchField(searchType = SearchTypeEnum.AGG_S, hasIndex = false,
            queryFieldValue = "logisticsOrderStatus")
    private String agg;

    @Override
    public @NonNull StringIdentifier getIdentifier() {
        return StringIdentifier.builder().id(orderNo).build();
    }

    /**
     * @throws ExceptionWrapperThrowable ExceptionWrapperThrowable
     */
    @Override
    public Validatable<StringIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
