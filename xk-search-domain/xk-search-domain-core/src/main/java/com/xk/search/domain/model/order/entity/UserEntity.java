package com.xk.search.domain.model.order.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.search.domain.model.search.annotation.SearchField;
import com.xk.search.enums.search.SearchTypeEnum;
import lombok.*;

/**
 * 用户信息
 * <AUTHOR>
 * @Date 2024/8/6 14:29
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserEntity implements Entity<LongIdentifier> {

    /**
     * 用户id
     */
    @SearchField(searchType = SearchTypeEnum.NUMBER)
    private Long userId;

    /**
     * 用户昵称
     */
    @SearchField(searchType = SearchTypeEnum.TAG)
    private String nickname;

    /**
     * 用户头像
     */
    @SearchField(searchType = SearchTypeEnum.TAG)
    private String userLogo;

    /**
     * 用户手机号
     */
    @SearchField(searchType = SearchTypeEnum.TAG)
    private String userMobile;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(userId).build();
    }

    /**
     * @throws ExceptionWrapperThrowable ExceptionWrapperThrowable
     */
    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
