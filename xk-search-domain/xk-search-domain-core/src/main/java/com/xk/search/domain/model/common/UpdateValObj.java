package com.xk.search.domain.model.common;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateValObj {

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    public void setDefaultValue() {
        this.updateTime = new Date();
    }
}
