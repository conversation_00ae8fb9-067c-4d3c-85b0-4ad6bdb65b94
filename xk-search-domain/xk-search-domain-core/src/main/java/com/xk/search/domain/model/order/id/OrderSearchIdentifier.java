package com.xk.search.domain.model.order.id;

import com.myco.mydata.domain.model.Identifier;
import com.xk.search.enums.search.SearchChannelTypeEnum;
import lombok.*;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-22
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderSearchIdentifier implements Identifier<OrderSearchIdentifier> {

    private SearchChannelTypeEnum searchChannelType;

    private String orderNo;

    @Override
    public @NonNull OrderSearchIdentifier getIdentifier() {
        return OrderSearchIdentifier.builder().searchChannelType(searchChannelType).orderNo(orderNo).build();
    }
}
