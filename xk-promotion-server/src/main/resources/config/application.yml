debug: true
server:
  port: 11008
spring:
  application:
    name: xkPromotion
  profiles:
    active: "@profileActive@"
    include: commons,data,jms,cache,http,schedule,proxy,os,server
  webflux:
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  main:
    allow-bean-definition-overriding: true

application:
  validation:
    enable: false
  proxy:
    business: true
  data:
    sqlmap:
      resources: classpath*:com/myco/mydata/infrastructure/data/sqlmap/**/*.xml,classpath*:com/myco/mydata/config/infrastructure/data/sqlmap/**/*.xml,classpath*:com/xk/promotion/infrastructure/data/sqlmap/**/*.xml,classpath*:com/xk/infrastructure/data/sqlmap/**/*.xml
  ncs:
    zookeeper:
      enable: true
  jms:
    kafka-sender:
      enable: false
    rocketmq-sender:
      enable: true
      aclEnable: false
    rocketmq-producer:
      enable: true
      aclEnable: false
    rocketmq-jvm:
      enable: true
      aclEnable: false
    rocketmq-consumer:
      enable: true
      aclEnable: false
    zmq-response:
      enable: false
  http:
    http:
      enable: true
  redis:
    zmqRedisClient:
      enable: true
    seqRedisClient:
      enable: true
    busiRedisClient:
      enable: true
  scheduling:
    quartz:
      enable: true
      loader: "ncs"
