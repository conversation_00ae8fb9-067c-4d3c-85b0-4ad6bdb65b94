package com.xk.search.application.handler.event.logistics;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.domain.event.logistics.LogisticsDetailUpdateEvent;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import com.xk.search.domain.model.logistics.LogisticsSearchRoot;
import com.xk.search.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.search.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.search.domain.model.logistics.valobj.LogisticsIndexValueObject;
import com.xk.search.domain.service.logistics.LogisticsSearchAdapterService;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelDefaultEnum;
import com.xk.search.enums.search.SearchIndexTypeEnum;
import com.xk.search.interfaces.dto.rsp.logistics.AppDetailResDto;
import com.xk.search.interfaces.dto.rsp.logistics.LogisticsValueObject;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsDetailUpdateEventHandler
        extends AbstractEventVerticle<LogisticsDetailUpdateEvent> {

    private final LogisticsSearchAdapterService logisticsSearchAdapterService;

    @Override
    public Mono<Void> handle(Mono<LogisticsDetailUpdateEvent> event) {
        return event.flatMap(updateEvent -> {
            AppDetailResDto resDto = AppDetailResDto.builder()
                    .logisticsNo(updateEvent.getLogisticsNo())
                    .logisticsCorpName(updateEvent.getLogisticsCorpName())
                    .logisticsValueObjectList(
                            JSONArray.parseArray(updateEvent.getTrajectoryValueObject(),
                                    LogisticsValueObject.class))
                    .build();
            return logisticsSearchAdapterService.updateDocumentField(Mono.just(
                    LogisticsSearchRoot.builder()
                                    .identifier(LogisticsOrderIdentifier.builder()
                                            .logisticsOrderId(updateEvent.getLogisticsOrderId())
                                            .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType())
                                            .build())
                                    .logisticsIndexValueObject(LogisticsIndexValueObject.builder()
                                            .logisticsOrderType(LogisticsOrderTypeEnum.getByCode(updateEvent.getLogisticsOrderType()))
                                            .searchIndexTypeEnum(SearchIndexTypeEnum.ORDER_LOGISTICS)
                                            .searchBizTypeEnum(SearchBizTypeEnum.ORDER_LOGISTICS)
                                            .build())
                                    .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                            .logisticsOrderId(updateEvent.getLogisticsOrderId())
                                            .goodsDetailList(JSONObject.toJSONString(resDto))
                                            .build())
                                    .build()))
                    .doOnSuccess(x -> {
                        log.debug("更新物流订单成功：{}", updateEvent.getLogisticsOrderId());
                    }).doOnError(x -> {
                        log.error("更新物流订单失败：{}", updateEvent.getLogisticsOrderId());
                    });
        });
    }
}
