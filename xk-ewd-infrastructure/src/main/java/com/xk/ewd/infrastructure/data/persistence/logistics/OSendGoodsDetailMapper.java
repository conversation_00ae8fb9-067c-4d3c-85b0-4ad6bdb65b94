package com.xk.ewd.infrastructure.data.persistence.logistics;

import java.util.Date;
import java.util.List;

import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDetail;

/**
 * <AUTHOR>
 * @description 针对表【o_send_goods_detail】的数据库操作Mapper
 * @createDate 2025-07-17 17:22:42
 * @Entity com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDetail
 */
@Repository
@Table(value = "o_send_goods_detail", ewdAsyncEnabled = false)
public interface OSendGoodsDetailMapper {

    int deleteByPrimaryKey(OSendGoodsDetail record);

    int insert(OSendGoodsDetail record);

    int insertSelective(OSendGoodsDetail record);

    OSendGoodsDetail selectByPrimaryKey(OSendGoodsDetail record);

    int updateByPrimaryKeySelective(OSendGoodsDetail record);

    int updateByPrimaryKey(OSendGoodsDetail record);

    List<OSendGoodsDetail> selectById(OSendGoodsDetail record);

    Date selectMaxTimeById(OSendGoodsDetail record);
}
