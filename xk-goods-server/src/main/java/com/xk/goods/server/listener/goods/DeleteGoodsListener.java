package com.xk.goods.server.listener.goods;

import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;
import com.xk.goods.domain.event.goods.DeleteGoodsEvent;

import lombok.RequiredArgsConstructor;

@ConsumerListener
@RequiredArgsConstructor
public class DeleteGoodsListener extends AbstractDispatchMessageListener<DeleteGoodsEvent>
        implements MessageListenerConcurrently {
    private final EventRootService eventRootService;

    @Override
    public void doProcessMessage(DeleteGoodsEvent event) throws Throwable {
        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(true).build();
        eventRootService.handler(eventRoot);
    }
}
