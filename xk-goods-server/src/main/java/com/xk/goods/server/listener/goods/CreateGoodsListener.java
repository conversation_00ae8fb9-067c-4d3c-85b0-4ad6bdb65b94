package com.xk.goods.server.listener.goods;

import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;
import com.xk.goods.domain.event.goods.CreateGoodsEvent;

import lombok.RequiredArgsConstructor;

@ConsumerListener
@RequiredArgsConstructor
public class CreateGoodsListener extends AbstractDispatchMessageListener<CreateGoodsEvent>
        implements MessageListenerConcurrently {
    private final EventRootService eventRootService;

    @Override
    public void doProcessMessage(CreateGoodsEvent event) throws Throwable {
        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(true).build();
        eventRootService.handler(eventRoot);
    }
}
