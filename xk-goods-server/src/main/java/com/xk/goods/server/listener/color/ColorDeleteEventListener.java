package com.xk.goods.server.listener.color;

import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;
import com.xk.goods.domain.event.color.ColorDeleteEvent;

import lombok.RequiredArgsConstructor;

@ConsumerListener
@RequiredArgsConstructor
public class ColorDeleteEventListener extends AbstractDispatchMessageListener<ColorDeleteEvent>
        implements MessageListenerConcurrently {

    private final EventRootService eventRootService;

    @Override
    public void doProcessMessage(ColorDeleteEvent event) throws Throwable {
        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(true).build();
        eventRootService.handler(eventRoot);
    }
}
