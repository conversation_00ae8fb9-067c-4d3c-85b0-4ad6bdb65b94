package com.xk.acct.interfaces.dto.req.corp;

import com.myco.mydata.commons.constant.SensitiveUseEnum;
import com.myco.mydata.domain.model.action.session.AdminInterface;
import com.myco.mydata.domain.model.sensitive.FilterSensitive;
import com.myco.mydata.domain.model.sensitive.FilterSensitiveList;

import com.xk.acct.interfaces.dto.req.user.UserCreateReqDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@FilterSensitiveList({
        @FilterSensitive(value = SensitiveUseEnum.PUBLISH_CONTENT,
                fields = {"identificationNumber", "loginPassword", "mobile"}),
        @FilterSensitive(value = SensitiveUseEnum.NICKNAME, fields = {"nickname"}),})
public class UserCreateCorpReqDto extends UserCreateReqDto implements AdminInterface {
    /**
     * 验证码
     */
    private String validateCode;
}
