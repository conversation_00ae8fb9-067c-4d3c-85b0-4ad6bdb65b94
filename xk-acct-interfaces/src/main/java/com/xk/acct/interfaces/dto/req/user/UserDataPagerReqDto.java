package com.xk.acct.interfaces.dto.req.user;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class UserDataPagerReqDto extends RequireSessionDtoPager {

    private String itemKey;
    /**
     * 语言
     */
    private String lang;
    /**
     * item_key_desc键值描述
     */
    private String itemKeyDesc;
    /**
     * item_pk字典父键值
     */
    private String itemPk;
    /**
     * 类型：0 系统类型；1 业务类型
     */
    private Integer itemType;


}
