package com.xk.acct.interfaces.dto.req.user;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: 用户配置修改
 * @date 2025/5/12 13:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserConfigReqDto extends RequireSessionDto {
    /**
     * 用户ID
     * */
    private Long userId;

    /**
     * 平台通知
     */
    private String platformNotify;

    /**
     * 活动通知
     */
    private String activityNotify;

    /**
     * 交易通知
     */
    private String transactionNotify;

    /**
     * 订单通知
     */
    private String orderNotify;

    /**
     * 是否冻结
     */
    private String isFreeze;

    /**
     * 是否禁止购物
     */
    private String isForbidDeal;

    /**
     * 是否禁止编辑
     */
    private String isForbidEdit;
}
