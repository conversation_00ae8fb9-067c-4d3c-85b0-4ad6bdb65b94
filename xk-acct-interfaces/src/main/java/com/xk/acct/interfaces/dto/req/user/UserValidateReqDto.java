package com.xk.acct.interfaces.dto.req.user;

import com.myco.mydata.commons.constant.ValidateCodeBusinessContentType;
import com.myco.mydata.commons.constant.ValidateCodeBusinessType;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class UserValidateReqDto extends UserIdentifyCodeReqDto {
    /**
     * 识别号类型.
     */
    @NotNull
    private String identifyType;
    /**
     * 验证码的业务类型
     */
    @NotNull
    private ValidateCodeBusinessType validateCodeBusinessType;

    /**
     * 验证码类型。1：仅数字，2:仅字母,3:数字和字母组合. 默认为3 数字和字母组合 .
     */
    private ValidateCodeBusinessContentType validateCodeType = ValidateCodeBusinessContentType.NUMBERS;

    /**
     * 验证码长度.默认为6.
     */
    private Integer validateCodeLength = 6;
    /**
     * 绑定手机时，手机区位码必填
     */
    private String mobileCode;
    /**
     * 是否根据session修改识别号，默认null和true表示根据session获取识别号；
     */
    private Boolean isEdit;
}
