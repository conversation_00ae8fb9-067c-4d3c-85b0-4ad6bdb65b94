package com.xk.acct.interfaces.dto.req.user;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.action.session.AbstractSession;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.InterfaceWrapperThrowable;

import com.xk.message.enums.validate.IdentifyTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import reactor.core.publisher.Mono;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SendAuthCodeReqDto extends AbstractSession {
    /**
     * 发送方式
     */
    @NotNull
    private IdentifyTypeEnum identifyType;
    /**
     * 识别号
     */
    @NotBlank
    private String identificationNumber;
    /**
     * 登录密码
     * 后台登录必须
     */
    @NotBlank
    private String loginPassword;
    /**
     * 当前会话语言
     */
    @NotNull
    private SystemLanguageLocale language;
    /**
     * 平台类型
     */
    @NotNull
    private PlatformTypeEnum platformType;
    /**
     * 如果是发送方式为手机，需填写手机号区号
     */
    private String mobileCode;

    public Mono<Void> validate() {
        if (IdentifyTypeEnum.MOBILE.equals(identifyType) && mobileCode == null) {
            return Mono.error(new InterfaceWrapperThrowable(SystemErrorEnum.VALIDATE_FAILURE));
        }
        return Mono.empty();
    }

}
