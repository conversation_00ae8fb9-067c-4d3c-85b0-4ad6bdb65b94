package com.xk.config.interfaces.api.dict;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.xk.config.interfaces.dto.req.res.DictPasswordSaveReqDto;
import com.xk.config.interfaces.dto.req.res.DictReqDto;
import com.xk.config.interfaces.service.dict.DictService;

import reactor.core.publisher.Mono;

/**
 * 全局设置管理
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/config/dict")
public interface DictDocService extends DictService {
    /**
     * 更新/新增商品规则信息
     *
     * @param sysResourceReqDtoMono sysResourceReqDtoMono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/updateOrSave")
    Mono<Void> updateOrSave(@RequestBody Mono<DictReqDto> sysResourceReqDtoMono);

    /**
     * 修改交易密码
     * 
     * @param mono mono
     * @return
     */
    @Override
    @PostMapping("/password/save")
    Mono<Void> updatePassword(@RequestBody Mono<DictPasswordSaveReqDto> mono);
}
