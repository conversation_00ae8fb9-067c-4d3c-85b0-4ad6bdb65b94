package com.xk.config.interfaces.api.recommend;

import com.xk.config.interfaces.dto.req.recommend.RecommendDeleteReqDto;
import com.xk.config.interfaces.dto.req.recommend.RecommendSaveReqDto;
import com.xk.config.interfaces.dto.req.recommend.RecommendUpdateReqDto;
import com.xk.config.interfaces.dto.req.recommend.RecommendUpdateShowReqDto;
import com.xk.config.interfaces.service.recommend.RecommendService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/config/recommend")
public interface RecommendDocService extends RecommendService {
    /**
     * 新增轮播图广告配置
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostMapping("/save")
    Mono<Void> save(@RequestBody Mono<RecommendSaveReqDto> mono);

    /**
     * 更新轮播图广告配置
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostMapping("/update")
    Mono<Void> update(@RequestBody Mono<RecommendUpdateReqDto> mono);

    /**
     * 更新轮播图广告配置-是否显示
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostMapping("/updateShow")
    Mono<Void> updateShow(@RequestBody Mono<RecommendUpdateShowReqDto> mono);

    /**
     * 删除轮播图广告配置
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostMapping("/delete")
    Mono<Void> delete(@RequestBody Mono<RecommendDeleteReqDto> mono);
}
