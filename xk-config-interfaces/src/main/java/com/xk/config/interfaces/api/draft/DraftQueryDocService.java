package com.xk.config.interfaces.api.draft;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.xk.config.interfaces.dto.req.draft.DraftSearchReqDto;
import com.xk.config.interfaces.dto.rsp.draft.DraftSearchResDto;
import com.xk.config.interfaces.query.draft.DraftQueryService;

import reactor.core.publisher.Mono;

/**
 * 草稿箱查询
 */
@Controller
@RequestMapping("/config/draft/query")
public interface DraftQueryDocService extends DraftQueryService {

    /**
     * 查询草稿箱
     *
     * @param mono mono
     * @return Mono<Object>
     */
    @Override
    @PostMapping("/search")
    Mono<DraftSearchResDto> searchDraft(@RequestBody Mono<DraftSearchReqDto> mono);
}
