package com.xk.config.interfaces.api.dict;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.config.interfaces.dto.req.res.DictQueryReqDto;
import com.xk.config.interfaces.dto.rsp.res.DictLogisticsCorpRspDto;
import com.xk.config.interfaces.dto.rsp.res.DictRspDto;
import com.xk.config.interfaces.dto.rsp.res.GoodsRuleRspDto;
import com.xk.config.interfaces.query.dict.DictQueryService;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/config/dict/query")
public interface DictDocQueryService extends DictQueryService {
    /**
     * 查询商品规则
     *
     * @param dictReqDto dictReqDto
     * @return Mono<DictRspDto>
     */
    @Override
    @PostMapping("/search")
    Mono<DictRspDto> search(@RequestBody Mono<DictQueryReqDto> dictReqDto);

    /**
     * 查询所有物流公司
     *
     * @param dictQueryReqDto dictQueryReqDto
     * @return Mono<DictRspDto>
     */
    @Override
    @PostMapping("/logistics/corp")
    Mono<DictLogisticsCorpRspDto> searchLogisticsCorp(
            @RequestBody Mono<RequireSessionDto> dictQueryReqDto);


    /**
     * 查询全部商品规则
     *
     * @param dictQueryReqDto dictQueryReqDto
     * @return Mono<DictRspDto>
     */
    @Override
    @PostMapping("/searchAll")
    Mono<GoodsRuleRspDto> searchAll(@RequestBody Mono<DictQueryReqDto> dictQueryReqDto);
}
