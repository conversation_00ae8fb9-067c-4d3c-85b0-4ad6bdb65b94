package com.xk.config.interfaces.api.draft;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.xk.config.interfaces.dto.req.draft.DraftReqDto;
import com.xk.config.interfaces.service.draft.DraftService;

import reactor.core.publisher.Mono;

/**
 * 草稿箱管理
 */
@Controller
@RequestMapping("/config/draft")
public interface DraftDocService extends DraftService {

    /**
     * 创建草稿箱
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/create")
    Mono<Void> createDraft(@RequestBody Mono<DraftReqDto> mono);
}
