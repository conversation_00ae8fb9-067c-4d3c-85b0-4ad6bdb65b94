package com.xk.config.interfaces.api.res;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.xk.config.interfaces.dto.req.res.*;
import com.xk.config.interfaces.dto.rsp.res.SysResourceSaveCorpRspDto;
import com.xk.config.interfaces.service.res.SysResourceService;

import reactor.core.publisher.Mono;

/**
 * 系统资源/操作服务
 *
 * @author: killer
 **/
@Controller
@RequestMapping("/config/res")
public interface SysResourceDocService extends SysResourceService {

    /**
     * 保存配置
     *
     * @param sysResourceReqDtoMono sysResourceReqDtoMono
     * @return Mono<Void>
     */
    @PostMapping("/save")
    Mono<Void> save(@RequestBody Mono<SysResourceReqDto> sysResourceReqDtoMono);

    /**
     * C端保存配置
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostMapping("/saveByCorp")
    Mono<Void> saveByCorp(@RequestBody Mono<SysResourceCorpReqDto> mono);

    /**
     * 更新配置
     *
     * @param sysResourceEditReqDtoMono sysResourceEditReqDtoMono
     * @return Mono<Void>
     */
    @PostMapping("/update")
    Mono<Void> update(@RequestBody Mono<SysResourceEditReqDto> sysResourceEditReqDtoMono);

    /**
     * 删除配置
     *
     * @param sysResourceIdReqDtoMono sysResourceIdReqDtoMono
     * @return Mono<Void>
     */
    @PostMapping("/remove")
    Mono<Void> remove(@RequestBody Mono<SysResourceIdReqDto> sysResourceIdReqDtoMono);

    /**
     * 删除图片
     *
     * @param sysResourceIdsReqDtoMono sysResourceIdsReqDtoMono
     * @return Mono<Void>
     */
    @PostMapping("/removePic")
    Mono<Void> removePic(@RequestBody Mono<SysResourceIdsReqDto> sysResourceIdsReqDtoMono);

    /**
     * 保存资源分类
     *
     * @param updateResCategoryReqDtoMono mono
     * @return mono
     */
    @PostMapping("/saveResCategory")
    Mono<Void> saveResCategory(
            @RequestBody Mono<UpdateResCategoryReqDto> updateResCategoryReqDtoMono);

    /**
     * 更新资源分类
     *
     * @param updateResCategoryReqDtoMono mono
     * @return mono
     */
    @PostMapping("/updateResCategory")
    Mono<Void> updateResCategory(
            @RequestBody Mono<UpdateResCategoryReqDto> updateResCategoryReqDtoMono);

    /**
     * 删除资源分类
     *
     * @param resCategoryIdentifierDtoMono mono
     * @return mono
     */
    @PostMapping("/deleteResCategory")
    Mono<Void> deleteResCategory(
            @RequestBody Mono<ResCategoryIdentifierDto> resCategoryIdentifierDtoMono);

    /**
     * 新增图片分类
     *
     * @param sysCategorySaveReqDto mono
     * @return mono
     */
    @PostMapping("/savePicCategory")
    Mono<Void> savePicCategory(@RequestBody Mono<PicCategorySaveReqDto> sysCategorySaveReqDto);

    /**
     * 更新图片分类
     *
     * @param sysCategoryUpdateReqDto mono
     * @return mono
     */
    @PostMapping("/updatePicCategory")
    Mono<Void> updatePicCategory(@RequestBody Mono<PicCategoryUpdateReqDto> sysCategoryUpdateReqDto);

    /**
     * 删除图片分类
     *
     * @param sysCategoryRemoveReqDto mono
     * @return mono
     */
    @PostMapping("/removePicCategory")
    Mono<Void> removePicCategory(@RequestBody Mono<PicCategoryRemoveReqDto> sysCategoryRemoveReqDto);

    /**
     * 商户保存资源
     *
     * @param mono mono
     * @return Mono<SysResourceSaveCorpRspDto>
     */
    @Override
    @PostMapping("/save/corp")
    Mono<SysResourceSaveCorpRspDto> saveCorp(@RequestBody Mono<SysResourceSaveCorpReqDto> mono);
}
