package com.xk.config.interfaces.api.res;

import java.util.List;

import com.xk.config.interfaces.dto.req.res.*;
import com.xk.config.interfaces.dto.rsp.res.PicCategoryTreeNodeDto;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.config.interfaces.dto.rsp.res.SysResourceRspDto;
import com.xk.config.interfaces.query.res.SysResourceQueryService;
import com.xk.interfaces.dto.tree.TreeNodeRspDto;

import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 系统资源/查询服务
 *
 * @author: killer
 **/
@Controller
@RequestMapping("/config/res/query")
public interface SysResourceDocQueryService extends SysResourceQueryService {

    /**
     * 根据id查询资源
     *
     * @param sysResourceIdReqDtoMono sysResourceIdReqDtoMono
     * @return Mono<SysResourceRspDto>
     */
    @PostMapping("/getById")
    Mono<SysResourceRspDto> getById(@RequestBody Mono<SysResourceIdReqDto> sysResourceIdReqDtoMono);

    /**
     * 根据条件查询配置
     *
     * @param sysResourcePagerReqDtoMono sysResourcePagerReqDtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/search")
    Mono<Pagination> search(@RequestBody Mono<SysResourcePagerReqDto> sysResourcePagerReqDtoMono);

    /**
     * 查询图片
     *
     * @param sysResourcePicPagerReqDtoMono sysResourcePagerReqDtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/searchPic")
    Mono<Pagination> searchPic(@RequestBody Mono<SysResourcePicPagerReqDto> sysResourcePicPagerReqDtoMono);

    /**
     * 查询图片分类树
     *
     * @param cateTreeReqDto sysResourcePagerReqDtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/searchPicCateTree")
    Mono<List<PicCategoryTreeNodeDto>> searchPicCateTree(@RequestBody Mono<PicCategoryReqDto> cateTreeReqDto);

    /**
     * 根据类型查询资源
     *
     * @param sysResourceIdReqDtoMono sysResourceIdReqDtoMono
     * @return Mono<List < SysResourceRspDto>>
     */
    @PostMapping("/getByType")
    Mono<List<SysResourceRspDto>> getByType(
            @RequestBody Mono<SysResourceGroupReqDto> sysResourceIdReqDtoMono);

    /**
     * 查询资源分类
     *
     * @param mono mono
     * @return void
     */
    @PostMapping("/findResCateTree")
    Mono<List<TreeNodeRspDto>> findResCateTree(@RequestBody Mono<CateTreeReqDto> mono);

    /**
     * 查询系列分类
     *
     * @param mono mono
     * @return void
     */
    @PostMapping("/findSeriesCateTree")
    Mono<List<TreeNodeRspDto>> findSeriesCateTree(@RequestBody Mono<CateTreeReqDto> mono);
}
