package com.xk.config.interfaces.api.recommend;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.config.interfaces.dto.req.recommend.RecommendAppQueryReqDto;
import com.xk.config.interfaces.dto.req.recommend.RecommendQueryReqDto;
import com.xk.config.interfaces.dto.rsp.res.RecommendRspDto;
import com.xk.config.interfaces.query.recommend.RecommendQueryService;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/config/recommend/query")
public interface RecommendQueryDocService extends RecommendQueryService {
    /**
     * 查询轮播图配置列表分页
     *
     * @param mono mono
     * @return Mono<Pagination>
     */
    @Override
    @PostMapping("/search")
    Mono<Pagination> search(@RequestBody Mono<RecommendQueryReqDto> mono);

    /**
     * 用户：查询轮播图配置列表分页
     *
     * @param mono mono
     * @return Mono<Pagination>
     */
    @Override
    @PostMapping("/searchByUser")
    Mono<Pagination> searchByUser(@RequestBody Mono<RecommendAppQueryReqDto> mono);

    /**
     * 查询轮播图配置详情
     *
     * @param mono mono
     * @return Mono<Pagination>
     */
    @Override
    @PostMapping("/searchDetail")
    Mono<RecommendRspDto> searchDetail(@RequestBody Mono<RecommendQueryReqDto> mono);
}
