package com.xk.config.interfaces.api.sensitive;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.config.interfaces.query.sensitive.SensitiveQueryService;
import com.xk.interfaces.dto.req.sensitive.SensitiveConfigIdReqDto;
import com.xk.interfaces.dto.req.sensitive.SensitiveConfigReqDto;
import com.xk.interfaces.dto.rsp.sensitive.SensitiveConfigResDto;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

/**
 * 敏感词查询
 */
@Controller
@RequestMapping("/config/sensitive/query")
public interface SensitiveDocQueryService extends SensitiveQueryService {

    /**
     * 根据id敏感词
     *
     * @param dtoMono
     * @return
     */
    @Override
    @PostMapping("/getSensitiveConfigById")
    Mono<SensitiveConfigResDto> getSensitiveConfigById(@RequestBody Mono<SensitiveConfigIdReqDto> dtoMono);

    /**
     * 敏感词分页列表
     *
     * @param dtoMono
     * @return
     */
    @Override
    @PostMapping("/getSensitiveConfigPager")
    Mono<Pagination> getSensitiveConfigPager(@RequestBody Mono<SensitiveConfigReqDto> dtoMono);
}
