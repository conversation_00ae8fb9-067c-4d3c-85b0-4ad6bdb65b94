package com.xk.config.infrastructure.repository.recommend;

import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.config.domain.model.recommend.RecommendRoot;
import com.xk.config.domain.repository.recommend.RecommendRootRepository;
import com.xk.config.infrastructure.data.persistence.recommend.TRecommendAdMapper;
import com.xk.config.infrastructure.data.po.recommend.TRecommendAd;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class RecommendRootRepositoryImpl implements RecommendRootRepository {

    private final TRecommendAdMapper recommendAdMapper;

    private final Converter converter;

    @Override
    public Mono<Void> updateAll(RecommendRoot root) {
        return null;
    }

    @Override
    public Mono<Long> removeRt(RecommendRoot root) {
        return null;
    }

    @Override
    public Mono<Void> save(RecommendRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(
                        domain -> Mono.justOrEmpty(domain.getRecommendEntity())
                                .flatMap(entity -> save(entity, TRecommendAd.class,
                                        converter::convert, recommendAdMapper::insertSelective)))
                .then();
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(RecommendRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(RecommendRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(domain -> Mono.justOrEmpty(domain.getRecommendEntity())
                        .flatMap(entity -> save(entity, TRecommendAd.class, converter::convert,
                                recommendAdMapper::updateByPrimaryKeySelective)))
                .then();
    }

    @Override
    public Mono<Void> remove(RecommendRoot root) {
        return Mono.justOrEmpty(root)
                .flatMap(
                        domain -> Mono.justOrEmpty(domain.getRecommendEntity())
                                .flatMap(entity -> save(entity, TRecommendAd.class,
                                        converter::convert, recommendAdMapper::deleteByPrimaryKey)))
                .then();
    }
}
