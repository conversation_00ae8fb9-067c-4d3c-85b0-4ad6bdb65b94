package com.xk.config.infrastructure.repository.recommend;

import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.config.domain.model.recommend.RecommendEntity;
import com.xk.config.domain.repository.recommend.RecommendRootQueryRepository;
import com.xk.config.infrastructure.data.persistence.recommend.TRecommendAdMapper;
import com.xk.config.infrastructure.data.po.recommend.TRecommendAd;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class RecommendRootQueryRepositoryImpl implements RecommendRootQueryRepository {

    private final TRecommendAdMapper recommendAdMapper;

    private final Converter converter;

    @Override
    public Flux<RecommendEntity> searchRecommend(Pagination pagination) {
        return search(pagination, recommendAdMapper::searchRecommend, RecommendEntity.class,
                converter::convert);
    }

    @Override
    public Mono<RecommendEntity> searchRecommendDetailById(LongIdentifier longIdentifier) {
        return get(longIdentifier,
                identifier -> recommendAdMapper.selectByPrimaryKey(
                        TRecommendAd.builder().recommendAdId(identifier.id()).build()),
                RecommendEntity.class, converter::convert);
    }
}
