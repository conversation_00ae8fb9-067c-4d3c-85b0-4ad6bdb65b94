package com.xk.config.infrastructure.support;

import com.xk.config.infrastructure.commons.XkConfigInfrastructureErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.wrapper.InfrastructureWrapperThrowable;

/**
 * @author: killer
 **/
public class XkConfigInfrastructureException extends InfrastructureWrapperThrowable {

    public XkConfigInfrastructureException(XkConfigInfrastructureErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

    public XkConfigInfrastructureException(XkConfigInfrastructureErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

}
