package com.xk.config.infrastructure.repository.draft;

import org.springframework.stereotype.Repository;

import com.xk.config.domain.model.draft.entity.DraftEntity;
import com.xk.config.domain.repository.draft.DraftRootQueryRepository;
import com.xk.config.infrastructure.data.persistence.draft.TDraftMapper;
import com.xk.config.infrastructure.data.po.draft.TDraft;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;

@Repository
@RequiredArgsConstructor
public class DraftRootQueryRepositoryImpl implements DraftRootQueryRepository {

    private final TDraftMapper tDraftMapper;
    private final Converter converter;

    @Override
    public Flux<DraftEntity> searchByCondition(DraftEntity draftEntity) {
        return this.find(converter.convert(draftEntity, TDraft.class),
                tDraftMapper::searchByCondition, DraftEntity.class, this.converter::convert);
    }
}
