package com.xk.config.infrastructure.repository.draft;

import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.config.domain.model.draft.DraftRoot;
import com.xk.config.domain.repository.draft.DraftRootRepository;
import com.xk.config.infrastructure.data.persistence.draft.TDraftMapper;
import com.xk.config.infrastructure.data.po.draft.TDraft;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class DraftRootRepositoryImpl implements DraftRootRepository {

    private final TDraftMapper tDraftMapper;
    private final Converter converter;

    @Override
    public Mono<Void> save(DraftRoot root) {
        return this.save(root.getDraftEntity(), TDraft.class, this.converter::convert,
                this.tDraftMapper::insertSelective);
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(DraftRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(DraftRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(DraftRoot root) {
        return this.remove(root.getDraftEntity(), TDraft.class, this.converter::convert,
                this.tDraftMapper::deleteByPrimaryKey);
    }
}
