<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.config.infrastructure.data.persistence.draft.TDraftMapper">

    <resultMap id="BaseResultMap" type="com.xk.config.infrastructure.data.po.draft.TDraft">
        <id property="draftId" column="draft_id" jdbcType="BIGINT"/>
        <result property="draftBusinessType" column="draft_business_type" jdbcType="TINYINT"/>
        <result property="businessId" column="business_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="createId" column="create_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateId" column="update_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        draft_id
        ,draft_business_type,business_id,
        user_id,content,status,
        deleted,create_id,create_time,
        update_id,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.config.infrastructure.data.po.draft.TDraft"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_draft
        where draft_id = #{draftId,jdbcType=BIGINT}
    </select>

    <select id="searchByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_draft
        where user_id = #{userId,jdbcType=BIGINT} AND draft_business_type = #{draftBusinessType,jdbcType=TINYINT}
        <if test="businessId != null">
            AND business_id = #{businessId,jdbcType=BIGINT}
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.config.infrastructure.data.po.draft.TDraft">
        delete
        from t_draft
        where draft_id = #{draftId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="draft_id" keyProperty="draftId"
            parameterType="com.xk.config.infrastructure.data.po.draft.TDraft" useGeneratedKeys="true">
        insert into t_draft
        ( draft_id, draft_business_type, business_id
        , user_id, content, status
        , deleted, create_id, create_time
        , update_id, update_time)
        values ( #{draftId,jdbcType=BIGINT}, #{draftBusinessType,jdbcType=TINYINT}, #{businessId,jdbcType=BIGINT}
               , #{userId,jdbcType=BIGINT}, #{content,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}
               , #{deleted,jdbcType=TINYINT}, #{createId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}
               , #{updateId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="draft_id" keyProperty="draftId"
            parameterType="com.xk.config.infrastructure.data.po.draft.TDraft" useGeneratedKeys="true">
        insert into t_draft
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="draftId != null">draft_id,</if>
            <if test="draftBusinessType != null">draft_business_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="deleted != null">deleted,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="draftId != null">#{draftId,jdbcType=BIGINT},</if>
            <if test="draftBusinessType != null">#{draftBusinessType,jdbcType=TINYINT},</if>
            <if test="businessId != null">#{businessId,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="content != null">#{content,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.config.infrastructure.data.po.draft.TDraft">
        update t_draft
        <set>
            <if test="draftBusinessType != null">
                draft_business_type = #{draftBusinessType,jdbcType=TINYINT},
            </if>
            <if test="businessId != null">
                business_id = #{businessId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where draft_id = #{draftId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.config.infrastructure.data.po.draft.TDraft">
        update t_draft
        set draft_business_type = #{draftBusinessType,jdbcType=TINYINT},
            business_id         = #{businessId,jdbcType=BIGINT},
            user_id             = #{userId,jdbcType=BIGINT},
            content             = #{content,jdbcType=VARCHAR},
            status              = #{status,jdbcType=TINYINT},
            deleted             = #{deleted,jdbcType=TINYINT},
            create_id           = #{createId,jdbcType=BIGINT},
            create_time         = #{createTime,jdbcType=TIMESTAMP},
            update_id           = #{updateId,jdbcType=BIGINT},
            update_time         = #{updateTime,jdbcType=TIMESTAMP}
        where draft_id = #{draftId,jdbcType=BIGINT}
    </update>
</mapper>
