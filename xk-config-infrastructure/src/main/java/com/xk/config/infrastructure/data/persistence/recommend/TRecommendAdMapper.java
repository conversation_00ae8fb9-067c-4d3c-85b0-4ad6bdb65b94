package com.xk.config.infrastructure.data.persistence.recommend;

import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.config.infrastructure.data.po.recommend.TRecommendAd;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_recommend_ad(全局轮播图广告配置)】的数据库操作Mapper
* @createDate 2025-06-07 14:54:10
* @Entity com.xk.config.infrastructure.data.po.recommend.TRecommendAd
*/
@Repository
@Table("t_recommend_ad")
public interface TRecommendAdMapper {

    int deleteByPrimaryKey(TRecommendAd  record);

    int insert(TRecommendAd record);

    int insertSelective(TRecommendAd record);

    TRecommendAd selectByPrimaryKey(TRecommendAd  record);

    int updateByPrimaryKeySelective(TRecommendAd record);

    int updateByPrimaryKey(TRecommendAd record);

    List<TRecommendAd> searchRecommend(Pagination pager);

}
