package com.xk.config.infrastructure.data.persistence.draft;

import java.util.List;

import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.config.infrastructure.data.po.draft.TDraft;

/**
 * <AUTHOR>
 * @description 针对表【t_draft(草稿箱表)】的数据库操作Mapper
 * @createDate 2025-07-23 10:59:49
 * @Entity com.xk.config.infrastructure.data.po.draft.TDraft
 */
@Repository
@Table("t_draft")
public interface TDraftMapper {

    int deleteByPrimaryKey(TDraft record);

    int insert(TDraft record);

    int insertSelective(TDraft record);

    TDraft selectByPrimaryKey(TDraft record);

    int updateByPrimaryKeySelective(TDraft record);

    int updateByPrimaryKey(TDraft record);

    List<TDraft> searchByCondition(TDraft record);
}
