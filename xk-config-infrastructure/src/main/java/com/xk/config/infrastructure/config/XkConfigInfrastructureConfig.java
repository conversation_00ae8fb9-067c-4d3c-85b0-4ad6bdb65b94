package com.xk.config.infrastructure.config;

import com.myco.mydata.infrastructure.data.annotation.Repository;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author: killer
 **/
@Configuration
@MapperScan(basePackages = "com.xk.config.infrastructure.data.persistence", annotationClass = Repository.class, sqlSessionFactoryRef = "defaultSqlSessionFactory")
@ComponentScan({"com.xk.config.infrastructure.convertor"
        , "com.xk.config.infrastructure.cache.dao"
        , "com.xk.config.infrastructure.repository"
        , "com.xk.config.infrastructure.service"
        , "com.xk.config.infrastructure.adapter"})
public class XkConfigInfrastructureConfig {
}
