package com.xk.config.infrastructure.data.po.draft;

import java.util.Date;

import com.myco.framework.cache.CacheLevel;
import com.myco.framework.cache.annotations.Cache;
import com.xk.config.domain.model.draft.entity.DraftEntity;
import com.xk.config.infrastructure.convertor.draft.DraftBusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 草稿箱表
 *
 * @TableName t_draft
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Cache(level = CacheLevel.REDIS)
@AutoMappers({@AutoMapper(target = DraftEntity.class,
        uses = {CommonStatusEnumConvertor.class, DraftBusinessTypeEnumConvertor.class})})
public class TDraft {
    /**
     * 草稿ID
     */
    private Long draftId;

    /**
     * 草稿类型 1-商品
     */
    private Integer draftBusinessType;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 草稿内容
     */
    private String content;

    /**
     * 状态枚举值
     */
    private Integer status;

    /**
     * 逻辑删除: 0-未删除, 1-已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    @AutoMappings({@AutoMapping(targetClass = DraftEntity.class, target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = DraftEntity.class, target = "createValObj.createTime")})
    private Date createTime;

    /**
     * 更新人
     */
    @AutoMappings({@AutoMapping(targetClass = DraftEntity.class, target = "updateValObj.updateId")})
    private Long updateId;

    /**
     * 更新时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = DraftEntity.class, target = "updateValObj.updateTime")})
    private Date updateTime;
}
