package com.xk.config.infrastructure.convertor.draft;


import com.xk.config.enums.draft.DraftBusinessTypeEnum;

public class DraftBusinessTypeEnumConvertor {

    private DraftBusinessTypeEnumConvertor() {}

    public static DraftBusinessTypeEnum map(Integer code) {
        if (code == null)
            return null;
        return DraftBusinessTypeEnum.getByCode(code);
    }

    public static Integer map(DraftBusinessTypeEnum value) {
        if (value == null)
            return null;
        return value.getCode();
    }
}
