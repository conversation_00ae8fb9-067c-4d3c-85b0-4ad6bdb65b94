<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.config.infrastructure.data.persistence.recommend.TRecommendAdMapper">

    <resultMap id="BaseResultMap" type="com.xk.config.infrastructure.data.po.recommend.TRecommendAd">
        <id property="recommendAdId" column="recommend_ad_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="recommendType" column="recommend_type" jdbcType="TINYINT"/>
        <result property="adPic" column="ad_pic" jdbcType="VARCHAR"/>
        <result property="link" column="link" jdbcType="VARCHAR"/>
        <result property="jumpType" column="jump_type" jdbcType="TINYINT"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="isShow" column="is_show" jdbcType="TINYINT"/>
        <result property="createId" column="create_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateId" column="update_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        recommend_ad_id
        ,name,recommend_type,
        ad_pic,link,jump_type,
        sort,is_show,create_id,
        create_time,update_id,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.config.infrastructure.data.po.recommend.TRecommendAd"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_recommend_ad
        where recommend_ad_id = #{recommendAdId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.config.infrastructure.data.po.recommend.TRecommendAd">
        delete
        from t_recommend_ad
        where recommend_ad_id = #{recommendAdId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="recommend_ad_id" keyProperty="recommendAdId"
            parameterType="com.xk.config.infrastructure.data.po.recommend.TRecommendAd" useGeneratedKeys="true">
        insert into t_recommend_ad
        ( recommend_ad_id, name, recommend_type
        , ad_pic, link, jump_type
        , sort, is_show, create_id
        , create_time, update_id, update_time)
        values ( #{recommendAdId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{recommendType,jdbcType=TINYINT}
               , #{adPic,jdbcType=VARCHAR}, #{link,jdbcType=VARCHAR}, #{jumpType,jdbcType=TINYINT}
               , #{sort,jdbcType=INTEGER}, #{isShow,jdbcType=TINYINT}, #{createId,jdbcType=BIGINT}
               , #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="recommend_ad_id" keyProperty="recommendAdId"
            parameterType="com.xk.config.infrastructure.data.po.recommend.TRecommendAd" useGeneratedKeys="true">
        insert into t_recommend_ad
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recommendAdId != null">recommend_ad_id,</if>
            <if test="name != null">name,</if>
            <if test="recommendType != null">recommend_type,</if>
            <if test="adPic != null">ad_pic,</if>
            <if test="link != null">link,</if>
            <if test="jumpType != null">jump_type,</if>
            <if test="sort != null">sort,</if>
            <if test="isShow != null">is_show,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recommendAdId != null">#{recommendAdId,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="recommendType != null">#{recommendType,jdbcType=TINYINT},</if>
            <if test="adPic != null">#{adPic,jdbcType=VARCHAR},</if>
            <if test="link != null">#{link,jdbcType=VARCHAR},</if>
            <if test="jumpType != null">#{jumpType,jdbcType=TINYINT},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xk.config.infrastructure.data.po.recommend.TRecommendAd">
        update t_recommend_ad
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="recommendType != null">
                recommend_type = #{recommendType,jdbcType=TINYINT},
            </if>
            <if test="adPic != null">
                ad_pic = #{adPic,jdbcType=VARCHAR},
            </if>
            <if test="link != null">
                link = #{link,jdbcType=VARCHAR},
            </if>
            <if test="jumpType != null">
                jump_type = #{jumpType,jdbcType=TINYINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="isShow != null">
                is_show = #{isShow,jdbcType=TINYINT},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where recommend_ad_id = #{recommendAdId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.config.infrastructure.data.po.recommend.TRecommendAd">
        update t_recommend_ad
        set name           = #{name,jdbcType=VARCHAR},
            recommend_type = #{recommendType,jdbcType=TINYINT},
            ad_pic         = #{adPic,jdbcType=VARCHAR},
            link           = #{link,jdbcType=VARCHAR},
            jump_type      = #{jumpType,jdbcType=TINYINT},
            sort           = #{sort,jdbcType=INTEGER},
            is_show        = #{isShow,jdbcType=TINYINT},
            create_id      = #{createId,jdbcType=BIGINT},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            update_id      = #{updateId,jdbcType=BIGINT},
            update_time    = #{updateTime,jdbcType=TIMESTAMP}
        where recommend_ad_id = #{recommendAdId,jdbcType=BIGINT}
    </update>

    <select id="searchRecommend" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_recommend_ad
        <where>
            <if test="name != null and name != ''">
                and name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="recommendType != null and recommendType != ''">
                and recommend_type = #{recommendType}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>
