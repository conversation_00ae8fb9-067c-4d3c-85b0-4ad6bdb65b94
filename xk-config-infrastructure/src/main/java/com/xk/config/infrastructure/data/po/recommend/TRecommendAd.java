package com.xk.config.infrastructure.data.po.recommend;

import java.util.Date;

import com.myco.framework.cache.CacheLevel;
import com.myco.framework.cache.annotations.Cache;
import com.xk.config.domain.model.recommend.RecommendEntity;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 全局轮播图广告配置
 * 
 * @TableName t_recommend_ad
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMapper(target = RecommendEntity.class)
@Cache(level = CacheLevel.REDIS)
public class TRecommendAd {
    /**
     * 推荐广告主键
     */
    private Long recommendAdId;

    /**
     * 广告名称
     */
    private String name;

    /**
     * 推荐类型: 1首页banner 2商城商品banner
     */
    private Integer recommendType;

    /**
     * 图片链接
     */
    private String adPic;

    /**
     * 链接
     */
    private String link;

    /**
     * 跳转类型: 1内链 2外链
     */
    private Integer jumpType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否显示版块：0不显示；1显示；
     */
    private Integer isShow;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;
}
