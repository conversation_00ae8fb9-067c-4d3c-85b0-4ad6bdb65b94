package com.xk.search.interfaces.dto.req.logistics;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/8/6 17:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppDetailReqDto extends RequireSessionDto {

    /**
     * 物流订单ID
     */
    private Long logisticsOrderId;

    /**
     * 物流订单类型 1-商城 2-物料 3-商品 4-赠品
     */
    private Integer logisticsOrderType;

}
