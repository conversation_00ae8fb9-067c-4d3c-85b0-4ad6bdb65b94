package com.xk.search.interfaces.api.query.order;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.search.interfaces.dto.req.order.*;
import com.xk.search.interfaces.dto.rsp.order.AppLogisticsDetailResDto;
import com.xk.search.interfaces.dto.rsp.order.OrderStatusCountResDto;
import com.xk.search.interfaces.query.order.OrderQueryService;

import reactor.core.publisher.Mono;

/**
 * APP-订单搜素
 */
@Controller
@RequestMapping("/search/app/order")
public interface OrderQueryDocService extends OrderQueryService {

    /**
     * 商城订单搜索查询
     * 
     * @param dtoMono dtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/mall")
    Mono<Pagination> searchMallOrder(@RequestBody Mono<OrderSearchByMallReqDto> dtoMono);

    /**
     * 物料订单搜索查询
     * 
     * @param dtoMono dtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/material")
    Mono<Pagination> searchMaterialOrder(@RequestBody Mono<OrderSearchByMaterialReqDto> dtoMono);

    /**
     * 商品订单搜索查询
     *
     * @param dtoMono dtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/merchant")
    Mono<Pagination> searchMerchantOrder(@RequestBody Mono<OrderSearchByMerchantReqDto> dtoMono);


    /**
     * 订单赠品卡密查询-筛选卡设列表
     * 
     * @param dtoMono dtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/gift/corps")
    Mono<Pagination> searchGiftCorpsOrder(@RequestBody Mono<OrderSearchByGiftReqDto> dtoMono);

    /**
     * 商家查询订单 物流详情
     * 
     * @param dtoMono dtoMono
     * @return Mono<AppLogisticsDetailResDto>
     */
    @PostMapping("/logistics/detail")
    Mono<AppLogisticsDetailResDto> logisticsDetail(
            @RequestBody Mono<AppLogisticsDetailReqDto> dtoMono);

    /**
     * 订单数量统计
     *
     * @param mono mono
     * @return Mono<Object>
     */
    @PostMapping("/status/count")
    Mono<OrderStatusCountResDto> searchOrderStatus(@RequestBody Mono<OrderStatusCountReqDto> mono);
}
