package com.xk.search.interfaces.dto.rsp.goods;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/8/6 17:06
 */
@Data
public class MallGoodsSearchRspDto {

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称 分词
     */
    private String goodsName;

    /**
     * 上下架状态
     */
    private Integer listingStatus;

    /**
     * 上架时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date actualUpTime;

    /**
     * 显示状态
     */
    private Integer showStatus;

    /**
     * 实际下架时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date actualDownTime;

    /**
     * 商品描述 分词
     */
    private String goodsDescribe;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 价格
     */
    private Long amount;

    /**
     * 成本价格
     */
    private Long costAmount;

    /**
     * 排序
     */
    private Integer sort;

}
