package com.xk.search.interfaces.dto.req.logistics;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LogisticsOrderNoRequireReq extends RequireSessionDto {

    /**
     * 物流订单ID
     */
    @NotNull(message = "物流订单ID不能为空")
    private Long logisticsOrderId;
}
