package com.xk.search.interfaces.dto.req.goods;

import com.myco.mydata.domain.model.action.session.AbstractSession;

import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/8/6 17:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchGoodsMerchantSoldReqDto extends AbstractSession {

    /**
     * 商品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;

}
