package com.xk.search.interfaces.api.query.order;

import com.xk.search.interfaces.dto.req.order.OrderIndexReqDto;
import com.xk.search.interfaces.service.order.OrderSearchService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

/**
 * 订单搜索 索引管理
 * <AUTHOR>
 * @Date 2024/8/6 17:03
 */
@Controller
@RequestMapping("/search/order")
public interface OrderSearchDocService extends OrderSearchService {

    /**
     * 创建索引 物料订单
     *
     * @return Mono<Void>
     */
    @PostMapping("/material/addIndex")
    Mono<Void> addIndexMaterial(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 更新索引 物料订单
     *
     * @return Mono<Void>
     */
    @PostMapping("/material/updateIndex")
    Mono<Void> updateIndexMaterial(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 删除索引 物料订单
     *
     * @return Mono<Void>
     */
    @PostMapping("/material/deleteIndex")
    Mono<Void> deleteIndexMaterial(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 创建索引 商城商品订单
     *
     * @return Mono<Void>
     */
    @PostMapping("/mall/addIndex")
    Mono<Void> addIndexMall(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 更新索引 商城商品订单
     *
     * @return Mono<Void>
     */
    @PostMapping("/mall/updateIndex")
    Mono<Void> updateIndexMall(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 删除索引 商城商品订单
     *
     * @return Mono<Void>
     */
    @PostMapping("/mall/deleteIndex")
    Mono<Void> deleteIndexMall(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 创建索引 商家商品订单
     *
     * @return Mono<Void>
     */
    @PostMapping("/merchant/addIndex")
    Mono<Void> addIndexMerchant(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 更新索引 商家商品订单
     *
     * @return Mono<Void>
     */
    @PostMapping("/merchant/updateIndex")
    Mono<Void> updateIndexMerchant(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 删除索引 商家商品订单
     *
     * @return Mono<Void>
     */
    @PostMapping("/merchant/deleteIndex")
    Mono<Void> deleteIndexMerchant(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

}
