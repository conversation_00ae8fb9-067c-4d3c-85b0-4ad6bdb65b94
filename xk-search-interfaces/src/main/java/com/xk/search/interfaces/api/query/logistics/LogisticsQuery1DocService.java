package com.xk.search.interfaces.api.query.logistics;

import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.search.interfaces.dto.req.logistics.AppCorpReqDto;
import com.xk.search.interfaces.dto.req.logistics.AppDetailReqDto;
import com.xk.search.interfaces.dto.req.logistics.AppOrderDetailReqDto;
import com.xk.search.interfaces.dto.req.order.*;
import com.xk.search.interfaces.dto.rsp.logistics.AppCorpResDto;
import com.xk.search.interfaces.dto.rsp.logistics.AppDetailResDto;
import com.xk.search.interfaces.dto.rsp.logistics.AppOrderDetailResDto;
import com.xk.search.interfaces.dto.rsp.logistics.AppPtsGoodsRspDto;
import com.xk.search.interfaces.dto.rsp.order.AppAddrResDto;
import com.xk.search.interfaces.dto.rsp.order.AppPtsGoodsGroupRspDto;
import com.xk.search.interfaces.dto.rsp.order.PtsGroupRspDto;

import reactor.core.publisher.Mono;

/**
 * APP-订单搜素/分页对象说明
 */
@Controller
@RequestMapping("/search/app/logistics/doc")
public interface LogisticsQuery1DocService {

    /**
     * 商家查询订单详情
     * @param mono mono
     * @return Mono<AppDetailResDto>
     */
    @PostMapping("/detail")
    Mono<AppDetailResDto> detail(@RequestBody Mono<AppDetailReqDto> mono);

    /**
     * 查询是否有超过10天未发货的订单
     * @param dtoMono dtoMono
     * @return Mono<Boolean>
     */
    @PostMapping("/ptsTenDay")
    Mono<Boolean> searchPtsTenDay(@RequestBody Mono<RequireSessionDto> dtoMono);

    /**
     * 查询待发货订单数
     * @param dtoMono dtoMono
     * @return Mono<Integer>
     */
    @PostMapping("/pts")
    Mono<Integer> searchPts(@RequestBody Mono<RequireSessionDto> dtoMono);

    /**
     * 根据用户进行分组统计查询
     * @param dtoMono dtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/ptsGroup")
    Mono<PtsGroupRspDto> ptsGroup(@RequestBody Mono<AppPtsGroupReqDto> dtoMono);

    /**
     * 根据商品进行分组统计查询
     * @param dtoMono dtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/ptsGoodsGroup")
    Mono<AppPtsGoodsGroupRspDto> ptsGoodsGroup(@RequestBody Mono<AppPtsGoodsGroupReqDto> dtoMono);

    /**
     * 查看物流详情
     * @param mono mono
     * @return Mono<AppDetailResDto>
     */
    @PostMapping("/order/detail")
    Mono<AppOrderDetailResDto> orderDetail(@RequestBody Mono<AppOrderDetailReqDto> mono);

    /**
     * 查询待发货订单商品 根据收货地址+用户ID查询+订单状态
     * @param dtoMono dtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/ptsGoods")
    Mono<AppPtsGoodsRspDto> ptsGoods(@RequestBody Mono<AppPtsGoodsReqDto> dtoMono);

    /**
     * 商品订单搜索查询
     * 
     * @param dtoMono dtoMono
     * @return Mono<Pagination>
     */
    @PostMapping("/merchant")
    Mono<Pagination> searchMerchantOrder(@RequestBody Mono<OrderSearchByMerchantReqDto> dtoMono);

    /**
     * 查询收货地址列表
     * @param dtoMono dtoMono
     * @return Mono<List<AppAddrResDto>>
     */
    @PostMapping("/addr")
    Mono<List<AppAddrResDto>> addr(@RequestBody Mono<AppAddrReqDto> dtoMono);

    /**
     * 商家查询订单
     *
     * @param dtoMono dtoMono
     * @return Mono<List<AppCorpResDto>>
     */
    @PostMapping("/corp")
    Mono<AppCorpResDto> corp(@RequestBody Mono<AppCorpReqDto> dtoMono);

}
