package com.xk.search.interfaces.dto.req.order;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import jakarta.validation.constraints.NotNull;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderStatusCountReqDto extends RequireSessionDto {

    /**
     * 物流订单类型 1-商城 2-物料 3-商品 4-赠品
     */
    @NotNull(message = "物流订单类型不能为空")
    private Integer logisticsOrderType;
}
