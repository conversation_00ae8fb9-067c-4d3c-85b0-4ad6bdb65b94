package com.xk.config.enums.dict;

import com.myco.mydata.domain.enums.util.EnumUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum CorpRuleTypeEnum {
    ANNOUNCE_INFO("公示信息", 1),
    GIFT_RULE("赠品规则", 2),
    PURCHASE_NOTE("购买须知", 3),
    RISK_WARNING("风险提示", 4),
    GIFT_INFO("赠品信息", 5),
    ;

    private final String name;

    private final int code;

    private static final Map<Integer, CorpRuleTypeEnum> MAP = EnumUtil.getEnumMap(CorpRuleTypeEnum.class, CorpRuleTypeEnum::getCode);

    public static CorpRuleTypeEnum getEnum(int code) {
        return MAP.get(code);
    }
}
