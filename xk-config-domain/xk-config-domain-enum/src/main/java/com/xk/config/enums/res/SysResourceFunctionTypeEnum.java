package com.xk.config.enums.res;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/8/16 10:09
 */
@Getter
@RequiredArgsConstructor
public enum SysResourceFunctionTypeEnum {

    INSIDE(1, "内部"),
    ;

    private static final Map<Integer, SysResourceFunctionTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SysResourceFunctionTypeEnum.values())
                .collect(Collectors.toMap(SysResourceFunctionTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static SysResourceFunctionTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
