package com.xk.config.enums.res;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/8/16 10:09
 */
@Getter
@RequiredArgsConstructor
public enum SysResourceJumpTypeEnum {

    INSIDE(1, "内部"),
    OUTSIDE(2, "外部"),
    ;

    private static final Map<Integer, SysResourceJumpTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SysResourceJumpTypeEnum.values())
                .collect(Collectors.toMap(SysResourceJumpTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static SysResourceJumpTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
