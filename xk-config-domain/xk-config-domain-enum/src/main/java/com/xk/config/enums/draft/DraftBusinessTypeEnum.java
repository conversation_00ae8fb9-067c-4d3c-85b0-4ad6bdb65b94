package com.xk.config.enums.draft;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum DraftBusinessTypeEnum {
    PRODUCT(1, "商品"),
    SINGLE_REPORT(2,"单人录入")
    ;
    private static final Map<Integer, DraftBusinessTypeEnum> MAP;

    static {
        MAP = Arrays.stream(DraftBusinessTypeEnum.values())
                .collect(Collectors.toMap(DraftBusinessTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static DraftBusinessTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
