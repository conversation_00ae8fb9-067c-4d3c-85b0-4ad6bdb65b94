<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.config</groupId>
        <artifactId>xk-config-domain</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-config-domain-event</artifactId>
    <packaging>jar</packaging>
    <name>xk-config-domain-event</name>
    <description>xk-config-domain-event</description>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-domain-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.config</groupId>
            <artifactId>xk-config-domain-enum</artifactId>
        </dependency>
    </dependencies>
</project>
