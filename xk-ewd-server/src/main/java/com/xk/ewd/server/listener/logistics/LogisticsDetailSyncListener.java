package com.xk.ewd.server.listener.logistics;


import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;
import com.xk.ewd.domain.event.logistics.LogisticsDetailSyncEvent;

import lombok.RequiredArgsConstructor;

/**
 * 物流单创建
 * 
 * <AUTHOR>
 */
@ConsumerListener(consumeThreadMin = 1, consumeThreadMax = 1)
@RequiredArgsConstructor
public class LogisticsDetailSyncListener
        extends AbstractDispatchMessageListener<LogisticsDetailSyncEvent>
        implements MessageListenerConcurrently {

    private final EventRootService eventRootService;

    @Override
    public void doProcessMessage(LogisticsDetailSyncEvent event) throws Throwable {
        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(false).build();
        eventRootService.handler(eventRoot);
    }

}
