package com.xk.ewd.domain.model.logistics;

import java.util.List;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.ewd.domain.model.logistics.entity.*;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.model.logistics.valobj.GoodsValueObject;
import com.xk.ewd.domain.model.logistics.valobj.TrajectoryValueObject;

import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class LogisticsOrderRoot extends DomainRoot<LogisticsOrderIdentifier> {

    private final CorpEntity corpEntity;

    private final LogisticsOrderEntity logisticsOrderEntity;

    private final OrderAddrEntity orderAddrEntity;

    private final OrderEntity orderEntity;

    private final UserEntity userEntity;

    private final GoodsValueObject goodsValueObject;

    private final List<TrajectoryValueObject> trajectoryValueObjectList;


    @Builder
    public LogisticsOrderRoot(@NonNull LogisticsOrderIdentifier identifier, OrderEntity orderEntity,
                              OrderAddrEntity orderAddrEntity, UserEntity userEntity,
                              GoodsValueObject goodsValueObject, CorpEntity corpEntity,
                              LogisticsOrderEntity logisticsOrderEntity, List<TrajectoryValueObject> trajectoryValueObjectList) {
        super(identifier);
        this.orderEntity = orderEntity;
        this.orderAddrEntity = orderAddrEntity;
        this.userEntity = userEntity;
        this.goodsValueObject = goodsValueObject;
        this.corpEntity = corpEntity;
        this.logisticsOrderEntity = logisticsOrderEntity;
        this.trajectoryValueObjectList = trajectoryValueObjectList;
    }

    @Override
    public Validatable<LogisticsOrderIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
