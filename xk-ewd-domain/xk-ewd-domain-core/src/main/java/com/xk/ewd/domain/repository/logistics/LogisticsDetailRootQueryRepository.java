package com.xk.ewd.domain.repository.logistics;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.ewd.domain.dto.logistics.LogisticsDetailDto;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Date;

public interface LogisticsDetailRootQueryRepository extends IQueryRepository {

    Flux<LogisticsDetailDto> selectById(LogisticsOrderRoot root);

    Mono<Date> selectMaxTimeById(LogisticsOrderRoot root);
}
