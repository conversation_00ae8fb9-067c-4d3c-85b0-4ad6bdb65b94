package com.xk.ewd.domain.model.logistics.valobj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 物流轨迹
 * 
 * <AUTHOR>
 * @Date 2024/8/6 14:29
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrajectoryValueObject {

    /**
     * 物流事件时间
     */
    private Date trackTime;

    /**
     * 物流描述
     */
    private String trackDescribe;

    /**
     * 事件类型
     */
    private String trackType;

}
