package com.xk.ewd.domain.repository.logistics;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.ewd.domain.dto.logistics.AppAddrDto;
import com.xk.ewd.domain.dto.logistics.GiftCorpDto;
import com.xk.ewd.domain.dto.logistics.LogisticsUserViewDto;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface LogisticsOrderRootQueryRepository extends IQueryRepository {


    Flux<LogisticsOrderRoot> selectList(Pagination pagination);

    Flux<AppAddrDto> addr(LogisticsOrderRoot logisticsOrderRoot);

    Flux<GiftCorpDto> giftCorpsOrder(Pagination pagination);

    Flux<LogisticsUserViewDto> userViewCorp(Pagination pagination);

    Mono<LogisticsOrderRoot> selectById(LogisticsOrderRoot record);

    Mono<Long> getWeekTotalRecords();

    Flux<LogisticsOrderRoot> getWeekRecords(Pagination pagination);
}
