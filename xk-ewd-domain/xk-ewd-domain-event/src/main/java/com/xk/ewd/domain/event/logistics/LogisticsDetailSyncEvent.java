package com.xk.ewd.domain.event.logistics;

import java.io.Serializable;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_EWD, domainName = DomainNameEnum.EWD)
public class LogisticsDetailSyncEvent extends AbstractCommonsDomainEvent
        implements Serializable {

    /**
     * 物流订单id
     */
    private final Long logisticsOrderId;

    /**
     * 物流订单类型 1-商城 2-物料 3-商品 4-赠品
     */
    private final Integer logisticsOrderType;

    /**
     * 物流公司名称
     */
    private final String logisticsCorpName;

    /**
     * 物流单号
     */
    private final String logisticsNo;

    /**
     * 收货人手机号
     */
    private final String receivingMobile;

    @Builder
    public LogisticsDetailSyncEvent(@NonNull Long identifier,
                                    Map<String, Object> context, Long logisticsOrderId, Integer logisticsOrderType, String logisticsCorpName, String logisticsNo, String receivingMobile) {
        super(identifier, context);
        this.logisticsOrderId = logisticsOrderId;
        this.logisticsOrderType = logisticsOrderType;
        this.logisticsCorpName = logisticsCorpName;
        this.logisticsNo = logisticsNo;
        this.receivingMobile = receivingMobile;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
