package com.xk.ewd.enums.order;

import com.myco.mydata.domain.enums.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
/**
 * 订单消息枚举
 */
@Getter
@AllArgsConstructor
public enum OrderLogMsgEnum {

    CREATE(1,"创建订单"),
    PAID(2,"订单已支付"),
    USER_CANCEL(3,"用户已取消"),
    TIME_OUT_CANCEL(4,"超时取消"),
    CORP_CANCEL(5,"商户取消"),
    ADMIN_CANCEL(6,"运营取消"),
    REFUND(7,"商品已退款"),
    SHIPPED(8, "已发货"),
    FINISHED(9,"已完成"),
    CREATE_LOGISTICS(10,"创建物流订单")    ;

    private final Integer code;
    private final String msg;


    private static final Map<Integer, OrderLogMsgEnum> MAP = EnumUtil.getEnumMap(OrderLogMsgEnum.class, OrderLogMsgEnum::getCode);

    public static OrderLogMsgEnum getEnum(Integer code) {
        return MAP.get(code);
    }
}
