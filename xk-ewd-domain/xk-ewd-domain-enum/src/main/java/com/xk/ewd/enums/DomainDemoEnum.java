package com.xk.ewd.enums;

import com.myco.mydata.domain.enums.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
/**
 * 领域枚举示例
 */
@Getter
@AllArgsConstructor
public enum DomainDemoEnum {

    ENUM_V1("name", 1),
    ;

    private final String name;

    private final int code;

    private static final Map<Integer, DomainDemoEnum> MAP = EnumUtil.getEnumMap(DomainDemoEnum.class, DomainDemoEnum::getCode);

    public static DomainDemoEnum getEnum(int name) {
        return MAP.get(name);
    }
}
