package com.xk.ewd.enums.order;

import java.util.Map;

import com.myco.mydata.domain.enums.util.EnumUtil;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.enums.order.OrderStatusEnum;
import com.xk.order.enums.payment.RefundStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 领域枚举示例
 */
@Getter
@AllArgsConstructor
public enum OrderListStatusEnum {

    ALL(1, null, null, null, "全部"),
    WAIT_PAID(2, OrderStatusEnum.WAIT_PAID.getCode(), null, null,"待付款"),
    TO_BE_SHIPPED(3, null, LogisticsOrderStatusEnum.TO_BE_SHIPPED.getCode(), null,"待发货"),
    TO_BE_RECEIVED(4, null, LogisticsOrderStatusEnum.TO_BE_RECEIVED.getCode(), null,"待收货"),
    RECEIVED(5, null, LogisticsOrderStatusEnum.RECEIVED.getCode(),null, "已完成"),
    CANCEL(6, OrderStatusEnum.CANCEL.getCode(), null,null, "已取消"),
     REFUND(7, null, null, RefundStatusEnum.PAID.getCode(), "已退款");

    private static final Map<Integer, OrderListStatusEnum> MAP =
            EnumUtil.getEnumMap(OrderListStatusEnum.class, OrderListStatusEnum::getCode);
    private final int code;
    private final Integer orderStatus;
    private final Integer logisticsOrderStatus;
    private final Integer refundStatus;
    private final String desc;

    public static OrderListStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
