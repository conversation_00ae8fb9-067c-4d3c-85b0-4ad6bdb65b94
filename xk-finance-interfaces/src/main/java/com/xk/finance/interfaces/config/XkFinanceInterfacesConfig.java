package com.xk.finance.interfaces.config;

import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import org.springframework.beans.factory.annotation.Value;

/**
 * @author: killer
 **/
public class XkFinanceInterfacesConfig {

    @Bean
    public WebClient xkFinanceWebClient(WebClient.Builder loadBalancedWebClientBuilder) {
        return loadBalancedWebClientBuilder.baseUrl("http://xkFinance").build();
    }

    @Bean
    public HttpServiceProxyFactory xkFinanceHttpServiceProxyFactory(WebClient xkFinanceWebClient) {
        WebClientAdapter adapter = WebClientAdapter.create(xkFinanceWebClient);
        return HttpServiceProxyFactory.builderFor(adapter).build();
    }

}
