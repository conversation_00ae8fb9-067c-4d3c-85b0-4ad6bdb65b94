package com.xk.auth.application.handler.event.corp;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.acct.domain.event.user.CorpUserMobileUpdateEvent;
import com.xk.auth.application.action.command.auth.userrole.UpdateUserRoleCommand;
import com.xk.domain.model.auth.userrole.UserRoleEntity;
import com.xk.domain.service.auth.userrole.UserRoleRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CorpUserMobileUpdateEventHandler
        extends AbstractEventVerticle<CorpUserMobileUpdateEvent> {

    private final UserRoleRootService userRoleRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public Mono<Void> handle(Mono<CorpUserMobileUpdateEvent> mono) {
        return mono.flatMap(event -> userRoleRootService
                .selectByUserId(UserRoleEntity.builder().userId(event.getOldUserId()).build())
                .flatMap(entity -> {
                    entity.setUserId(event.getUserId());
                    return commandDispatcher.executeCommand(Mono.just(entity),
                            UpdateUserRoleCommand.class);
                }));
    }
}
