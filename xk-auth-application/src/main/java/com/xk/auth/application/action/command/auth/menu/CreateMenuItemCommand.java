package com.xk.auth.application.action.command.auth.menu;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.menu.MenuItemEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @Author: liu<PERSON><PERSON><PERSON>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = MenuItemEntity.class)
})
public class CreateMenuItemCommand extends AbstractActionCommand {

    /**
     * 菜单关系id
     */
    private String itemId;

    /**
     * 菜单id
     */
    private Long menuId;
}
