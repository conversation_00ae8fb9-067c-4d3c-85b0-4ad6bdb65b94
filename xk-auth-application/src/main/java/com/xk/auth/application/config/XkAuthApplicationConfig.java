package com.xk.auth.application.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author: killer
 **/
@Configuration
@ComponentScan({"com.xk.auth.application.convertor"
        , "com.xk.auth.application.query"
        , "com.xk.auth.application.service"
        , "com.xk.auth.application.handler"
        , "com.xk.auth.application.task"})
public class XkAuthApplicationConfig {
}
