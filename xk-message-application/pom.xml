<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.message</groupId>
        <artifactId>xk-message</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-message-application</artifactId>
    <packaging>jar</packaging>
    <name>xk-message-application</name>
    <description>xk-message-application</description>
    <properties>
        <mapstruct-plus.mapperPackage>com.xk.message.application.convertor.auto</mapstruct-plus.mapperPackage>
        <mapstruct-plus.adapterPackage>com.xk.message.application.convertor.adapter</mapstruct-plus.adapterPackage>
        <mapstruct-plus.autoConfigPackage>com.xk.message.application.config</mapstruct-plus.autoConfigPackage>
        <mapstruct-plus.adapterClassName>XkMessageApplicationConverterMapperAdapter</mapstruct-plus.adapterClassName>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.message</groupId>
            <artifactId>xk-message-domain-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.message</groupId>
            <artifactId>xk-message-interfaces</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.message</groupId>
            <artifactId>xk-message-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.message</groupId>
            <artifactId>xk-message-infrastructure</artifactId>
        </dependency>
    </dependencies>
</project>
