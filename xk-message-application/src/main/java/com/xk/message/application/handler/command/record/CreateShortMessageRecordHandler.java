package com.xk.message.application.handler.command.record;

import com.xk.message.application.action.command.record.CreateShortMessageRecordCommand;
import com.xk.message.domain.model.record.MessageRecordRoot;
import com.xk.message.domain.model.record.entity.ShortMessageRecordEntity;
import com.xk.message.domain.repository.record.MessageRecordRootRepository;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CreateShortMessageRecordHandler implements IActionCommandHandler<CreateShortMessageRecordCommand, Void> {

    private final MessageRecordRootRepository messageRecordRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateShortMessageRecordCommand> commandMono) {
        return execute(commandMono, ShortMessageRecordEntity.class, converter::convert, entity -> MessageRecordRoot.builder()
                .identifier(LongIdentifier.builder().id(entity.getRecordId()).build())
                .shortMessageRecordEntity(entity)
                .build(), messageRecordRootRepository::createShortMessageRecord);
    }
}
