package com.xk.message.application.handler.query.message;


import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.message.application.action.query.template.BusinessMessageTemplateBySceneQuery;
import com.xk.message.application.dto.message.BusinessMessageTemplateAppDto;
import com.xk.message.domain.model.template.entity.BusinessMessageTemplateEntity;
import com.xk.message.domain.repository.template.MessageTemplateRootQueryRepository;
import com.xk.message.enums.message.MessageBusinessSceneEnum;
import com.xk.message.interfaces.dto.rsp.template.BusinessMessageTemplateRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class BusinessMessageTemplateBySceneQueryHandler
        implements IActionQueryHandler<BusinessMessageTemplateBySceneQuery, BusinessMessageTemplateRspDto> {

    private final MessageTemplateRootQueryRepository messageTemplateRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<BusinessMessageTemplateRspDto> execute(Mono<BusinessMessageTemplateBySceneQuery> query) {
        return execute(query, BusinessMessageTemplateEntity.class, (data, clazz) ->
                        BusinessMessageTemplateEntity.builder()
                                .platformType(PlatformTypeEnum.getByValue(data.getPlatformType()))
                                .businessType(BusinessTypeEnum.getByValue(data.getBusinessType()))
                                .businessScene(MessageBusinessSceneEnum.getByCode(data.getBusinessScene()))
                                .build()
                , messageTemplateRootQueryRepository::findByScene, BusinessMessageTemplateRspDto.class,
                (data, clazz) -> {
                    BusinessMessageTemplateAppDto appDto = this.converter.convert(data,
                            BusinessMessageTemplateAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
