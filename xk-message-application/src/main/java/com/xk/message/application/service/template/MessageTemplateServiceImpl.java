package com.xk.message.application.service.template;

import java.util.Date;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.message.application.action.command.template.*;
import com.xk.message.application.action.query.template.*;
import com.xk.message.application.commons.XkMessageApplicationErrorEnum;
import com.xk.message.application.support.XkMessageApplicationException;
import com.xk.message.domain.model.template.MessageTemplateRoot;
import com.xk.message.domain.support.message.MessageSequenceEnum;
import com.xk.message.enums.message.MessageTypeStatusEnum;
import com.xk.message.interfaces.dto.req.template.*;
import com.xk.message.interfaces.dto.rsp.template.BusinessMessageTemplateRspDto;
import com.xk.message.interfaces.dto.rsp.template.ImMessageTemplateRspDto;
import com.xk.message.interfaces.dto.rsp.template.ShortMessageTemplateRspDto;
import com.xk.message.interfaces.dto.rsp.template.StationMessageTemplateRspDto;
import com.xk.message.interfaces.service.template.MessageTemplateService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class MessageTemplateServiceImpl implements MessageTemplateService {

    private final ActionCommandDispatcher<AbstractActionCommand> commandActionCommandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;
    private final Converter converter;


    @Override
    @BusiCode
    public Mono<Void> createBusinessMessageTemplate(
            Mono<CreateBusinessMessageTemplateReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            BusinessMessageTemplateBySceneQuery businessMessageTemplateBySceneQuery =
                    BusinessMessageTemplateBySceneQuery.builder()
                            .businessScene(dto.getBusinessScene())
                            .businessType(dto.getBusinessType()).platformType(dto.getPlatformType())
                            .build();
            return this.actionQueryDispatcher
                    .process(Mono.just(businessMessageTemplateBySceneQuery),
                            BusinessMessageTemplateBySceneQuery.class,
                            BusinessMessageTemplateRspDto.class)
                    .hasElement().flatMap(hasElement -> {
                        if (hasElement) {
                            return Mono.error(new XkMessageApplicationException(
                                    XkMessageApplicationErrorEnum.MESSAGE_SCENE_ALREADY_EXIST));
                        }
                        CreateBusinessMessageTemplateCommand createBusinessMessageTemplateCommand =
                                this.converter.convert(dto,
                                        CreateBusinessMessageTemplateCommand.class);
                        createBusinessMessageTemplateCommand.setBusinessMessageTemplateId(
                                MessageTemplateRoot.generateIdentifier(
                                        MessageSequenceEnum.M_BUSINESS_MESSAGE_TEMPLATE));
                        createBusinessMessageTemplateCommand.setCreateTime(new Date());
                        createBusinessMessageTemplateCommand
                                .setImEnable(MessageTypeStatusEnum.OFF.getCode());
                        createBusinessMessageTemplateCommand
                                .setShortMessageEnable(MessageTypeStatusEnum.OFF.getCode());
                        createBusinessMessageTemplateCommand
                                .setStationMessage(MessageTypeStatusEnum.OFF.getCode());
                        return this.commandActionCommandDispatcher.process(
                                Mono.just(createBusinessMessageTemplateCommand),
                                CreateBusinessMessageTemplateCommand.class, Void.class);
                    });
        });
    }

    @Override
    @BusiCode
    public Mono<Void> updateBusinessMessageTemplate(
            Mono<UpdateBusinessMessageTemplateReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            BusinessMessageTemplateBySceneQuery businessMessageTemplateBySceneQuery =
                    BusinessMessageTemplateBySceneQuery.builder()
                            .businessScene(dto.getBusinessScene())
                            .businessType(dto.getBusinessType()).platformType(dto.getPlatformType())
                            .build();
            return this.actionQueryDispatcher
                    .process(Mono.just(businessMessageTemplateBySceneQuery),
                            BusinessMessageTemplateBySceneQuery.class,
                            BusinessMessageTemplateRspDto.class)
                    .defaultIfEmpty(new BusinessMessageTemplateRspDto())
                    .flatMap(businessMessageTemplateRspDto -> {
                        if (businessMessageTemplateRspDto
                                .getBusinessMessageTemplateId() != null && businessMessageTemplateRspDto
                                        .getBusinessMessageTemplateId() != dto
                                                .getBusinessMessageTemplateId()) {
                            return Mono.error(new XkMessageApplicationException(
                                    XkMessageApplicationErrorEnum.MESSAGE_SCENE_ALREADY_EXIST));
                        }
                        UpdateBusinessMessageTemplateCommand command = this.converter.convert(dto,
                                UpdateBusinessMessageTemplateCommand.class);
                        command.setUpdateTime(new Date());
                        return this.commandActionCommandDispatcher.process(Mono.just(command),
                                UpdateBusinessMessageTemplateCommand.class, Void.class);
                    });
        });
    }

    @Override
    @BusiCode
    public Mono<Void> deletedBusinessMessageTemplate(
            Mono<BusinessMessageTemplateIdentifierReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            DeletedBusinessMessageTemplateCommand command =
                    DeletedBusinessMessageTemplateCommand.builder()
                            .businessMessageTemplateId(dto.getBusinessMessageTemplateId()).build();
            return this.commandActionCommandDispatcher.process(Mono.just(command),
                    DeletedBusinessMessageTemplateCommand.class, Void.class).then(Mono.defer(() -> {
                        ImMessageTemplateByBusinessTemplateIdQuery query =
                                ImMessageTemplateByBusinessTemplateIdQuery.builder()
                                        .businessMessageTemplateId(
                                                dto.getBusinessMessageTemplateId())
                                        .build();
                        return this.actionQueryManyDispatcher
                                .process(Mono.just(query),
                                        ImMessageTemplateByBusinessTemplateIdQuery.class,
                                        ImMessageTemplateRspDto.class)
                                .hasElements().flatMap(hasElements -> {
                                    if (hasElements) {
                                        return Mono.error(new XkMessageApplicationException(
                                                XkMessageApplicationErrorEnum.BUSINESS_MESSAGE_TEMPLATE_HAVA_DETAIL_TEMPLATE));
                                    }
                                    return Mono.empty();
                                });
                    })).then(Mono.defer(() -> {
                        ShortMessageTemplateByBusinessTemplateIdQuery query =
                                ShortMessageTemplateByBusinessTemplateIdQuery.builder()
                                        .businessMessageTemplateId(
                                                dto.getBusinessMessageTemplateId())
                                        .build();
                        return this.actionQueryManyDispatcher
                                .process(Mono.just(query),
                                        ShortMessageTemplateByBusinessTemplateIdQuery.class,
                                        ShortMessageTemplateRspDto.class)
                                .hasElements().flatMap(hasElements -> {
                                    if (hasElements) {
                                        return Mono.error(new XkMessageApplicationException(
                                                XkMessageApplicationErrorEnum.BUSINESS_MESSAGE_TEMPLATE_HAVA_DETAIL_TEMPLATE));
                                    }
                                    return Mono.empty();
                                });
                    })).then(Mono.defer(() -> {
                        StationMessageTemplateByBusinessTemplateIdQuery query =
                                StationMessageTemplateByBusinessTemplateIdQuery.builder()
                                        .businessMessageTemplateId(
                                                dto.getBusinessMessageTemplateId())
                                        .build();
                        return this.actionQueryManyDispatcher
                                .process(Mono.just(query),
                                        StationMessageTemplateByBusinessTemplateIdQuery.class,
                                        StationMessageTemplateRspDto.class)
                                .hasElements().flatMap(hasElements -> {
                                    if (hasElements) {
                                        return Mono.error(new XkMessageApplicationException(
                                                XkMessageApplicationErrorEnum.BUSINESS_MESSAGE_TEMPLATE_HAVA_DETAIL_TEMPLATE));
                                    }
                                    return Mono.empty();
                                });
                    }));
        });
    }

    @Override
    @BusiCode
    public Mono<Void> createImMessageTemplate(Mono<CreateImMessageTemplateReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            BusinessMessageTemplateInfoQuery businessMessageTemplateInfoQuery =
                    BusinessMessageTemplateInfoQuery.builder()
                            .businessMessageTemplateId(dto.getBusinessMessageTemplateId()).build();
            return this.actionQueryDispatcher.process(Mono.just(businessMessageTemplateInfoQuery),
                    BusinessMessageTemplateInfoQuery.class, BusinessMessageTemplateRspDto.class)
                    .flatMap(businessMessageTemplateRspDto -> {
                        CreateImMessageTemplateCommand createImMessageTemplateCommand =
                                this.converter.convert(dto, CreateImMessageTemplateCommand.class);
                        createImMessageTemplateCommand.setCreateTime(new Date());
                        createImMessageTemplateCommand.setTemplateId(MessageTemplateRoot
                                .generateIdentifier(MessageSequenceEnum.M_IM_MESSAGE_TEMPLATE));
                        createImMessageTemplateCommand
                                .setBusinessType(businessMessageTemplateRspDto.getBusinessType());
                        createImMessageTemplateCommand
                                .setPlatformType(businessMessageTemplateRspDto.getPlatformType());
                        return this.commandActionCommandDispatcher.process(
                                Mono.just(createImMessageTemplateCommand),
                                CreateImMessageTemplateCommand.class, Void.class);
                    });
        });
    }

    @Override
    @BusiCode
    public Mono<Void> updateImMessageTemplate(Mono<UpdateImMessageTemplateReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            UpdateImMessageTemplateCommand updateImMessageTemplateCommand =
                    this.converter.convert(dto, UpdateImMessageTemplateCommand.class);
            return this.commandActionCommandDispatcher
                    .process(Mono.just(updateImMessageTemplateCommand),
                            UpdateImMessageTemplateCommand.class, Void.class)
                    .then(Mono.defer(() -> {
                        if (StringUtils.isBlank(dto.getTitle()) || StringUtils
                                .isBlank(dto.getTemplateContent())) {
                            ImMessageTemplateInfoQuery imMessageTemplateInfoQuery =
                                    ImMessageTemplateInfoQuery.builder()
                                            .templateId(dto.getTemplateId()).build();
                            return this.actionQueryDispatcher
                                    .process(Mono.just(imMessageTemplateInfoQuery),
                                            ImMessageTemplateInfoQuery.class,
                                            ImMessageTemplateRspDto.class)
                                    .flatMap(imMessageTemplateRspDto -> {
                                        BusinessMessageTemplateInfoQuery businessMessageTemplateInfoQuery =
                                                BusinessMessageTemplateInfoQuery.builder()
                                                        .businessMessageTemplateId(
                                                                imMessageTemplateRspDto
                                                                        .getBusinessMessageTemplateId())
                                                        .build();
                                        return this.actionQueryDispatcher
                                                .process(
                                                        Mono.just(businessMessageTemplateInfoQuery),
                                                        BusinessMessageTemplateInfoQuery.class,
                                                        BusinessMessageTemplateRspDto.class)
                                                .flatMap(businessMessageTemplateRspDto -> {
                                                    if (Objects.equals(
                                                            businessMessageTemplateRspDto
                                                                    .getStationMessage(),
                                                            MessageTypeStatusEnum.ON.getCode())) {
                                                        return Mono.error(
                                                                new XkMessageApplicationException(
                                                                        XkMessageApplicationErrorEnum.MESSAGE_TEMPLATE_CANT_UPDATE));
                                                    }
                                                    return Mono.empty();
                                                });
                                    });
                        }
                        return Mono.empty();
                    }));
        });
    }

    @Override
    @BusiCode
    public Mono<Void> createShortMessageTemplate(Mono<CreateShortMessageTemplateReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            BusinessMessageTemplateInfoQuery businessMessageTemplateInfoQuery =
                    BusinessMessageTemplateInfoQuery.builder()
                            .businessMessageTemplateId(dto.getBusinessMessageTemplateId()).build();
            return this.actionQueryDispatcher.process(Mono.just(businessMessageTemplateInfoQuery),
                    BusinessMessageTemplateInfoQuery.class, BusinessMessageTemplateRspDto.class)
                    .flatMap(businessMessageTemplateRspDto -> {
                        CreateShortMessageTemplateCommand createShortMessageTemplateCommand =
                                this.converter.convert(dto,
                                        CreateShortMessageTemplateCommand.class);
                        createShortMessageTemplateCommand.setCreateTime(new Date());
                        createShortMessageTemplateCommand.setTemplateId(MessageTemplateRoot
                                .generateIdentifier(MessageSequenceEnum.M_SHORT_MESSAGE_TEMPLATE));
                        createShortMessageTemplateCommand
                                .setBusinessType(businessMessageTemplateRspDto.getBusinessType());
                        createShortMessageTemplateCommand
                                .setPlatformType(businessMessageTemplateRspDto.getPlatformType());
                        return this.commandActionCommandDispatcher.process(
                                Mono.just(createShortMessageTemplateCommand),
                                CreateShortMessageTemplateCommand.class, Void.class);
                    });
        });
    }

    @Override
    @BusiCode
    public Mono<Void> updateShortMessageTemplate(Mono<UpdateShortMessageTemplateReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            UpdateShortMessageTemplateCommand updateShortMessageTemplateCommand =
                    this.converter.convert(dto, UpdateShortMessageTemplateCommand.class);
            return this.commandActionCommandDispatcher.process(
                    Mono.just(updateShortMessageTemplateCommand),
                    UpdateShortMessageTemplateCommand.class, Void.class);
        });
    }

    @Override
    @BusiCode
    public Mono<Void> deletedShortMessageTemplate(Mono<DeletedShortMessageTemplateReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            ShortMessageTemplateInfoQuery shortMessageTemplateInfoQuery =
                    ShortMessageTemplateInfoQuery.builder().templateId(dto.getTemplateId()).build();
            return this.actionQueryDispatcher
                    .process(Mono.just(shortMessageTemplateInfoQuery),
                            ShortMessageTemplateInfoQuery.class, ShortMessageTemplateRspDto.class)
                    .flatMap(shortMessageTemplateRspDto -> {
                        DeletedShortMessageTemplateCommand deletedShortMessageTemplateCommand =
                                this.converter.convert(dto,
                                        DeletedShortMessageTemplateCommand.class);
                        return this.commandActionCommandDispatcher
                                .process(Mono.just(deletedShortMessageTemplateCommand),
                                        DeletedShortMessageTemplateCommand.class, Void.class)
                                .then(Mono.defer(() -> {
                                    BusinessMessageTemplateInfoQuery businessMessageTemplateInfoQuery =
                                            BusinessMessageTemplateInfoQuery.builder()
                                                    .businessMessageTemplateId(
                                                            shortMessageTemplateRspDto
                                                                    .getBusinessMessageTemplateId())
                                                    .build();
                                    return this.actionQueryDispatcher
                                            .process(Mono.just(businessMessageTemplateInfoQuery),
                                                    BusinessMessageTemplateInfoQuery.class,
                                                    BusinessMessageTemplateRspDto.class)
                                            .flatMap(businessMessageTemplateRspDto -> {
                                                if (Objects.equals(
                                                        businessMessageTemplateRspDto
                                                                .getShortMessageEnable(),
                                                        MessageTypeStatusEnum.OFF.getCode())) {
                                                    return Mono.empty();
                                                }
                                                ShortMessageTemplateByBusinessTemplateIdQuery query =
                                                        ShortMessageTemplateByBusinessTemplateIdQuery
                                                                .builder()
                                                                .businessMessageTemplateId(
                                                                        shortMessageTemplateRspDto
                                                                                .getBusinessMessageTemplateId())
                                                                .build();
                                                return this.actionQueryManyDispatcher.process(
                                                        Mono.just(query),
                                                        ShortMessageTemplateByBusinessTemplateIdQuery.class,
                                                        ShortMessageTemplateRspDto.class)
                                                        .hasElements().flatMap(hasElements -> {
                                                            if (!hasElements) {
                                                                return Mono.error(
                                                                        new XkMessageApplicationException(
                                                                                XkMessageApplicationErrorEnum.SHORT_MESSAGE_TEMPLATE_CANT_DELETE));
                                                            }
                                                            return Mono.empty();
                                                        });
                                            });
                                }));
                    });
        });
    }

    @Override
    @BusiCode
    public Mono<Void> createStationMessageTemplate(
            Mono<CreateStationMessageTemplateReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            BusinessMessageTemplateInfoQuery businessMessageTemplateInfoQuery =
                    BusinessMessageTemplateInfoQuery.builder()
                            .businessMessageTemplateId(dto.getBusinessMessageTemplateId()).build();
            return this.actionQueryDispatcher.process(Mono.just(businessMessageTemplateInfoQuery),
                    BusinessMessageTemplateInfoQuery.class, BusinessMessageTemplateRspDto.class)
                    .flatMap(businessMessageTemplateRspDto -> {
                        CreateStationMessageTemplateCommand createStationMessageTemplateCommand =
                                this.converter.convert(dto,
                                        CreateStationMessageTemplateCommand.class);
                        createStationMessageTemplateCommand.setCreateTime(new Date());
                        createStationMessageTemplateCommand
                                .setTemplateId(MessageTemplateRoot.generateIdentifier(
                                        MessageSequenceEnum.M_STATION_MESSAGE_TEMPLATE));
                        createStationMessageTemplateCommand
                                .setBusinessType(businessMessageTemplateRspDto.getBusinessType());
                        createStationMessageTemplateCommand
                                .setPlatformType(businessMessageTemplateRspDto.getPlatformType());
                        return this.commandActionCommandDispatcher.process(
                                Mono.just(createStationMessageTemplateCommand),
                                CreateStationMessageTemplateCommand.class, Void.class);
                    });
        });
    }

    @Override
    @BusiCode
    public Mono<Void> updateStationMessageTemplate(
            Mono<UpdateStationMessageTemplateReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            UpdateStationMessageTemplateCommand updateStationMessageTemplateCommand =
                    this.converter.convert(dto, UpdateStationMessageTemplateCommand.class);
            return this.commandActionCommandDispatcher
                    .process(Mono.just(updateStationMessageTemplateCommand),
                            UpdateStationMessageTemplateCommand.class, Void.class)
                    .then(Mono.defer(() -> {
                        if (StringUtils.isBlank(dto.getTitle()) || StringUtils
                                .isBlank(dto.getTemplateContent())) {
                            StationMessageTemplateInfoQuery stationMessageTemplateInfoQuery =
                                    StationMessageTemplateInfoQuery.builder()
                                            .templateId(dto.getTemplateId()).build();
                            return this.actionQueryDispatcher
                                    .process(Mono.just(stationMessageTemplateInfoQuery),
                                            StationMessageTemplateInfoQuery.class,
                                            StationMessageTemplateRspDto.class)
                                    .flatMap(stationMessageTemplateRspDto -> {
                                        BusinessMessageTemplateInfoQuery businessMessageTemplateInfoQuery =
                                                BusinessMessageTemplateInfoQuery.builder()
                                                        .businessMessageTemplateId(
                                                                stationMessageTemplateRspDto
                                                                        .getBusinessMessageTemplateId())
                                                        .build();
                                        return this.actionQueryDispatcher
                                                .process(
                                                        Mono.just(businessMessageTemplateInfoQuery),
                                                        BusinessMessageTemplateInfoQuery.class,
                                                        BusinessMessageTemplateRspDto.class)
                                                .flatMap(businessMessageTemplateRspDto -> {
                                                    if (Objects.equals(
                                                            businessMessageTemplateRspDto
                                                                    .getStationMessage(),
                                                            MessageTypeStatusEnum.ON.getCode())) {
                                                        return Mono.error(
                                                                new XkMessageApplicationException(
                                                                        XkMessageApplicationErrorEnum.MESSAGE_TEMPLATE_CANT_UPDATE));
                                                    }

                                                    if (StringUtils.isBlank(
                                                            dto.getTitle()) && StringUtils.isBlank(
                                                                    dto.getTemplateContent())) {
                                                        DeletedStationMessageTemplateCommand deletedStationMessageTemplateCommand =
                                                                DeletedStationMessageTemplateCommand
                                                                        .builder()
                                                                        .templateId(
                                                                                dto.getTemplateId())
                                                                        .businessMessageTemplateId(
                                                                                businessMessageTemplateRspDto
                                                                                        .getBusinessMessageTemplateId())
                                                                        .build();
                                                        return this.commandActionCommandDispatcher
                                                                .process(Mono.just(
                                                                        deletedStationMessageTemplateCommand),
                                                                        DeletedStationMessageTemplateCommand.class,
                                                                        Void.class);
                                                    }

                                                    return Mono.empty();
                                                });
                                    });
                        }
                        return Mono.empty();
                    }));
        });
    }

    @BusiCode
    @Override
    public Mono<Void> commitOpenShortMessage(
            Mono<BusinessMessageTemplateIdentifierReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            ShortMessageTemplateByBusinessTemplateIdQuery query =
                    ShortMessageTemplateByBusinessTemplateIdQuery.builder()
                            .businessMessageTemplateId(dto.getBusinessMessageTemplateId()).build();
            return this.actionQueryManyDispatcher
                    .process(Mono.just(query), ShortMessageTemplateByBusinessTemplateIdQuery.class,
                            ShortMessageTemplateRspDto.class)
                    .hasElements().flatMap(hasElements -> {
                        if (!hasElements) {
                            return Mono.error(new XkMessageApplicationException(
                                    XkMessageApplicationErrorEnum.SHORT_MESSAGE_CANT_ENABLE));
                        }
                        UpdateBusinessMessageTemplateCommand command =
                                UpdateBusinessMessageTemplateCommand.builder()
                                        .businessMessageTemplateId(
                                                dto.getBusinessMessageTemplateId())
                                        .shortMessageEnable(MessageTypeStatusEnum.ON.getCode())
                                        .build();
                        command.setUpdateTime(new Date());
                        return this.commandActionCommandDispatcher.process(Mono.just(command),
                                UpdateBusinessMessageTemplateCommand.class, Void.class);
                    });
        });
    }

    @BusiCode
    @Override
    public Mono<Void> commitCloseShortMessage(
            Mono<BusinessMessageTemplateIdentifierReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            UpdateBusinessMessageTemplateCommand command = UpdateBusinessMessageTemplateCommand
                    .builder().businessMessageTemplateId(dto.getBusinessMessageTemplateId())
                    .shortMessageEnable(MessageTypeStatusEnum.OFF.getCode()).build();
            command.setUpdateTime(new Date());
            return this.commandActionCommandDispatcher.process(Mono.just(command),
                    UpdateBusinessMessageTemplateCommand.class, Void.class);
        });
    }

    @BusiCode
    @Override
    public Mono<Void> commitOpenStationMessage(
            Mono<BusinessMessageTemplateIdentifierReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            StationMessageTemplateByBusinessTemplateIdQuery query =
                    StationMessageTemplateByBusinessTemplateIdQuery.builder()
                            .businessMessageTemplateId(dto.getBusinessMessageTemplateId()).build();
            return this.actionQueryManyDispatcher
                    .process(Mono.just(query),
                            StationMessageTemplateByBusinessTemplateIdQuery.class,
                            StationMessageTemplateRspDto.class)
                    .defaultIfEmpty(new StationMessageTemplateRspDto()).take(1).single()
                    .flatMap(stationMessageTemplateRspDto -> {
                        if (stationMessageTemplateRspDto.getTemplateId() == null) {
                            return Mono.error(new XkMessageApplicationException(
                                    XkMessageApplicationErrorEnum.STATION_MESSAGE_CANT_ENABLE));
                        }
                        if (StringUtils.isBlank(
                                stationMessageTemplateRspDto.getTitle()) || StringUtils.isBlank(
                                        stationMessageTemplateRspDto.getTemplateContent())) {
                            return Mono.error(new XkMessageApplicationException(
                                    XkMessageApplicationErrorEnum.STATION_MESSAGE_CANT_ENABLE));
                        }

                        UpdateBusinessMessageTemplateCommand command =
                                UpdateBusinessMessageTemplateCommand.builder()
                                        .businessMessageTemplateId(
                                                dto.getBusinessMessageTemplateId())
                                        .stationMessage(MessageTypeStatusEnum.ON.getCode()).build();
                        command.setUpdateTime(new Date());
                        return this.commandActionCommandDispatcher.process(Mono.just(command),
                                UpdateBusinessMessageTemplateCommand.class, Void.class);
                    });
        });
    }

    @BusiCode
    @Override
    public Mono<Void> commitCloseStationMessage(
            Mono<BusinessMessageTemplateIdentifierReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            UpdateBusinessMessageTemplateCommand command = UpdateBusinessMessageTemplateCommand
                    .builder().businessMessageTemplateId(dto.getBusinessMessageTemplateId())
                    .stationMessage(MessageTypeStatusEnum.OFF.getCode()).build();
            command.setUpdateTime(new Date());
            return this.commandActionCommandDispatcher.process(Mono.just(command),
                    UpdateBusinessMessageTemplateCommand.class, Void.class);
        });
    }
}
