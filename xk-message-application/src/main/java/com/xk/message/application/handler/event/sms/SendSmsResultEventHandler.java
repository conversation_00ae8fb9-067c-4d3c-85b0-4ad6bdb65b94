package com.xk.message.application.handler.event.sms;

import java.util.Date;
import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.message.application.action.command.record.CreateShortMessageRecordCommand;
import com.xk.message.domain.model.template.entity.ShortMessageTemplateEntity;
import com.xk.message.domain.model.record.MessageRecordRoot;
import com.xk.message.domain.service.template.MessageTemplateRootService;
import com.xk.message.domain.support.message.MessageSequenceEnum;
import com.xk.tp.domain.event.sms.SendSmsResultEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class SendSmsResultEventHandler extends AbstractEventVerticle<SendSmsResultEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final MessageTemplateRootService messageTemplateRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<SendSmsResultEvent> mono) {
        return mono.flatMap(event -> {
            Supplier<Mono<ShortMessageTemplateEntity>> getTemplate =
                    () -> messageTemplateRootService.findShorMessageTemplateById(
                            LongIdentifier.builder().id(event.getTemplateId()).build());

            Function<ShortMessageTemplateEntity, Mono<Void>> createCommand = (entity) -> {
                Long recordId = MessageRecordRoot
                        .generateIdentifier(MessageSequenceEnum.M_SHORT_MESSAGE_RECORD);

                CreateShortMessageRecordCommand command = CreateShortMessageRecordCommand.builder()
                        .recordId(recordId).templateId(entity.getTemplateId())
                        .content(entity.getTemplateContent()).sendTime(new Date())
                        .businessType(entity.getBusinessType().getValue())
                        .platformType(entity.getPlatformType().getValue())
                        .receiverPhone(event.getSendPhone())
                        .messageParam(JSON.toJSONString(event.getSendParams()))
                        .sign(entity.getSign()).build();

                return this.commandDispatcher.process(Mono.just(command),
                        CreateShortMessageRecordCommand.class, Void.class);
            };

            return getTemplate.get().flatMap(createCommand);
        });
    }
}
