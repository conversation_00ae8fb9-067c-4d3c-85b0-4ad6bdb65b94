package com.xk.message.application.commons;

import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * application错误码
 * 13000-13999
 */
@Getter
@AllArgsConstructor
public enum XkMessageApplicationErrorEnum implements ExceptionIdentifier {

    APPLICATION_ERROR(13000, "application错误"),
    BUSINESS_MESSAGE_TEMPLATE_HAVA_DETAIL_TEMPLATE(13001, "请先删除模板内所有内容"),
    SHORT_MESSAGE_TEMPLATE_CANT_DELETE(13002, "短信模板不能删除"),

    MESSAGE_TEMPLATE_CANT_UPDATE(13003, "模板不能编辑"),
    MESSAGE_SCENE_ALREADY_EXIST(13004, "场景已存在"),
    SHORT_MESSAGE_CANT_ENABLE(13005, "短信模板不能启用"),
    STATION_MESSAGE_CANT_ENABLE(13006, "站内信模板不能启用"),
    ;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}
