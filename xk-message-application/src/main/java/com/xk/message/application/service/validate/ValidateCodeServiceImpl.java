package com.xk.message.application.service.validate;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.config.domain.service.cfg.DictObjectDomainService;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.event.message.MessageNoticeEvent;
import com.xk.domain.model.count.CountIdentifier;
import com.xk.domain.service.count.CountRootDomainService;
import com.xk.enums.count.CountBusiTypeEnum;
import com.xk.message.domain.commons.SendMessageEnum;
import com.xk.message.domain.commons.XkMessageDomainErrorEnum;
import com.xk.message.domain.model.validate.ValidateCodeEntity;
import com.xk.message.domain.model.validate.ids.ValidateIdentifier;
import com.xk.message.domain.service.validate.ValidateRootService;
import com.xk.message.domain.support.XkMessageDomainException;
import com.xk.message.enums.message.MessageBusinessSceneEnum;
import com.xk.message.enums.validate.IdentifyTypeEnum;
import com.xk.message.enums.validate.ValidateCodeBusinessContentTypeEnum;
import com.xk.message.enums.validate.ValidateCodeBusinessTypeEnum;
import com.xk.message.interfaces.dto.req.validate.SendValidateCodeReqDto;
import com.xk.message.interfaces.dto.req.validate.ValidateCodeCountIdReqDto;
import com.xk.message.interfaces.service.validate.ValidateCodeService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/7/30 16:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ValidateCodeServiceImpl implements ValidateCodeService {

    private final ValidateRootService validateRootService;

    private final CountRootDomainService countRootDomainService;

    private final DictObjectDomainService dictObjectDomainService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    @BusiCode
    public Mono<Void> sendValidateCode(Mono<SendValidateCodeReqDto> mono) {
        return mono.flatMap(dto -> {

            Supplier<Mono<Long>> getCount = () -> {
                CountIdentifier identifier =
                        CountIdentifier.builder().countBusiType(CountBusiTypeEnum.VERIFICATION_CODE)
                                .key(MessageBusinessSceneEnum.VERIFICATION_CODE
                                        .getCode() + "_" + dto.getIdentifyCode())
                                .build();
                return countRootDomainService.getCount(identifier);
            };

            Function<Long, Mono<Void>> checkCount = count -> {
                Supplier<Mono<Long>> getMaxNum = () -> dictObjectDomainService
                        .getSystemConfigToLong(SendMessageEnum.SEND_SMD_MESSAGE_MAX_NUM);
                return getMaxNum.get().filter(max -> count <= max)
                        .switchIfEmpty(Mono.error(new XkMessageDomainException(
                                XkMessageDomainErrorEnum.VALIDATE_CODE_NUMBER_ERROR)))
                        .then();
            };

            // 查询配置信息
            Supplier<Mono<String>> saveAndGetCode = () -> {
                ValidateIdentifier validateIdentifier = ValidateIdentifier.builder()
                        .businessType(BusinessTypeEnum.getByValue(dto.getBusinessType()))
                        .identifyType(IdentifyTypeEnum.getByCode(dto.getIdentifyType()))
                        .validateCodeBusinessType(ValidateCodeBusinessTypeEnum
                                .getByCode(dto.getValidateCodeBusinessType()))
                        .mobileCode(dto.getMobileCode()).identifyCode(dto.getIdentifyCode())
                        .build();

                return validateRootService
                        .saveAndGetValidateCode(ValidateCodeEntity.getBuilder(validateIdentifier)
                                .validateCodeBusinessContentType(ValidateCodeBusinessContentTypeEnum
                                        .getByCode(dto.getValidateCodeBusinessContentType()))
                                .validateCodeLength(dto.getValidateCodeLength())
                                .language(SystemLanguageLocale.valueOf(dto.getLanguage())).build());
            };

            Function<String, Mono<Void>> publishEvent = (code) -> {
                EventRoot root = EventRoot.builder().domainEvent(MessageNoticeEvent.builder()
                        .identifier(
                                EventRoot.getCommonsDomainEventIdentifier(MessageNoticeEvent.class))
                        .platformType(PlatformTypeEnum.PC_BOSS_OMS.getValue())
                        .businessType(BusinessTypeEnum.XING_KA.getValue())
                        .msgBusiScene(MessageBusinessSceneEnum.VERIFICATION_CODE.getCode())
                        .receiverPhone(dto.getIdentifyCode()).params(List.of(code)).build())
                        .isQueue(true).build();
                return eventRootService.publisheByMono(root).then();
            };

            return getCount.get().flatMap(checkCount)
                    .then(saveAndGetCode.get().flatMap(publishEvent));
        });
    }

    @Override
    @BusiCode
    public Mono<Void> resetValidateCount(Mono<ValidateCodeCountIdReqDto> dtoMono) {
        return dtoMono.flatMap(dto -> {
            CountIdentifier identifier =
                    CountIdentifier.builder().countBusiType(CountBusiTypeEnum.VERIFICATION_CODE)
                            .key(MessageBusinessSceneEnum.VERIFICATION_CODE.getCode() + "_" + dto
                                    .getIdentifyCode())
                            .build();
            return countRootDomainService.remove(identifier);
        });
    }
}
