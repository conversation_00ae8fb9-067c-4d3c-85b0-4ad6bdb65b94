package com.xk.message.application.handler.command.template;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.command.template.DeletedShortMessageTemplateCommand;
import com.xk.message.domain.model.template.MessageTemplateRoot;
import com.xk.message.domain.model.template.entity.ShortMessageTemplateEntity;
import com.xk.message.domain.repository.template.MessageTemplateRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class DeletedShortMessageTemplateHandler
        implements IActionCommandHandler<DeletedShortMessageTemplateCommand, Void> {

    private final MessageTemplateRootRepository messageTemplateRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<DeletedShortMessageTemplateCommand> commandMono) {
        return execute(commandMono, ShortMessageTemplateEntity.class, converter::convert,
                entity -> MessageTemplateRoot.builder()
                        .identifier(LongIdentifier.builder().id(-1L).build())
                        .shortMessageTemplateEntityList(List.of(entity))
                        .build(), messageTemplateRootRepository::deletedShortMessageTemplate);
    }
}
