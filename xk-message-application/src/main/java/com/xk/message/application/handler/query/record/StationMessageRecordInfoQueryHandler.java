package com.xk.message.application.handler.query.record;


import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.query.record.StationMessageRecordInfoQuery;
import com.xk.message.application.dto.record.StationMessageRecordAppDto;
import com.xk.message.domain.repository.record.MessageRecordRootQueryRepository;
import com.xk.message.interfaces.dto.rsp.record.StationMessageRecordRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class StationMessageRecordInfoQueryHandler
        implements IActionQueryHandler<StationMessageRecordInfoQuery, StationMessageRecordRspDto> {

    private final MessageRecordRootQueryRepository messageRecordRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<StationMessageRecordRspDto> execute(Mono<StationMessageRecordInfoQuery> query) {
        return execute(query, LongIdentifier.class,
                (data, clazz) -> LongIdentifier.builder().id(data.getRecordId()).build(),
                messageRecordRootQueryRepository::findStationMessageRecordById,
                StationMessageRecordRspDto.class, (data, clazz) -> {
                    StationMessageRecordAppDto appDto =
                            this.converter.convert(data, StationMessageRecordAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
