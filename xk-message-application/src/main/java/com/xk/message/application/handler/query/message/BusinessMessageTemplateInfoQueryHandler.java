package com.xk.message.application.handler.query.message;


import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.query.template.BusinessMessageTemplateInfoQuery;
import com.xk.message.application.dto.message.BusinessMessageTemplateAppDto;
import com.xk.message.domain.repository.template.MessageTemplateRootQueryRepository;
import com.xk.message.interfaces.dto.rsp.template.BusinessMessageTemplateRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class BusinessMessageTemplateInfoQueryHandler
        implements IActionQueryHandler<BusinessMessageTemplateInfoQuery, BusinessMessageTemplateRspDto> {

    private final MessageTemplateRootQueryRepository messageTemplateRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<BusinessMessageTemplateRspDto> execute(Mono<BusinessMessageTemplateInfoQuery> query) {
        return execute(query, LongIdentifier.class,
                (data, clazz) -> LongIdentifier.builder().id(data.getBusinessMessageTemplateId()).build()
                , messageTemplateRootQueryRepository::findById, BusinessMessageTemplateRspDto.class,
                (data, clazz) -> {
                    BusinessMessageTemplateAppDto appDto = this.converter.convert(data,
                            BusinessMessageTemplateAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
