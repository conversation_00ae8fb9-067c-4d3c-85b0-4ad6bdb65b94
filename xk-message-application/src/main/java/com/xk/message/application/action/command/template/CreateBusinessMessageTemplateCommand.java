package com.xk.message.application.action.command.template;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.message.interfaces.dto.req.template.CreateBusinessMessageTemplateReqDto;
import com.xk.message.domain.model.template.entity.BusinessMessageTemplateEntity;
import com.xk.message.enums.message.MessageBusinessSceneEnum;
import com.xk.message.enums.message.MessageTypeStatusEnum;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = BusinessMessageTemplateEntity.class,
                uses = {BusinessTypeEnum.class, PlatformTypeEnum.class,
                        MessageBusinessSceneEnum.class, MessageTypeStatusEnum.class}),
        @AutoMapper(target = CreateBusinessMessageTemplateReqDto.class)})
public class CreateBusinessMessageTemplateCommand extends AbstractActionCommand {

    /**
     * 模板id
     */
    private Long businessMessageTemplateId;

    /**
     * 业务场景
     */
    @ReverseAutoMapping(targetClass = BusinessMessageTemplateEntity.class,
            expression = "java(source.getBusinessScene() == null ? null : source.getBusinessScene().getCode())")
    private Integer businessScene;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 业务类型
     */
    @ReverseAutoMapping(targetClass = BusinessMessageTemplateEntity.class,
            expression = "java(source.getBusinessType() == null ? null : source.getBusinessType().getValue())")
    private Integer businessType;

    /**
     * 平台类型
     */
    @ReverseAutoMapping(targetClass = BusinessMessageTemplateEntity.class,
            expression = "java(source.getPlatformType() == null ? null : source.getPlatformType().getValue())")
    private Integer platformType;

    /**
     * 短信开关
     */
    @ReverseAutoMapping(targetClass = BusinessMessageTemplateEntity.class,
            expression = "java(source.getShortMessageEnable() == null ? null : source.getShortMessageEnable().getCode())")
    private Integer shortMessageEnable;

    /**
     * 站内信开关
     */
    @ReverseAutoMapping(targetClass = BusinessMessageTemplateEntity.class,
            expression = "java(source.getStationMessage() == null ? null : source.getStationMessage().getCode())")
    private Integer stationMessage;

    /**
     * im开关
     */
    @ReverseAutoMapping(targetClass = BusinessMessageTemplateEntity.class,
            expression = "java(source.getImEnable() == null ? null : source.getImEnable().getCode())")
    private Integer imEnable;

}
