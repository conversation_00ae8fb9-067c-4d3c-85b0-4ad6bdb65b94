package com.xk.message.application.handler.event.message;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.config.domain.service.cfg.DictObjectDomainService;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.event.message.MessageNoticeEvent;
import com.xk.domain.model.count.CountEntity;
import com.xk.domain.model.count.CountIdentifier;
import com.xk.domain.model.count.CountRoot;
import com.xk.domain.service.count.CountRootDomainService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.enums.count.CountBusiTypeEnum;
import com.xk.message.application.action.command.record.CreateStationMessageRecordCommand;
import com.xk.message.application.action.query.template.BusinessMessageTemplateBySceneQuery;
import com.xk.message.domain.commons.SendMessageEnum;
import com.xk.message.domain.commons.XkMessageDomainErrorEnum;
import com.xk.message.domain.event.message.ShortMessageCreateEvent;
import com.xk.message.domain.model.record.MessageRecordRoot;
import com.xk.message.domain.model.template.MessageTemplateRoot;
import com.xk.message.domain.model.template.entity.BusinessMessageTemplateEntity;
import com.xk.message.domain.model.template.entity.ShortMessageTemplateEntity;
import com.xk.message.domain.model.template.entity.StationMessageTemplateEntity;
import com.xk.message.domain.service.template.MessageTemplateRootService;
import com.xk.message.domain.support.XkMessageDomainException;
import com.xk.message.domain.support.message.MessageSequenceEnum;
import com.xk.message.enums.message.MessageBusinessSceneEnum;
import com.xk.message.enums.message.MessageTypeStatusEnum;
import com.xk.message.enums.message.ShortMessageSendTypeEnum;
import com.xk.message.enums.record.MsgBusiTypeEnum;
import com.xk.message.infrastructure.commons.MessageReplaceUtil;
import com.xk.message.interfaces.dto.rsp.template.BusinessMessageTemplateRspDto;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MessageNoticeEventHandler extends AbstractEventVerticle<MessageNoticeEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandActionCommandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final DictObjectDomainService dictObjectDomainService;
    private final CountRootDomainService countRootDomainService;
    private final MessageTemplateRootService messageTemplateRootService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<MessageNoticeEvent> mono) {
        return mono.flatMap(event -> {
            // 根据业务场景 平台 业务 查询模板
            Supplier<Mono<MessageTemplateRoot>> getMessageRoot = () -> {
                BusinessMessageTemplateBySceneQuery query = BusinessMessageTemplateBySceneQuery
                        .builder().businessScene(event.getMsgBusiScene())
                        .platformType(event.getPlatformType()).businessType(event.getBusinessType())
                        .build();
                return this.actionQueryDispatcher
                        .executeQuery(Mono.just(query), BusinessMessageTemplateBySceneQuery.class,
                                BusinessMessageTemplateRspDto.class)
                        .map(BusinessMessageTemplateRspDto::getBusinessMessageTemplateId)
                        .flatMap(id -> messageTemplateRootService
                                .getRoot(LongIdentifier.builder().id(id).build()));
            };

            Function<MessageTemplateRoot, Mono<MessageTemplateRoot>> sendShorMsg = (root) -> {
                BusinessMessageTemplateEntity entity = root.getBusinessMessageTemplateEntity();
                if (Objects.equals(entity.getShortMessageEnable(), MessageTypeStatusEnum.OFF)) {
                    return Mono.just(root);
                }
                return sendMsg(Flux.fromIterable(root.getShortMessageTemplateEntityList()), event)
                        .thenReturn(root);
            };

            Function<MessageTemplateRoot, Mono<Void>> sendStationMsg = (root -> {
                BusinessMessageTemplateEntity entity = root.getBusinessMessageTemplateEntity();
                if (Objects.equals(entity.getStationMessage(), MessageTypeStatusEnum.OFF)) {
                    return Mono.empty();
                }
                List<StationMessageTemplateEntity> list =
                        root.getStationMessageTemplateEntityList();
                return sendStationMsg(list.getFirst(), event);
            });

            return getMessageRoot.get().flatMap(sendShorMsg).flatMap(sendStationMsg);
        });
    }

    private Mono<Void> sendMsg(Flux<ShortMessageTemplateEntity> flux, MessageNoticeEvent event) {
        return flux.flatMap(entity -> {
            Supplier<Mono<Tuple2<Long, Long>>> getConfig = () -> {
                Supplier<Mono<Long>> getExpireTime =
                        () -> dictObjectDomainService.getSystemConfigToLong(
                                SendMessageEnum.SEND_SMD_MESSAGE_MAX_NUM_EXPIRE_TIME);
                Supplier<Mono<Long>> getMaxNum = () -> dictObjectDomainService
                        .getSystemConfigToLong(SendMessageEnum.SEND_SMD_MESSAGE_MAX_NUM);

                return Mono.zip(getExpireTime.get(), getMaxNum.get());
            };

            Function<Tuple2<Long, Long>, Mono<Void>> countSend = (tuple) -> {
                Supplier<Mono<Long>> doCount = () -> {
                    CountIdentifier identifier = CountIdentifier.builder()
                            .countBusiType(CountBusiTypeEnum.VERIFICATION_CODE)
                            .key(event.getMsgBusiScene() + "_" + event.getReceiverPhone()).build();
                    CountEntity countEntity = CountEntity.getBuilder(identifier).isExpired(true)
                            .expireTime(tuple.getT1()).increment(1L).build();
                    CountRoot root = CountRoot.builder().identifier(identifier)
                            .countEntity(countEntity).build();
                    return countRootDomainService.incr(root);
                };

                Function<Long, Mono<Void>> checkCount = (count) -> {
                    if (count > tuple.getT2()) {
                        log.error("{},业务:{},短信发送次数超过限制，当前次数：{}", event.getReceiverPhone(),
                                event.getMsgBusiScene(), count);
                        return Mono.error(new XkMessageDomainException(
                                XkMessageDomainErrorEnum.VALIDATE_CODE_NUMBER_ERROR));
                    }
                    return Mono.empty();
                };

                return doCount.get().flatMap(checkCount);
            };

            Supplier<Mono<EventRoot>> createEventRoot = () -> {
                ShortMessageCreateEvent.ShortMessageCreateEventBuilder builder =
                        ShortMessageCreateEvent.builder();
                builder.identifier(
                        EventRoot.getCommonsDomainEventIdentifier(ShortMessageCreateEvent.class))
                        .accessId(entity.getAccessId()).tpTemplateId(entity.getTpTemplateId())
                        .sendType(entity.getSendType().getCode())
                        .phoneNumber(event.getReceiverPhone()).sign(entity.getSign());
                if (Objects.equals(entity.getSendType(),
                        ShortMessageSendTypeEnum.TEMPLATE_ID_SEND)) {
                    buildTemplateParam(event, entity, builder);
                } else if (Objects.equals(entity.getSendType(),
                        ShortMessageSendTypeEnum.CONTEND_SEND)) {
                    buildContentParam(event, entity, builder);
                }
                return Mono.just(EventRoot.builder().domainEvent(builder.build()).build());
            };

            Function<EventRoot, Mono<Boolean>> publishEvent =
                    (msgEvent) -> Mono.fromCallable(() -> {
                        try {
                            return eventRootService.publish(msgEvent);
                        } catch (ExceptionWrapperThrowable e) {
                            throw new RuntimeException(e);
                        }
                    }).thenReturn(true).onErrorReturn(false);

            return getConfig.get().flatMap(countSend)
                    .then(createEventRoot.get().flatMap(publishEvent)).onErrorResume(e -> {
                        log.error("phone: {},templateId: {},sendMsg error ",
                                event.getReceiverPhone(), entity.getBusinessMessageTemplateId(), e);
                        return Mono.just(false);
                    });
        }).any(success -> success).then();
    }

    private Mono<Void> sendStationMsg(StationMessageTemplateEntity entity,
            MessageNoticeEvent data) {
        Long identifier =
                MessageRecordRoot.generateIdentifier(MessageSequenceEnum.M_STATION_MESSAGE_RECORD);
        String content = entity.getTemplateContent();
        Integer msgBusiType = null;
        if (Objects.equals(data.getMsgBusiScene(),
                MessageBusinessSceneEnum.SEND_ACCOUNT.getCode())) {
            msgBusiType = MsgBusiTypeEnum.GOODS.getCode();
        } else if (Objects.equals(data.getMsgBusiScene(),
                MessageBusinessSceneEnum.ACCOUNT_ORDER_PAY_SUCCESS.getCode())) {
            msgBusiType = MsgBusiTypeEnum.ORDER.getCode();
        }
        List<String> templateParams = MessageReplaceUtil.getParams(entity.getTemplateContent());
        if (CollectionUtils.isNotEmpty(templateParams)) {
            for (int i = 0; i < templateParams.size(); i++) {
                content = content.replace(templateParams.get(i), data.getParams().get(i));
            }
        }
        CreateStationMessageRecordCommand createStationMessageRecordCommand =
                CreateStationMessageRecordCommand.builder().recordId(identifier)
                        .templateId(entity.getTemplateId()).content(content)
                        .receiverCode(data.getReceiverCode()).sendTime(new Date())
                        .title(entity.getTitle()).msgBusiType(msgBusiType)
                        .msgBusiId(data.getBusiId()).createTime(new Date())
                        .businessType(data.getBusinessType()).platformType(data.getPlatformType())
                        .readStatus(CommonStatusEnum.DISABLE.getCode()).build();
        return this.commandActionCommandDispatcher.process(
                Mono.just(createStationMessageRecordCommand),
                CreateStationMessageRecordCommand.class, Void.class);
    }

    private void buildTemplateParam(MessageNoticeEvent event, ShortMessageTemplateEntity entity,
            ShortMessageCreateEvent.ShortMessageCreateEventBuilder builder) {
        builder.templateId(String.valueOf(entity.getTemplateId()));
        if (StringUtils.isBlank(entity.getTemplateParam())) {
            return;
        }
        String[] array = entity.getTemplateParam().split(",");
        List<String> templateParamsList = Arrays.asList(array);
        Map<String, String> templateParams = new LinkedHashMap<>();
        for (int i = 0; i < templateParamsList.size(); i++) {
            String templateParam = templateParamsList.get(i);
            templateParams.put(templateParam, event.getParams().get(i));
        }
        builder.templateParams(templateParams);
    }

    private void buildContentParam(MessageNoticeEvent event, ShortMessageTemplateEntity entity,
            ShortMessageCreateEvent.ShortMessageCreateEventBuilder builder) {
        builder.content(entity.getTemplateContent());
        List<String> templateParams = MessageReplaceUtil.getParams(entity.getTemplateContent());
        if (CollectionUtils.isEmpty(templateParams)) {
            return;
        }

        String replace = entity.getTemplateContent();
        for (int i = 0; i < templateParams.size(); i++) {
            replace = replace.replace(templateParams.get(i), event.getParams().get(i));
        }
        builder.content(replace);
    }
}

