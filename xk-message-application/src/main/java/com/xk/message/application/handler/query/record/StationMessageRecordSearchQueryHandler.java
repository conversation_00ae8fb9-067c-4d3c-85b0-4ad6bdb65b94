package com.xk.message.application.handler.query.record;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.message.application.action.query.record.StationMessageRecordPagerQuery;
import com.xk.message.application.dto.record.StationMessageRecordAppDto;
import com.xk.message.domain.repository.record.MessageRecordRootQueryRepository;
import com.xk.message.interfaces.dto.rsp.record.StationMessageRecordRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class StationMessageRecordSearchQueryHandler implements IActionQueryHandler<StationMessageRecordPagerQuery, Pagination> {

    private final MessageRecordRootQueryRepository messageRecordRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<StationMessageRecordPagerQuery> query) {
        return this.execute(query,
                (searchQuery) -> {
                    Pagination pagination = new Pagination();
                    pagination.setLimit(searchQuery.getLimit());
                    pagination.setOffset(searchQuery.getOffset());
                    pagination.setCriteria(CollectionHelper.converBeanToMap(searchQuery));
                    return pagination;
                },
                messageRecordRootQueryRepository::searchStationMessageRecordPager,
                StationMessageRecordRspDto.class, (data, clazz) -> {
                    StationMessageRecordAppDto appDto = this.converter.convert(data, StationMessageRecordAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
