package com.xk.message.application.handler.query.message;


import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.query.template.ShortMessageTemplateInfoQuery;
import com.xk.message.application.dto.message.ShortMessageTemplateAppDto;
import com.xk.message.domain.repository.template.MessageTemplateRootQueryRepository;
import com.xk.message.interfaces.dto.rsp.template.ShortMessageTemplateRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class ShortMessageTemplateInfoQueryHandler
        implements IActionQueryHandler<ShortMessageTemplateInfoQuery, ShortMessageTemplateRspDto> {

    private final MessageTemplateRootQueryRepository messageTemplateRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<ShortMessageTemplateRspDto> execute(Mono<ShortMessageTemplateInfoQuery> query) {
        return execute(query, LongIdentifier.class,
                (data, clazz) -> LongIdentifier.builder().id(data.getTemplateId()).build()
                , messageTemplateRootQueryRepository::findShortMessageTemplateById, ShortMessageTemplateRspDto.class,
                (data, clazz) -> {
                    ShortMessageTemplateAppDto appDto = this.converter.convert(data, ShortMessageTemplateAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
