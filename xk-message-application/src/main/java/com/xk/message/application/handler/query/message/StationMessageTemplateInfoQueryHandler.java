package com.xk.message.application.handler.query.message;


import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.query.template.StationMessageTemplateInfoQuery;
import com.xk.message.application.dto.message.StationMessageTemplateAppDto;
import com.xk.message.domain.repository.template.MessageTemplateRootQueryRepository;
import com.xk.message.interfaces.dto.rsp.template.StationMessageTemplateRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class StationMessageTemplateInfoQueryHandler
        implements IActionQueryHandler<StationMessageTemplateInfoQuery, StationMessageTemplateRspDto> {

    private final MessageTemplateRootQueryRepository messageTemplateRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<StationMessageTemplateRspDto> execute(Mono<StationMessageTemplateInfoQuery> query) {
        return execute(query, LongIdentifier.class,
                (data, clazz) -> LongIdentifier.builder().id(data.getTemplateId()).build()
                , messageTemplateRootQueryRepository::findStationMessageTemplateById, StationMessageTemplateRspDto.class,
                (data, clazz) -> {
                    StationMessageTemplateAppDto appDto = this.converter.convert(data,
                            StationMessageTemplateAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
