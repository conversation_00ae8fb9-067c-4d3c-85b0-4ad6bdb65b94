package com.xk.message.application.action.command.template;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.message.interfaces.dto.req.template.UpdateImMessageTemplateReqDto;

import com.xk.message.domain.model.template.entity.ImMessageTemplateEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = UpdateImMessageTemplateReqDto.class),
        @AutoMapper(target = ImMessageTemplateEntity.class, uses = {BusinessTypeEnum.class, PlatformTypeEnum.class})
})
public class UpdateImMessageTemplateCommand extends AbstractActionCommand {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 模板参数 逗号隔开
     */
    private String templateParam;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 业务类型
     */
    @ReverseAutoMapping(targetClass = ImMessageTemplateEntity.class,
            expression = "java(source.getBusinessType() == null ? null : source.getBusinessType().getValue())"
    )
    private Integer businessType;

    /**
     * 平台类型
     */
    @ReverseAutoMapping(targetClass = ImMessageTemplateEntity.class,
            expression = "java(source.getPlatformType() == null ? null : source.getPlatformType().getValue())"
    )
    private Integer platformType;

    /**
     * 模板id
     */
    private Long businessMessageTemplateId;

    /**
     * 标题
     */
    private String title;

}
