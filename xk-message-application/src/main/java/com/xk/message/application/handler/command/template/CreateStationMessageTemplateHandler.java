package com.xk.message.application.handler.command.template;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.command.template.CreateStationMessageTemplateCommand;
import com.xk.message.domain.model.template.MessageTemplateRoot;
import com.xk.message.domain.model.template.entity.StationMessageTemplateEntity;
import com.xk.message.domain.repository.template.MessageTemplateRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class CreateStationMessageTemplateHandler
        implements IActionCommandHandler<CreateStationMessageTemplateCommand, Void> {

    private final MessageTemplateRootRepository messageTemplateRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateStationMessageTemplateCommand> commandMono) {
        return execute(commandMono, StationMessageTemplateEntity.class, converter::convert,
                entity -> MessageTemplateRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getBusinessMessageTemplateId()).build())
                        .stationMessageTemplateEntityList(List.of(entity))
                        .build(), messageTemplateRootRepository::createStationMessageTemplate);
    }
}
