package com.xk.message.application.action.command.template;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.message.interfaces.dto.req.template.UpdateShortMessageTemplateReqDto;
import com.xk.message.domain.model.template.entity.ShortMessageTemplateEntity;

import com.xk.message.enums.message.ShortMessageSendTypeEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = UpdateShortMessageTemplateReqDto.class), @AutoMapper(
        target = ShortMessageTemplateEntity.class,
        uses = {BusinessTypeEnum.class, PlatformTypeEnum.class, ShortMessageSendTypeEnum.class})})
public class UpdateShortMessageTemplateCommand extends AbstractActionCommand {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 三方模板id
     */
    private String tpTemplateId;

    /**
     * 通道id
     */
    private Long accessId;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 发送类型 id发送/内容发送
     */
    @ReverseAutoMapping(targetClass = ShortMessageTemplateEntity.class,
            expression = "java(source.getSendType() == null ? null : source.getSendType().getCode())")
    private Integer sendType;

    /**
     * 模板参数 逗号隔开
     */
    private String templateParam;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 业务类型
     */
    @ReverseAutoMapping(targetClass = ShortMessageTemplateEntity.class,
            expression = "java(source.getBusinessType() == null ? null : source.getBusinessType().getValue())")
    private Integer businessType;

    /**
     * 平台类型
     */
    @ReverseAutoMapping(targetClass = ShortMessageTemplateEntity.class,
            expression = "java(source.getPlatformType() == null ? null : source.getPlatformType().getValue())")
    private Integer platformType;

    /**
     * 模板id
     */
    private Long businessMessageTemplateId;

    /**
     * 签名
     */
    private String sign;
}
