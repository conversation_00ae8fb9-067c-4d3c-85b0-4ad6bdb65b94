package com.xk.message.application.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author: killer
 **/
@Configuration
@ComponentScan({"com.xk.message.application.convertor"
        , "com.xk.message.application.query"
        , "com.xk.message.application.service"
        , "com.xk.message.application.handler"
        , "com.xk.message.application.task"})
public class XkMessageApplicationConfig {
}
