package com.xk.message.application.handler.command.template;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.command.template.CreateShortMessageTemplateCommand;
import com.xk.message.domain.model.template.MessageTemplateRoot;
import com.xk.message.domain.model.template.entity.ShortMessageTemplateEntity;
import com.xk.message.domain.repository.template.MessageTemplateRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class CreateShortMessageTemplateHandler
        implements IActionCommandHandler<CreateShortMessageTemplateCommand, Void> {

    private final MessageTemplateRootRepository messageTemplateRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateShortMessageTemplateCommand> commandMono) {
        return execute(commandMono, ShortMessageTemplateEntity.class, converter::convert,
                entity -> MessageTemplateRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getBusinessMessageTemplateId()).build())
                        .shortMessageTemplateEntityList(List.of(entity))
                        .build(), messageTemplateRootRepository::createShortMessageTemplate);
    }
}
