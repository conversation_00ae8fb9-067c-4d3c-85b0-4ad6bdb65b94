package com.xk.message.application.handler.query.message;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.message.application.action.query.template.BusinessMessageTemplatePagerQuery;
import com.xk.message.application.dto.message.BusinessMessageTemplateAppDto;
import com.xk.message.domain.repository.template.MessageTemplateRootQueryRepository;
import com.xk.message.interfaces.dto.rsp.template.BusinessMessageTemplateRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class BusinessMessageTemplateSearchQueryHandler
        implements IActionQueryHandler<BusinessMessageTemplatePagerQuery, Pagination> {

    private final MessageTemplateRootQueryRepository messageTemplateRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<BusinessMessageTemplatePagerQuery> query) {
        return this.execute(query,
                (searchQuery) -> {
                    Pagination pagination = new Pagination();
                    pagination.setLimit(searchQuery.getLimit());
                    pagination.setOffset(searchQuery.getOffset());
                    pagination.setCriteria(CollectionHelper.converBeanToMap(searchQuery));
                    return pagination;
                },
                messageTemplateRootQueryRepository::searchBusinessMessageTemplatePager,
                BusinessMessageTemplateRspDto.class, (data, clazz) -> {
                    BusinessMessageTemplateAppDto appDto = this.converter.convert(data,
                            BusinessMessageTemplateAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
