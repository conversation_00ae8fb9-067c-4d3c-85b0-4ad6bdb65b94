package com.xk.message.application.action.command.template;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.message.interfaces.dto.req.template.DeletedShortMessageTemplateReqDto;

import com.xk.message.domain.model.template.entity.ShortMessageTemplateEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = DeletedShortMessageTemplateReqDto.class),
        @AutoMapper(target = ShortMessageTemplateEntity.class)
})
public class DeletedShortMessageTemplateCommand extends AbstractActionCommand {


    /**
     * 模板id
     */
    private Long templateId;


    /**
     * 模板id
     */
    private Long businessMessageTemplateId;
}
