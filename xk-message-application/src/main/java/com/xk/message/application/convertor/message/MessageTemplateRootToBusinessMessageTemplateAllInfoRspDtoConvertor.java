package com.xk.message.application.convertor.message;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.xk.message.domain.model.template.MessageTemplateRoot;
import com.xk.message.domain.model.template.entity.BusinessMessageTemplateEntity;
import com.xk.message.domain.model.template.entity.ShortMessageTemplateEntity;
import com.xk.message.domain.model.template.entity.StationMessageTemplateEntity;
import com.xk.message.interfaces.dto.rsp.template.BusinessMessageTemplateAllInfoRspDto;
import com.xk.message.interfaces.dto.rsp.template.ShortMessageTemplateRspDto;
import com.xk.message.interfaces.dto.rsp.template.StationMessageTemplateRspDto;

import io.github.linpeilie.BaseMapper;

@Component
public class MessageTemplateRootToBusinessMessageTemplateAllInfoRspDtoConvertor
        implements BaseMapper<MessageTemplateRoot, BusinessMessageTemplateAllInfoRspDto> {

    @Override
    public BusinessMessageTemplateAllInfoRspDto convert(MessageTemplateRoot source) {
        return convert(source, new BusinessMessageTemplateAllInfoRspDto());
    }

    @Override
    public BusinessMessageTemplateAllInfoRspDto convert(MessageTemplateRoot source,
            BusinessMessageTemplateAllInfoRspDto target) {
        if (source == null) {
            return target;
        }

        BusinessMessageTemplateEntity entity = source.getBusinessMessageTemplateEntity();
        if (entity != null) {
            target.setBusinessMessageTemplateId(entity.getBusinessMessageTemplateId());
            if (entity.getBusinessScene() != null) {
                target.setBusinessScene(entity.getBusinessScene().getCode());
            }
            target.setCreateId(entity.getCreateId());
            target.setCreateTime(entity.getCreateTime());
            target.setUpdateId(entity.getUpdateId());
            target.setUpdateTime(entity.getUpdateTime());
            if (entity.getBusinessType() != null) {
                target.setBusinessType(entity.getBusinessType().getValue());
            }
            if (entity.getPlatformType() != null) {
                target.setPlatformType(entity.getPlatformType().getValue());
            }
            if (entity.getShortMessageEnable() != null) {
                target.setShortMessageEnable(entity.getShortMessageEnable().getCode());
            }
            if (entity.getImEnable() != null) {
                target.setImEnable(entity.getImEnable().getCode());
            }
        }
        List<ShortMessageTemplateEntity> shortMessageTemplateEntityList =
                source.getShortMessageTemplateEntityList();
        if (CollectionUtils.isNotEmpty(shortMessageTemplateEntityList)) {
            target.setShortMessageTemplateRspDtoList(
                    shortMessageTemplateEntityList.stream().map(shortEntity -> {
                        ShortMessageTemplateRspDto rspDto = new ShortMessageTemplateRspDto();
                        rspDto.setTemplateId(shortEntity.getTemplateId());
                        rspDto.setTpTemplateId(shortEntity.getTpTemplateId());
                        rspDto.setAccessId(shortEntity.getAccessId());
                        rspDto.setTemplateContent(shortEntity.getTemplateContent());
                        if (shortEntity.getSendType() != null) {
                            rspDto.setSendType(shortEntity.getSendType().getCode());
                        }
                        rspDto.setTemplateParam(shortEntity.getTemplateParam());
                        rspDto.setCreateId(shortEntity.getCreateId());
                        rspDto.setCreateTime(shortEntity.getCreateTime());
                        rspDto.setUpdateId(shortEntity.getUpdateId());
                        rspDto.setUpdateTime(shortEntity.getUpdateTime());
                        if (shortEntity.getBusinessType() != null) {
                            rspDto.setBusinessType(shortEntity.getBusinessType().getValue());
                        }
                        if (shortEntity.getPlatformType() != null) {
                            rspDto.setPlatformType(shortEntity.getPlatformType().getValue());
                        }
                        rspDto.setBusinessMessageTemplateId(
                                shortEntity.getBusinessMessageTemplateId());
                        rspDto.setSign(shortEntity.getSign());
                        return rspDto;
                    }).collect(Collectors.toList()));
        }

        List<StationMessageTemplateEntity> stationMessageTemplateEntityList =
                source.getStationMessageTemplateEntityList();
        if (CollectionUtils.isNotEmpty(stationMessageTemplateEntityList)) {
            StationMessageTemplateRspDto rspDto = new StationMessageTemplateRspDto();
            StationMessageTemplateEntity stationEntity =
                    stationMessageTemplateEntityList.getFirst();
            rspDto.setTemplateId(stationEntity.getTemplateId());
            rspDto.setTemplateContent(stationEntity.getTemplateContent());
            rspDto.setTemplateParam(stationEntity.getTemplateParam());
            rspDto.setCreateId(stationEntity.getCreateId());
            rspDto.setCreateTime(stationEntity.getCreateTime());
            rspDto.setUpdateId(stationEntity.getUpdateId());
            rspDto.setUpdateTime(stationEntity.getUpdateTime());
            if (stationEntity.getBusinessType() != null) {
                rspDto.setBusinessType(stationEntity.getBusinessType().getValue());
            }
            if (stationEntity.getPlatformType() != null) {
                rspDto.setPlatformType(stationEntity.getPlatformType().getValue());
            }
            rspDto.setBusinessMessageTemplateId(stationEntity.getBusinessMessageTemplateId());
            rspDto.setTitle(stationEntity.getTitle());
            target.setStationMessageTemplateRspDto(rspDto);
        }
        return target;
    }
}
