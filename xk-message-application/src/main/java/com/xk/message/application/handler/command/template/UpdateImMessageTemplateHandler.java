package com.xk.message.application.handler.command.template;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.command.template.UpdateImMessageTemplateCommand;
import com.xk.message.domain.model.template.MessageTemplateRoot;
import com.xk.message.domain.model.template.entity.ImMessageTemplateEntity;
import com.xk.message.domain.repository.template.MessageTemplateRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class UpdateImMessageTemplateHandler implements IActionCommandHandler<UpdateImMessageTemplateCommand, Void> {

    private final MessageTemplateRootRepository messageTemplateRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdateImMessageTemplateCommand> commandMono) {
        return execute(commandMono, ImMessageTemplateEntity.class, converter::convert,
                entity -> MessageTemplateRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getBusinessMessageTemplateId()).build())
                        .imMessageTemplateEntityList(List.of(entity))
                        .build(), messageTemplateRootRepository::updatedImMessageTemplate);
    }
}
