package com.xk.message.application.service.record;

import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.message.application.action.command.record.UpdateStationMessageRecordCommand;
import com.xk.message.domain.repository.record.MessageRecordRootQueryRepository;
import com.xk.message.interfaces.dto.req.record.RecordReadPageReqDto;
import com.xk.message.interfaces.service.record.MessageRecordService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class MessageRecordServiceImpl implements MessageRecordService {

    private final ActionCommandDispatcher<AbstractActionCommand> actionCommandDispatcher;
    private final MessageRecordRootQueryRepository messageRecordRootQueryRepository;

    @BusiCode
    @Override
    public Mono<Void> commitPageRead(Mono<RecordReadPageReqDto> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto -> {
            Supplier<Flux<Long>> getFlux = () -> Flux.fromIterable(dto.getRecordIds());

            Function<Long, Mono<Void>> doExecute = id -> actionCommandDispatcher.executeCommand(
                    Mono.just(UpdateStationMessageRecordCommand.builder().recordId(id)
                            .readStatus(CommonStatusEnum.ENABLE.getCode()).build()),
                    UpdateStationMessageRecordCommand.class);

            return getFlux.get().flatMap(doExecute).then();
        }));
    }
}
