package com.xk.message.application.handler.command.record;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.command.record.UpdateStationMessageRecordCommand;
import com.xk.message.domain.model.record.MessageRecordRoot;
import com.xk.message.domain.model.record.entity.StationMessageRecordEntity;
import com.xk.message.domain.repository.record.MessageRecordRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class UpdateStationMessageRecordHandler
        implements IActionCommandHandler<UpdateStationMessageRecordCommand, Void> {

    private final MessageRecordRootRepository messageRecordRootRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdateStationMessageRecordCommand> mono) {
        return execute(mono, StationMessageRecordEntity.class, converter::convert,
                entity -> MessageRecordRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getRecordId()).build())
                        .stationMessageRecordEntity(entity).build(),
                messageRecordRootRepository::updateStationMessageRecord);
    }
}
