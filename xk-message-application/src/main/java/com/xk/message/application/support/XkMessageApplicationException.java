package com.xk.message.application.support;

import com.xk.message.application.commons.XkMessageApplicationErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.wrapper.ApplicationWrapperThrowable;

/**
 * @author: killer
 **/
public class XkMessageApplicationException extends ApplicationWrapperThrowable {

    public XkMessageApplicationException(XkMessageApplicationErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public XkMessageApplicationException(XkMessageApplicationErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

}
