package com.xk.message.application.handler.command.record;

import com.xk.message.application.action.command.record.CreateStationMessageRecordCommand;
import com.xk.message.domain.model.record.MessageRecordRoot;
import com.xk.message.domain.model.record.entity.StationMessageRecordEntity;
import com.xk.message.domain.repository.record.MessageRecordRootRepository;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class CreateStationMessageRecordHandler implements IActionCommandHandler<CreateStationMessageRecordCommand, Void> {

    private final MessageRecordRootRepository messageRecordRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateStationMessageRecordCommand> commandMono) {
        return execute(commandMono, StationMessageRecordEntity.class, converter::convert, entity -> MessageRecordRoot.builder()
                .identifier(LongIdentifier.builder().id(entity.getRecordId()).build())
                .stationMessageRecordEntity(entity)
                .build(), messageRecordRootRepository::createStationMessageRecord);
    }
}
