package com.xk.message.application.handler.query.message;


import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.query.template.ImMessageTemplateByBusinessTemplateIdQuery;
import com.xk.message.application.dto.message.ImMessageTemplateAppDto;
import com.xk.message.domain.repository.template.MessageTemplateRootQueryRepository;
import com.xk.message.interfaces.dto.rsp.template.ImMessageTemplateRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class ImMessageTemplateListQueryHandler
        implements IActionQueryManyHandler<ImMessageTemplateByBusinessTemplateIdQuery, ImMessageTemplateRspDto> {

    private final MessageTemplateRootQueryRepository messageTemplateRootQueryRepository;
    private final Converter converter;

    @Override
    public Flux<ImMessageTemplateRspDto> execute(Mono<ImMessageTemplateByBusinessTemplateIdQuery> query) {
        return execute(query, LongIdentifier.class,
                (data, clazz) -> LongIdentifier.builder().id(data.getBusinessMessageTemplateId()).build()
                , messageTemplateRootQueryRepository::searchImMessageTemplateByRootId, ImMessageTemplateRspDto.class,
                (data, clazz) -> {
                    ImMessageTemplateAppDto appDto = this.converter.convert(data, ImMessageTemplateAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
