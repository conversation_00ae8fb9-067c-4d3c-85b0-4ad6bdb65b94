package com.xk.message.application.action.command.template;

import com.myco.mydata.application.handler.command.AbstractActionCommand;

import com.xk.message.domain.model.template.entity.BusinessMessageTemplateEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = BusinessMessageTemplateEntity.class)
})
public class DeletedBusinessMessageTemplateCommand extends AbstractActionCommand {

    /**
     * 模板id
     */
    private Long businessMessageTemplateId;

}
