package com.xk.ewd.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import com.xk.acct.interfaces.query.UserFollowCorpQueryService;
import com.xk.goods.interfaces.query.goods.GoodsSearchQueryService;
import com.xk.interfaces.query.object.CorpObjectQueryService;
import com.xk.interfaces.query.object.GoodsObjectQueryService;
import com.xk.interfaces.query.object.UserObjectQueryService;
import com.xk.order.interfaces.query.order.OrderQueryService;
import com.xk.order.interfaces.service.logistics.LogisticsOrderQueryService;
import com.xk.tp.interfaces.query.logistics.LogisticsQueryService;

/**
 * @author: killer
 **/
public class XkEwdServiceConfig {

    @Bean
    public UserObjectQueryService userObjectQueryService(
            HttpServiceProxyFactory xkAcctHttpServiceProxyFactory) {
        return xkAcctHttpServiceProxyFactory.createClient(UserObjectQueryService.class);
    }

    @Bean
    public CorpObjectQueryService corpObjectQueryService(
            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpObjectQueryService.class);
    }

    @Bean
    public GoodsObjectQueryService goodsObjectQueryService(
            HttpServiceProxyFactory xkGoodsHttpServiceProxyFactory) {
        return xkGoodsHttpServiceProxyFactory.createClient(GoodsObjectQueryService.class);
    }

    @Bean
    public OrderQueryService orderQueryService(
            HttpServiceProxyFactory xkOrderHttpServiceProxyFactory) {
        return xkOrderHttpServiceProxyFactory.createClient(OrderQueryService.class);
    }

    @Bean
    public LogisticsOrderQueryService logisticsOrderQueryService(
            HttpServiceProxyFactory xkOrderHttpServiceProxyFactory) {
        return xkOrderHttpServiceProxyFactory.createClient(LogisticsOrderQueryService.class);
    }

    @Bean
    public GoodsSearchQueryService validateCodeQueryService(
            HttpServiceProxyFactory xkGoodsHttpServiceProxyFactory) {
        return xkGoodsHttpServiceProxyFactory.createClient(GoodsSearchQueryService.class);
    }

    @Bean
    public UserFollowCorpQueryService userFollowCorpQueryService(
            HttpServiceProxyFactory xkAcctHttpServiceProxyFactory) {
        return xkAcctHttpServiceProxyFactory.createClient(UserFollowCorpQueryService.class);
    }

    @Bean
    public LogisticsQueryService logisticsQueryService(
            HttpServiceProxyFactory xkThirdPartyHttpServiceProxyFactory) {
        return xkThirdPartyHttpServiceProxyFactory.createClient(LogisticsQueryService.class);
    }
}
