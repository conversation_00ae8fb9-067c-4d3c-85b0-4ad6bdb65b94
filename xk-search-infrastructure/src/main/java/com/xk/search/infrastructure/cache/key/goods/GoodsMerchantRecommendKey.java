package com.xk.search.infrastructure.cache.key.goods;

import java.io.Serializable;

import com.myco.mydata.infrastructure.cache.key.AbstractCacheKey;

import lombok.*;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class GoodsMerchantRecommendKey extends AbstractCacheKey {

    /**
     * 商品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;

    private Integer blockType;

    public String getKey(Serializable... keys) {
        return super.getKey(blockType, productType);
    }


}
