package com.xk.search.infrastructure.cache.key.goods;

import com.myco.mydata.infrastructure.cache.key.AbstractCacheKey;
import lombok.*;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class GoodsMerchantNotSoldKey extends AbstractCacheKey {

    private Long corpInfoId;

    public String getKey(Serializable... keys) {
        return super.getKey(corpInfoId);
    }


}
