package com.xk.search.infrastructure.repository.searchIndex;

import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.search.domain.model.searchIndex.SearchIndexRoot;
import com.xk.search.domain.repository.searchindex.SearchIndexRootRepository;
import com.xk.search.infrastructure.data.persistence.searchindex.SearchIndexMapper;
import com.xk.search.infrastructure.data.po.searchIndex.SearchIndex;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/8 17:22
 */
@Repository
@RequiredArgsConstructor
public class SearchIndexRootRepositoryImpl implements SearchIndexRootRepository {

    private final SearchIndexMapper searchIndexMapper;

    private final Converter converter;

    @Override
    public Mono<Void> save(SearchIndexRoot root) {
        return this.save(root.getSearchIndexEntity(), SearchIndex.class, this.converter::convert,
                this.searchIndexMapper::insertSelective);
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(SearchIndexRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(SearchIndexRoot root) {
        return this.update(root.getSearchIndexEntity(), SearchIndex.class, this.converter::convert,
                this.searchIndexMapper::updateByPrimaryKeySelective);
    }

    @Override
    public Mono<Void> remove(SearchIndexRoot root) {
        return this.remove(root.getSearchIndexEntity(), SearchIndex.class, this.converter::convert,
                searchIndexMapper::deleteByType);
    }
}
