package com.xk.search.infrastructure.cache.dao.goods;

import com.myco.mydata.infrastructure.cache.dao.StringCacheDao;
import com.xk.search.infrastructure.cache.key.goods.GoodsMerchantNotSoldKey;
import com.xk.search.infrastructure.cache.key.goods.GoodsMerchantRecommendKey;
import org.springframework.stereotype.Repository;

@Repository
public class GoodsMerchantNotSoldDao extends StringCacheDao<GoodsMerchantNotSoldKey, String> {
}
