package com.xk.search.infrastructure.repository.retry;

import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.domain.repository.retry.RetryRootQueryRepository;
import com.xk.search.infrastructure.cache.dao.retry.RetryDao;
import com.xk.search.infrastructure.cache.key.retry.RetryKey;
import org.springframework.stereotype.Repository;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @Date 2024/8/8 17:29
 */
@Repository
@RequiredArgsConstructor
public class RetryRootQueryRepositoryImpl implements RetryRootQueryRepository {

    private final RetryDao retryDao;

    @Override
    public Flux<RetrySearchRoot> queryRetryGoods(RetrySearchRoot root) {
        Long size = retryDao.getLLen(RetryKey.builder()
                .channelType(root.getRetrySearchEntity().getChannelType())
                .optType(root.getRetrySearchEntity().getOptType())
                .build());
        ArrayList<RetrySearchRoot> list = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            list.add(retryDao.rightPopValue(RetryKey.builder()
                    .channelType(root.getRetrySearchEntity().getChannelType())
                    .optType(root.getRetrySearchEntity().getOptType())
                    .build()));
        }
        return Flux.fromIterable(list);
    }

}
