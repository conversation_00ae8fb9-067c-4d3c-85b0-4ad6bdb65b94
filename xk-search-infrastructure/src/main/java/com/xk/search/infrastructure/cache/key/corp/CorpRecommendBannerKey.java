package com.xk.search.infrastructure.cache.key.corp;

import com.myco.mydata.infrastructure.cache.key.AbstractCacheKey;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CorpRecommendBannerKey extends AbstractCacheKey {

    private Integer blockType;
    private Integer groupType;
    private Integer pageNum;

    @Override
    public String getKey(Serializable... keys) {
        return super.getKey(blockType, groupType, pageNum);
    }
}
