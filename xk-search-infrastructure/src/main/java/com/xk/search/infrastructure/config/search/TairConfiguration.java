package com.xk.search.infrastructure.config.search;

import com.aliyun.tair.tairsearch.TairSearch;
import com.xk.search.enums.search.SearchDataSourceEnum;
import io.valkey.JedisPool;
import io.valkey.JedisPoolConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Slf4j
@Configuration(proxyBeanMethods = false)
public class TairConfiguration {

    private static JedisPool getStatefulTairSearchConnectionGenericObjectPool(
            TairSearchProperties searchProperties) {
        TairSearchProperties.PoolConfig pool = searchProperties.getPool();
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(pool.getMaxTotal());
        config.setMaxIdle(pool.getMaxIdle());
        config.setMinIdle(pool.getMinIdle());
        config.setMaxWait(Duration.ofMillis(pool.getMaxWait()));
        config.setJmxEnabled(false);
        return new JedisPool(config, searchProperties.getHost(), searchProperties.getPort(),
                searchProperties.getTimeout(), searchProperties.getUsername(),
                searchProperties.getPassword(), searchProperties.getDb());
    }

    // ---------------------------------- 商品 ----------------------------------

    @Bean("goodsTairSearchProperties")
    @ConfigurationProperties(prefix = "configuration.search.tairsearch.goods")
    public TairSearchProperties goodsTairSearchProperties() {
        return new TairSearchProperties();
    }

    @Bean(name = "goodsTairSearchConnectionPool", destroyMethod = "close")
    public JedisPool goodsTairSearchConnectionPool(
            @Qualifier("goodsTairSearchProperties") TairSearchProperties goodsTairSearchProperties) {
        return getStatefulTairSearchConnectionGenericObjectPool(goodsTairSearchProperties);
    }

    @Bean("goodsTairSearchService")
    public TairSearchService goodsTairSearchService(
            @Qualifier("goodsTairSearchConnectionPool") JedisPool pool) {
        return TairSearchService.builder().tairSearch(new TairSearch(pool)).jedisPool(pool)
                .type(SearchDataSourceEnum.GOODS.name()).build();
    }

    // ---------------------------------- 商家订单 ----------------------------------

    @Bean("orderMerchantTairSearchProperties")
    @ConfigurationProperties(prefix = "configuration.search.tairsearch.order.merchant")
    public TairSearchProperties orderMerchantTairSearchProperties() {
        return new TairSearchProperties();
    }

    @Bean(name = "orderMerchantTairSearchConnectionPool", destroyMethod = "close")
    public JedisPool orderMerchantTairSearchConnectionPool(
            @Qualifier("orderMerchantTairSearchProperties") TairSearchProperties orderTairSearchProperties) {
        return getStatefulTairSearchConnectionGenericObjectPool(orderTairSearchProperties);
    }

    @Bean("orderMerchantTairSearchService")
    public TairSearchService orderMerchantTairSearchService(
            @Qualifier("orderMerchantTairSearchConnectionPool") JedisPool pool) {
        return TairSearchService.builder().tairSearch(new TairSearch(pool)).jedisPool(pool)
                .type(SearchDataSourceEnum.ORDER_MERCHANT.name()).build();
    }

    // ---------------------------------- 物料订单 ----------------------------------

    @Bean("orderMaterialTairSearchProperties")
    @ConfigurationProperties(prefix = "configuration.search.tairsearch.order.material")
    public TairSearchProperties orderMaterialTairSearchProperties() {
        return new TairSearchProperties();
    }

    @Bean(name = "orderMaterialTairSearchConnectionPool", destroyMethod = "close")
    public JedisPool orderMaterialTairSearchConnectionPool(
            @Qualifier("orderMaterialTairSearchProperties") TairSearchProperties orderTairSearchProperties) {
        return getStatefulTairSearchConnectionGenericObjectPool(orderTairSearchProperties);
    }

    @Bean("orderMaterialTairSearchService")
    public TairSearchService orderMaterialTairSearchService(
            @Qualifier("orderMaterialTairSearchConnectionPool") JedisPool pool) {
        return TairSearchService.builder().tairSearch(new TairSearch(pool)).jedisPool(pool)
                .type(SearchDataSourceEnum.ORDER_MATERIAL.name()).build();
    }

    // ---------------------------------- 商家订单 ----------------------------------

    @Bean("orderMallTairSearchProperties")
    @ConfigurationProperties(prefix = "configuration.search.tairsearch.order.mall")
    public TairSearchProperties orderMallTairSearchProperties() {
        return new TairSearchProperties();
    }

    @Bean(name = "orderMallTairSearchConnectionPool", destroyMethod = "close")
    public JedisPool orderMallTairSearchConnectionPool(
            @Qualifier("orderMallTairSearchProperties") TairSearchProperties orderTairSearchProperties) {
        return getStatefulTairSearchConnectionGenericObjectPool(orderTairSearchProperties);
    }

    @Bean("orderMallTairSearchService")
    public TairSearchService orderMallTairSearchService(
            @Qualifier("orderMallTairSearchConnectionPool") JedisPool pool) {
        return TairSearchService.builder().tairSearch(new TairSearch(pool)).jedisPool(pool)
                .type(SearchDataSourceEnum.ORDER_MALL.name()).build();
    }

    // ---------------------------------- 物流订单 ----------------------------------

    @Bean("orderLogisticsTairSearchProperties")
    @ConfigurationProperties(prefix = "configuration.search.tairsearch.order.logistics")
    public TairSearchProperties orderLogisticsTairSearchProperties() {
        return new TairSearchProperties();
    }

    @Bean(name = "orderLogisticsTairSearchConnectionPool", destroyMethod = "close")
    public JedisPool orderLogisticsTairSearchConnectionPool(
            @Qualifier("orderLogisticsTairSearchProperties") TairSearchProperties orderTairSearchProperties) {
        return getStatefulTairSearchConnectionGenericObjectPool(orderTairSearchProperties);
    }

    @Bean("orderLogisticsTairSearchService")
    public TairSearchService orderLogisticsTairSearchService(
            @Qualifier("orderLogisticsTairSearchConnectionPool") JedisPool pool) {
        return TairSearchService.builder().tairSearch(new TairSearch(pool)).jedisPool(pool)
                .type(SearchDataSourceEnum.ORDER_LOGISTICS.name()).build();
    }

    // ---------------------------------- 商家 ----------------------------------

    @Bean("corpTairSearchProperties")
    @ConfigurationProperties(prefix = "configuration.search.tairsearch.corp")
    public TairSearchProperties corpTairSearchProperties() {
        return new TairSearchProperties();
    }

    @Bean(name = "corpTairSearchConnectionPool", destroyMethod = "close")
    public JedisPool corpTairSearchConnectionPool(
            @Qualifier("corpTairSearchProperties") TairSearchProperties corpTairSearchProperties) {
        return getStatefulTairSearchConnectionGenericObjectPool(corpTairSearchProperties);
    }

    @Bean("corpTairSearchService")
    public TairSearchService corpTairSearchService(
            @Qualifier("corpTairSearchConnectionPool") JedisPool pool) {
        return TairSearchService.builder().tairSearch(new TairSearch(pool)).jedisPool(pool)
                .type(SearchDataSourceEnum.CORP.name()).build();
    }


}
