package com.xk.search.infrastructure.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import com.myco.mydata.infrastructure.data.annotation.Repository;

/**
 * @author: killer
 **/
@Configuration
@MapperScan(basePackages = "com.xk.search.infrastructure.data.persistence",
        annotationClass = Repository.class, sqlSessionFactoryRef = "defaultSqlSessionFactory")
@ComponentScan({"com.xk.search.infrastructure.convertor", "com.xk.search.infrastructure.cache.dao",
        "com.xk.search.infrastructure.repository", "com.xk.search.infrastructure.service",
        "com.xk.search.infrastructure.config.search", "com.xk.search.infrastructure.adapter"})
public class XkSearchInfrastructureConfig {
}
