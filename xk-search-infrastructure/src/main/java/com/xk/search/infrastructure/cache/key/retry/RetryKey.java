package com.xk.search.infrastructure.cache.key.retry;

import java.io.Serializable;

import com.myco.mydata.infrastructure.cache.key.AbstractCacheKey;

import lombok.*;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class RetryKey extends AbstractCacheKey {

    private Integer channelType;

    private String optType;


    @Override
    public String getKey(Serializable... keys) {
        return super.getKey(channelType, optType);
    }
}
