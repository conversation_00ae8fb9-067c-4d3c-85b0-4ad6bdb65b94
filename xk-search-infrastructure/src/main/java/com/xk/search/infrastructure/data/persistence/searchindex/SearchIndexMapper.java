package com.xk.search.infrastructure.data.persistence.searchindex;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.myco.framework.cache.annotations.GroupStrategy;
import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.search.infrastructure.data.po.searchIndex.SearchIndex;

@Repository
@Table("search_index")
public interface SearchIndexMapper {

    int deleteByType(SearchIndex record);

    int insert(SearchIndex record);

    int insertSelective(SearchIndex record);

    SearchIndex selectByPrimaryKey(@Param("bizType") String bizType,
            @Param("blockType") String blockType, @Param("channelType") Integer channelType);

    int updateByPrimaryKeySelective(SearchIndex record);

    int updateByPrimaryKey(SearchIndex record);

    @GroupStrategy("searchIndexGroup")
    List<SearchIndex> selectByType(SearchIndex searchIndex);
}
