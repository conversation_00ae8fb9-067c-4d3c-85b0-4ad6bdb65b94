package com.xk.search.infrastructure.adapter.retry;

import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.search.domain.commons.StringUtils;
import com.xk.search.domain.model.corp.CorpSearchRoot;
import com.xk.search.domain.model.corp.valobj.CorpIndexValueObject;
import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.domain.model.search.id.SearchIdentifier;
import com.xk.search.domain.model.searchIndex.SearchIndexRoot;
import com.xk.search.domain.model.searchIndex.entity.SearchIndexEntity;
import com.xk.search.domain.model.searchIndex.id.SearchIndexIdentifier;
import com.xk.search.domain.repository.corp.CorpSearchQueryRepository;
import com.xk.search.domain.service.corp.CorpSearchRootService;
import com.xk.search.domain.service.retry.RetrySearchAdapterService;
import com.xk.search.domain.service.retry.RetrySearchRootService;
import com.xk.search.domain.service.searchIndex.SearchIndexRootService;
import com.xk.search.enums.retry.RetryBizTypeEnum;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class CorpRetrySearchAdapterServiceImpl implements RetrySearchAdapterService {

    private final CorpSearchQueryRepository corpSearchQueryRepository;

    private final CorpSearchRootService corpSearchRootService;

    private final SearchIndexRootService searchIndexRootService;

    private final RetrySearchRootService retrySearchRootService;

    @Override
    public RetryBizTypeEnum getBizType() {
        return RetryBizTypeEnum.CORP;
    }

    @Override
    public Mono<Void> updateDocument(Mono<RetrySearchRoot> retrySearchRootMono) {
        return retrySearchRootMono.flatMap(root -> corpSearchQueryRepository
                .getCorpById(Mono
                        .just(LongIdentifier.builder().id(StringUtils.toLong(root.getIdentifier().getId())).build()))
                .flatMap(corpSearchRootService::updateDocument).onErrorResume(throwable -> {
                    log.error("商家更新文档失败：{}", throwable.getMessage());
                    if (root.getRetrySearchEntity().getRetryCount() > 10) {
                        // 大于10 则不进行重试
                        return retrySearchRootService.addErr(root);
                    } else {
                        return retrySearchRootMono
                                .flatMap(identifier -> retrySearchRootService.add(root));
                    }
                }));
    }

    @Override
    public Mono<Void> deleteDocument(Mono<RetrySearchRoot> retrySearchRootMono) {
        return retrySearchRootMono
                .flatMap(root -> corpSearchQueryRepository
                        .getCorpById(Mono.just(
                                LongIdentifier.builder().id(StringUtils.toLong(root.getIdentifier().getId())).build()))
                        .map(searchRoot -> searchIndexRootService
                                .getIndexList(SearchIndexRoot
                                        .builder().identifier(SearchIndexIdentifier
                                                .builder()
                                                .channelType(root.getRetrySearchEntity()
                                                        .getChannelType())
                                                .bizType(SearchBizTypeEnum.CORP.getCode())
                                                .build())
                                        .searchIndexEntity(
                                                SearchIndexEntity.builder().channelType(root
                                                        .getRetrySearchEntity().getChannelType())
                                                        .bizType(SearchBizTypeEnum.CORP
                                                                .getCode())
                                                        .build())
                                        .build())
                                .flatMap(searchIndexEntity -> corpSearchRootService
                                        .deleteDocument(CorpSearchRoot.builder()
                                                .identifier(LongIdentifier.builder()
                                                        .id(StringUtils.toLong(root.getIdentifier().getId())).build())
                                                .searchIdentifier(SearchIdentifier.builder()
                                                        .searchChannelType(
                                                                SearchChannelTypeEnum.getByType(
                                                                        root.getRetrySearchEntity()
                                                                                .getChannelType()))
                                                        .build())
                                                .corpIndexValueObject(CorpIndexValueObject
                                                        .builder()
                                                        .searchBizTypeEnum(
                                                                SearchBizTypeEnum.CORP)
                                                        .build())
                                                .build())))
                        .then().onErrorResume(throwable -> {
                            log.error("商家删除文档失败：{}", throwable.getMessage());
                            if (root.getRetrySearchEntity().getRetryCount() > 10) {
                                // 大于10 则不进行重试
                                return retrySearchRootService.addErr(root);
                            } else {
                                return retrySearchRootMono
                                        .flatMap(identifier -> retrySearchRootService.add(root));
                            }
                        }));
    }

}
