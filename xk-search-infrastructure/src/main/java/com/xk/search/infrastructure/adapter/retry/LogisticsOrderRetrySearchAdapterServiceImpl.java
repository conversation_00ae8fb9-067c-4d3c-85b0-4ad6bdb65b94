package com.xk.search.infrastructure.adapter.retry;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import com.xk.search.domain.commons.StringUtils;
import com.xk.search.domain.dto.LogisticsQueryById;
import com.xk.search.domain.model.logistics.LogisticsSearchRoot;
import com.xk.search.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.search.domain.model.logistics.valobj.LogisticsIndexValueObject;
import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.domain.model.searchIndex.SearchIndexRoot;
import com.xk.search.domain.model.searchIndex.entity.SearchIndexEntity;
import com.xk.search.domain.model.searchIndex.id.SearchIndexIdentifier;
import com.xk.search.domain.repository.logistics.LogisticsSearchQueryRepository;
import com.xk.search.domain.service.logistics.LogisticsSearchRootService;
import com.xk.search.domain.service.retry.RetrySearchAdapterService;
import com.xk.search.domain.service.retry.RetrySearchRootService;
import com.xk.search.domain.service.searchIndex.SearchIndexRootService;
import com.xk.search.enums.retry.RetryBizTypeEnum;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelTypeEnum;
import com.xk.search.enums.search.SearchIndexTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsOrderRetrySearchAdapterServiceImpl implements RetrySearchAdapterService {

    private final LogisticsSearchRootService logisticsSearchRootService;

    private final LogisticsSearchQueryRepository logisticsSearchQueryRepository;

    private final SearchIndexRootService searchIndexRootService;

    private final RetrySearchRootService retrySearchRootService;

    @Override
    public RetryBizTypeEnum getBizType() {
        return RetryBizTypeEnum.ORDER_LOGISTICS;
    }

    @Override
    public Mono<Void> updateDocument(Mono<RetrySearchRoot> retrySearchRootMono) {
        return retrySearchRootMono.flatMap(root -> {
            String param = root.getRetrySearchEntity().getParam();
            JSONObject jsonObject = JSONObject.parseObject(param);
            return logisticsSearchQueryRepository
                    .getLogisticsOrderById(Mono.just(LogisticsQueryById.builder()
                            .logisticsOrderId(StringUtils.toLong(root.getIdentifier().getId()))
                            .logisticsOrderType(LogisticsOrderTypeEnum
                                    .getByCode(StringUtils.toInteger(jsonObject.get("orderType"))))
                            .giftName(jsonObject.get("giftName").toString())
                            .giftAddr(JSONArray.parseArray(jsonObject.get("giftAddr").toString(),
                                    String.class))
                            .build()))
                    .flatMap(logisticsSearchRootService::updateDocument)
                    .onErrorResume(throwable -> {
                        log.error("物流订单更新文档失败：{}", throwable.getMessage());
                        if (root.getRetrySearchEntity().getRetryCount() > 10) {
                            // 大于10 则不进行重试
                            return retrySearchRootService.addErr(root);
                        } else {
                            return retrySearchRootMono
                                    .flatMap(identifier -> retrySearchRootService.add(root));
                        }
                    });
        });
    }

    @Override
    public Mono<Void> deleteDocument(Mono<RetrySearchRoot> retrySearchRootMono) {
        return retrySearchRootMono.flatMap(root -> {
            String param = root.getRetrySearchEntity().getParam();
            JSONObject jsonObject = JSONObject.parseObject(param);
            return logisticsSearchQueryRepository
                    .getLogisticsOrderById(Mono.just(LogisticsQueryById.builder()
                            .logisticsOrderId(StringUtils.toLong(root.getIdentifier().getId()))
                            .logisticsOrderType(LogisticsOrderTypeEnum
                                    .getByCode(StringUtils.toInteger(jsonObject.get("orderType"))))
                            .giftName(jsonObject.get("giftName").toString())
                            .giftAddr(
                                    JSONArray.parseArray(jsonObject.get("giftAddr").toString(),
                                            String.class))
                            .build()))
                    .map(searchRoot -> searchIndexRootService.getIndexList(SearchIndexRoot.builder()
                            .identifier(SearchIndexIdentifier.builder()
                                    .channelType(root.getRetrySearchEntity().getChannelType())
                                    .bizType(SearchBizTypeEnum.ORDER_LOGISTICS.getCode()).build())
                            .searchIndexEntity(SearchIndexEntity.builder()
                                    .channelType(root.getRetrySearchEntity().getChannelType())
                                    .bizType(SearchBizTypeEnum.ORDER_LOGISTICS.getCode()).build())
                            .build())
                            .flatMap(searchIndexEntity -> logisticsSearchRootService
                                    .deleteDocument(LogisticsSearchRoot.builder()
                                            .identifier(LogisticsOrderIdentifier.builder()
                                                    .logisticsOrderId(StringUtils
                                                            .toLong(root.getIdentifier().getId()))
                                                    .searchChannelType(SearchChannelTypeEnum
                                                            .getByType(root.getRetrySearchEntity()
                                                                    .getChannelType()))
                                                    .build())
                                            .logisticsIndexValueObject(LogisticsIndexValueObject
                                                    .builder()
                                                    .searchIndexTypeEnum(
                                                            SearchIndexTypeEnum.ORDER_LOGISTICS)
                                                    .searchBizTypeEnum(
                                                            SearchBizTypeEnum.ORDER_LOGISTICS)
                                                    .logisticsOrderType(
                                                            LogisticsOrderTypeEnum.getByCode(
                                                                    searchIndexEntity.getIdxType()))
                                                    .build())
                                            .build())))
                    .then().onErrorResume(throwable -> {
                        log.error("物流订单删除文档失败：{}", throwable.getMessage());
                        if (root.getRetrySearchEntity().getRetryCount() > 10) {
                            // 大于10 则不进行重试
                            return retrySearchRootService.addErr(root);
                        } else {
                            return retrySearchRootMono
                                    .flatMap(identifier -> retrySearchRootService.add(root));
                        }
                    });
        });
    }

}
