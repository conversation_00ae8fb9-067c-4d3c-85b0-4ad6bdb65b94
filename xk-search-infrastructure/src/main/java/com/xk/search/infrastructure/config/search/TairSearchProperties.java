package com.xk.search.infrastructure.config.search;

import lombok.Getter;
import lombok.Setter;

/**
 * @<PERSON> <PERSON><PERSON><PERSON><PERSON>
 * @Date 2024/8/23 14:18
 */
@Setter
@Getter
public class TairSearchProperties {

    private String host;
    private int port;
    private int db;
    private int timeout;
    private String username;
    private String password;
    private PoolConfig pool;

    @Setter
    @Getter
    public static class PoolConfig {
        private int maxTotal;
        private int maxIdle;
        private int minIdle;
        private long maxWait;
    }
}
