package com.xk.search.infrastructure.service.impl;

import com.myco.mydata.commons.util.CollectionUtil;
import com.xk.search.domain.commons.StringUtils;
import com.xk.search.domain.model.common.SearchSortValueObject;
import com.xk.search.domain.model.corp.CorpSearchRoot;
import com.xk.search.domain.model.corp.valobj.CorpIndexValueObject;
import com.xk.search.domain.model.retrySearch.RetrySearchEntity;
import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.domain.model.retrySearch.id.RetrySearchIdentifier;
import com.xk.search.domain.model.search.SearchEntity;
import com.xk.search.domain.model.search.SearchRoot;
import com.xk.search.domain.model.search.id.SearchIdentifier;
import com.xk.search.domain.model.search.valobj.*;
import com.xk.search.domain.model.searchIndex.SearchIndexRoot;
import com.xk.search.domain.model.searchIndex.entity.SearchIndexEntity;
import com.xk.search.domain.model.searchIndex.id.SearchIndexIdentifier;
import com.xk.search.domain.repository.corp.CorpSearchQueryRepository;
import com.xk.search.domain.repository.search.SearchRootRepository;
import com.xk.search.domain.service.corp.CorpSearchRootService;
import com.xk.search.domain.service.retry.RetrySearchRootService;
import com.xk.search.domain.service.search.SearchRootService;
import com.xk.search.domain.service.searchIndex.SearchIndexRootService;
import com.xk.search.enums.retry.OptTypeEnum;
import com.xk.search.enums.retry.RetryBizTypeEnum;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelDefaultEnum;
import com.xk.search.enums.search.SearchChannelTypeEnum;
import com.xk.search.enums.search.SearchIndexTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

@Slf4j
@Service
@RequiredArgsConstructor
public class CorpSearchRootServiceImpl implements CorpSearchRootService {

    private final CorpSearchQueryRepository corpSearchQueryRepository;

    private final SearchRootRepository searchRootRepository;

    private final SearchRootService searchRootService;

    private final SearchIndexRootService searchIndexRootService;

    private final RetrySearchRootService retrySearchRootService;

    @Override
    public Mono<Void> updateDocument(CorpSearchRoot root) {
        return corpSearchQueryRepository.getCorpById(Mono.just(root.getIdentifier()))
                .flatMap(corpSearchRoot -> {
            String indexName = searchIndexRootService.getIndexName(SearchIndexRoot.builder()
                    .identifier(SearchIndexIdentifier.builder()
                            .bizType(corpSearchRoot.getCorpIndexValueObject().getSearchBizTypeEnum()
                                    .getCode())
                            .channelType(corpSearchRoot.getSearchIdentifier().getSearchChannelType()
                                    .getType())
                            .build())
                    .searchIndexEntity(SearchIndexEntity.builder()
                            .bizType(corpSearchRoot.getCorpIndexValueObject().getSearchBizTypeEnum()
                                    .getCode())
                            .channelType(corpSearchRoot.getSearchIdentifier().getSearchChannelType()
                                    .getType())
                            .build())
                    .build());

            SearchRoot searchRoot = SearchRoot.builder()
                    .identifier(SearchIdentifier.builder()
                            .searchChannelType(
                                    corpSearchRoot.getSearchIdentifier().getSearchChannelType())
                            .build())
                    .searchEntity(SearchEntity.builder()
                            .searchChannelType(
                                    corpSearchRoot.getSearchIdentifier().getSearchChannelType())
                            .serachBizValueObject(SerachBizValueObject.builder()
                                    .searchIndexType(SearchIndexTypeEnum.CORP)
                                    .domainRoot(corpSearchRoot).build())
                            .tairValueObject(TairValueObject.builder().index(indexName)
                                    .docId(StringUtils.toString(corpSearchRoot.getIdentifier().id()))
                                    .build())
                            .build())
                    .build();
            return Mono.just(searchRoot);
        }).flatMap(searchRootRepository::update).onErrorResume(throwable -> {
            log.error("商家更新文档失败：{}", throwable.getMessage());
            return retrySearchRootService.add(RetrySearchRoot.builder()
                            .identifier(RetrySearchIdentifier.builder()
                                    .bizType(RetryBizTypeEnum.CORP).id(StringUtils.toString(root.getIdentifier().id())).build())
                            .retrySearchEntity(RetrySearchEntity.builder().id(StringUtils.toString(root.getIdentifier().id()))
                                    .retryCount(1).optType(OptTypeEnum.CREATE.getCode())
                                    .channelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType().getType())
                                    .bizType(RetryBizTypeEnum.CORP).build())
                            .build());
        });
    }

    @Override
    public Mono<Void> updateDocumentField(CorpSearchRoot root) {
        Function<CorpSearchRoot,String> getIndexNameFunction = searchIndexRoot -> searchIndexRootService.getIndexName(SearchIndexRoot.builder()
                .identifier(SearchIndexIdentifier.builder()
                        .bizType(searchIndexRoot.getCorpIndexValueObject().getSearchBizTypeEnum()
                                .getCode())
                        .channelType(searchIndexRoot.getSearchIdentifier().getSearchChannelType()
                                .getType())
                        .build())
                .searchIndexEntity(SearchIndexEntity.builder()
                        .bizType(searchIndexRoot.getCorpIndexValueObject().getSearchBizTypeEnum()
                                .getCode())
                        .channelType(searchIndexRoot.getSearchIdentifier().getSearchChannelType()
                                .getType())
                        .build())
                .build());

        Function<CorpSearchRoot, SearchRoot> rootSearchRootFunction = corpSearchRoot -> SearchRoot.builder()
                .identifier(SearchIdentifier.builder()
                        .searchChannelType(
                                corpSearchRoot.getSearchIdentifier().getSearchChannelType())
                        .build())
                .searchEntity(SearchEntity.builder()
                        .searchChannelType(
                                corpSearchRoot.getSearchIdentifier().getSearchChannelType())
                        .serachBizValueObject(SerachBizValueObject.builder()
                                .searchIndexType(SearchIndexTypeEnum.CORP)
                                .domainRoot(corpSearchRoot).build())
                        .tairValueObject(TairValueObject.builder().index(getIndexNameFunction.apply(root))
                                .docId(StringUtils.toString(corpSearchRoot.getIdentifier().id()))
                                .build())
                        .build())
                .build();

        Function<Integer,RetrySearchRoot> getRetrySearchRoot = count -> RetrySearchRoot.builder()
                .identifier(RetrySearchIdentifier.builder()
                        .bizType(RetryBizTypeEnum.CORP_FOLLOW).id(StringUtils.toString(root.getIdentifier().id())).build())
                .retrySearchEntity(RetrySearchEntity.builder().id(StringUtils.toString(root.getIdentifier().id()))
                        .retryCount(count).optType(OptTypeEnum.CREATE.getCode())
                        .channelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType().getType())
                        .bizType(RetryBizTypeEnum.CORP_FOLLOW).build())
                .build();

        return Mono.just(rootSearchRootFunction.apply(root))
                .flatMap(searchRootRepository::update)
                .onErrorResume(throwable -> {
            log.error("商家更新文档失败：{}", throwable.getMessage());
            return retrySearchRootService.add(getRetrySearchRoot.apply(1));
        });
    }

    @Override
    public Mono<Void> deleteDocument(CorpSearchRoot root) {
        return  corpSearchQueryRepository
                .getCorpById(Mono.just(root.getIdentifier()))
                .switchIfEmpty(Mono.just(searchIndexRootService.getIndexName(SearchIndexRoot
                        .builder()
                        .identifier(SearchIndexIdentifier.builder()
                                .bizType(SearchBizTypeEnum.CORP.getCode())
                                .channelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType().getType()).build())
                        .searchIndexEntity(SearchIndexEntity.builder()
                                .bizType(SearchBizTypeEnum.CORP.getCode())
                                .channelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType().getType()).build())
                        .build())).flatMap(indexName -> {
                            SearchRoot searchRoot = SearchRoot.builder()
                                    .identifier(SearchIdentifier.builder()
                                            .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType())
                                            .build())
                                    .searchEntity(SearchEntity.builder()
                                            .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType())
                                            .serachBizValueObject(SerachBizValueObject.builder()
                                                    .searchIndexType(SearchIndexTypeEnum.CORP)
                                                    .build())
                                            .tairValueObject(TairValueObject.builder()
                                                    .index(indexName)
                                                    .docId(StringUtils.toString(root.getIdentifier().id())).build())
                                            .build())
                                    .build();
                            return Mono.just(searchRoot);
                        }).flatMap(searchRoot -> {
                            searchRootRepository.remove(searchRoot);
                            return Mono.empty();
                        }))
                .onErrorResume(throwable -> {
                    log.error("商家删除文档失败：{}", throwable.getMessage());
                    return retrySearchRootService
                            .add(RetrySearchRoot.builder()
                                    .identifier(RetrySearchIdentifier.builder()
                                            .bizType(RetryBizTypeEnum.CORP).id(StringUtils.toString(root.getIdentifier().id()))
                                            .build())
                                    .retrySearchEntity(RetrySearchEntity.builder()
                                            .id(StringUtils.toString(root.getIdentifier().id())).retryCount(1)
                                            .optType(OptTypeEnum.DELETE.getCode())
                                            .channelType(
                                                    SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType().getType())
                                            .bizType(RetryBizTypeEnum.CORP).build())
                                    .build())
                            .then(Mono.empty());
                }).then();
    }

    @Override
    public Mono<String> getIndex(CorpSearchRoot root) {
        return searchRootRepository.getIndex(idxBuildIndexSearchRoot(root));
    }

    @Override
    public Mono<Void> addIndex(CorpSearchRoot root) {
        return searchRootRepository.saveIndex(idxBuildIndexSearchRoot(root));
    }

    @Override
    public Mono<Void> updateIndex(CorpSearchRoot root) {
        return searchRootRepository.updateIndex(idxBuildIndexSearchRoot(root));
    }

    @Override
    public Mono<Void> deleteIndex(CorpSearchRoot root) {
        return searchRootRepository.removeIndex(idxBuildIndexSearchRoot(root));
    }

    @Override
    public Mono<SearchDocumentValObj> idxSearchDocument(CorpSearchRoot root) {
        return idxBuilderSearchRoot(root).flatMap(searchRootService::idxSearchDocument);
    }

    public Mono<SearchRoot> idxBuilderSearchRoot(CorpSearchRoot root) {
        LimitValueObject limitValueObject = null;
        if (root.getSearchLimitValueObject() != null) {
            limitValueObject = new LimitValueObject();
            limitValueObject.setLimit(root.getSearchLimitValueObject().getLimit());
            limitValueObject.setOffset(root.getSearchLimitValueObject().getOffset());
            limitValueObject.setKeysCursor(root.getSearchLimitValueObject().getKeysCursor());
        }
        // 排序
        List<SortValueObject> sortValueObjectList = new ArrayList<>();
        if (!CollectionUtil.isNullOrEmpty(root.getSearchSortValueObjectList())) {
            for (SearchSortValueObject searchSortValueObject : root.getSearchSortValueObjectList()) {
                sortValueObjectList.add(SortValueObject.builder().fileName(searchSortValueObject.getFileName()).sortBy(searchSortValueObject.getSortBy()).build());
            }
        }

        SearchChannelTypeEnum searchType = root.getSearchIdentifier().getSearchChannelType();

        return Mono.just(SearchRoot.builder()
                .identifier(SearchIdentifier.builder()
                        .searchChannelType(SearchChannelTypeEnum.getByType(searchType.getType()))
                        .build())
                .sortValueObjectList(sortValueObjectList).limitValueObject(limitValueObject)
                .searchEntity(SearchEntity.builder().searchChannelType(searchType)
                        .serachBizValueObject(SerachBizValueObject.builder()
                                .searchIndexType(SearchIndexTypeEnum.CORP).domainRoot(root)
                                .build())
                        .tairValueObject(TairValueObject.builder()
                                .index(searchIndexRootService.getIndexName(SearchIndexRoot.builder()
                                        .identifier(SearchIndexIdentifier.builder()
                                                .bizType(root.getCorpIndexValueObject()
                                                        .getSearchBizTypeEnum().getCode())
                                                .channelType(root.getSearchIdentifier()
                                                        .getSearchChannelType().getType())
                                                .build())
                                        .searchIndexEntity(SearchIndexEntity.builder()
                                                .channelType(root.getSearchIdentifier()
                                                        .getSearchChannelType().getType())
                                                .bizType(root.getCorpIndexValueObject()
                                                        .getSearchBizTypeEnum().getCode())
                                                .build())
                                        .build()))
                                .docId(StringUtils.toString(root.getIdentifier().id())).build())
                        .build())
                .build());

    }

    private SearchRoot idxBuildIndexSearchRoot(CorpSearchRoot root) {
        CorpIndexValueObject corpIndexValueObject = root.getCorpIndexValueObject();
        return SearchRoot.builder().identifier(SearchIdentifier.builder()
                        .searchChannelType(root.getSearchIdentifier().getSearchChannelType()).build())
                .searchEntity(SearchEntity.builder()
                        .searchChannelType(root.getSearchIdentifier().getSearchChannelType())
                        .serachBizValueObject(SerachBizValueObject.builder()
                                .searchIndexType(SearchIndexTypeEnum.CORP).domainRoot(root)
                                .build())
                        .tairValueObject(TairValueObject.builder().index(searchIndexRootService
                                        .getIndexName(SearchIndexRoot.builder()
                                                .identifier(SearchIndexIdentifier.builder()
                                                        .bizType(corpIndexValueObject
                                                                .getSearchBizTypeEnum().getCode())
                                                        .channelType(root.getSearchIdentifier()
                                                                .getSearchChannelType().getType())
                                                        .build())
                                                .searchIndexEntity(SearchIndexEntity.builder()
                                                        .channelType(root.getSearchIdentifier()
                                                                .getSearchChannelType().getType())
                                                        .bizType(corpIndexValueObject
                                                                .getSearchBizTypeEnum().getCode())
                                                        .build())
                                                .build()))
                                .build())
                        .build())
                .build();
    }

}
