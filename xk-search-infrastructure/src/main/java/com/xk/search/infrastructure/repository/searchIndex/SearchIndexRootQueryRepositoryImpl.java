package com.xk.search.infrastructure.repository.searchIndex;

import org.springframework.stereotype.Repository;

import com.xk.search.domain.model.searchIndex.SearchIndexRoot;
import com.xk.search.domain.model.searchIndex.entity.SearchIndexEntity;
import com.xk.search.domain.repository.searchindex.SearchIndexRootQueryRepository;
import com.xk.search.infrastructure.data.persistence.searchindex.SearchIndexMapper;
import com.xk.search.infrastructure.data.po.searchIndex.SearchIndex;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @Date 2024/8/8 17:22
 */
@Repository
@RequiredArgsConstructor
public class SearchIndexRootQueryRepositoryImpl implements SearchIndexRootQueryRepository {

    private final SearchIndexMapper searchIndexMapper;

    private final Converter converter;

    @Override
    public Flux<SearchIndexEntity> getIndexList(SearchIndexRoot searchIndexRoot) {
        return this.find(
                SearchIndex.builder().bizType(searchIndexRoot.getSearchIndexEntity().getBizType())
                        .channelType(searchIndexRoot.getSearchIndexEntity().getChannelType())
                        .build(),
                sSearchIndex -> searchIndexMapper.selectByType(sSearchIndex).stream().filter(x -> {
                    if (searchIndexRoot.getSearchIndexEntity().getIdxType() != null
                            && !searchIndexRoot.getSearchIndexEntity().getIdxType().equals("")) {
                        return x.getIdxType() == null || x.getIdxType()
                                .equals(searchIndexRoot.getSearchIndexEntity().getIdxType());
                    }
                    if (searchIndexRoot.getSearchIndexEntity().getIdxStatus() != null
                            && !searchIndexRoot.getSearchIndexEntity().getIdxStatus().equals("")) {
                        return x.getIdxStatus() == null || x.getIdxStatus()
                                .equals(searchIndexRoot.getSearchIndexEntity().getIdxStatus());
                    }
                    return true;
                }).toList(), SearchIndexEntity.class, this.converter::convert);
    }
}
