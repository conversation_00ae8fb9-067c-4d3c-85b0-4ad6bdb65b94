package com.xk.search.infrastructure.adapter.retry;

import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.search.domain.commons.StringUtils;
import com.xk.search.domain.model.goods.GoodsSearchRoot;
import com.xk.search.domain.model.goods.valobj.GoodsIndexValueObject;
import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.domain.model.search.id.SearchIdentifier;
import com.xk.search.domain.model.searchIndex.SearchIndexRoot;
import com.xk.search.domain.model.searchIndex.entity.SearchIndexEntity;
import com.xk.search.domain.model.searchIndex.id.SearchIndexIdentifier;
import com.xk.search.domain.repository.goods.GoodsSearchQueryRepository;
import com.xk.search.domain.service.goods.GoodsSearchRootService;
import com.xk.search.domain.service.retry.RetrySearchAdapterService;
import com.xk.search.domain.service.retry.RetrySearchRootService;
import com.xk.search.domain.service.searchIndex.SearchIndexRootService;
import com.xk.search.enums.retry.RetryBizTypeEnum;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class MallRetrySearchAdapterServiceImpl implements RetrySearchAdapterService {

    private final GoodsSearchQueryRepository goodsSearchQueryRepository;

    private final GoodsSearchRootService goodsSearchRootService;

    private final SearchIndexRootService searchIndexRootService;

    private final RetrySearchRootService retrySearchRootService;

    @Override
    public RetryBizTypeEnum getBizType() {
        return RetryBizTypeEnum.MALL_PRODUCT;
    }

    @Override
    public Mono<Void> updateDocument(Mono<RetrySearchRoot> retrySearchRootMono) {
        return retrySearchRootMono.flatMap(root -> goodsSearchQueryRepository
                .getGoodsMallById(Mono
                        .just(LongIdentifier.builder().id(StringUtils.toLong(root.getIdentifier().getId())).build()))
                .flatMap(goodsSearchRootService::updateDocument)
                .onErrorResume(throwable -> {
                    log.error("商城商品更新文档失败：{}", throwable.getMessage());
                    if (root.getRetrySearchEntity().getRetryCount() > 10) {
                        // 大于10 则不进行重试
                        return retrySearchRootService.addErr(root);
                    } else {
                        return retrySearchRootMono
                                .flatMap(identifier -> retrySearchRootService.add(root));
                    }
                }));
    }

    @Override
    public Mono<Void> deleteDocument(Mono<RetrySearchRoot> retrySearchRootMono) {
        return retrySearchRootMono.flatMap(root -> goodsSearchQueryRepository
                .getGoodsMallById(Mono
                        .just(LongIdentifier.builder().id(StringUtils.toLong(root.getIdentifier().getId())).build()))
                .map(searchRoot -> searchIndexRootService
                        .getIndexList(SearchIndexRoot.builder().identifier(SearchIndexIdentifier
                                .builder().channelType(root.getRetrySearchEntity().getChannelType())
                                .bizType(SearchBizTypeEnum.GOODS_MALL.getCode()).build())
                                .searchIndexEntity(SearchIndexEntity.builder()
                                        .channelType(root.getRetrySearchEntity().getChannelType())
                                        .bizType(SearchBizTypeEnum.GOODS_MALL.getCode()).build())
                                .build())
                        .flatMap(searchIndexEntity -> goodsSearchRootService
                                .deleteDocument(GoodsSearchRoot.builder()
                                        .identifier(LongIdentifier.builder()
                                                .id(StringUtils.toLong(root.getIdentifier().getId())).build())
                                        .searchIdentifier(SearchIdentifier.builder()
                                                .searchChannelType(SearchChannelTypeEnum
                                                        .getByType(root.getRetrySearchEntity()
                                                                .getChannelType()))
                                                .build())
                                        .goodsIndexValueObject(GoodsIndexValueObject.builder()
                                                .searchBizTypeEnum(SearchBizTypeEnum.GOODS_MALL)
                                                .blockType(searchIndexEntity.getIdxType())
                                                .build())
                                        .build())))
                .then().onErrorResume(throwable -> {
                    log.error("商城商品删除文档失败：{}", throwable.getMessage());
                    if (root.getRetrySearchEntity().getRetryCount() > 10) {
                        // 大于10 则不进行重试
                        return retrySearchRootService.addErr(root);
                    } else {
                        return retrySearchRootMono
                                .flatMap(identifier -> retrySearchRootService.add(root));
                    }
                }));
    }

}
