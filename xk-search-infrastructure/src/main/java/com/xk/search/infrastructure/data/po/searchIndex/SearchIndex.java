package com.xk.search.infrastructure.data.po.searchIndex;

import com.myco.framework.cache.CacheLevel;
import com.myco.framework.cache.annotations.Cache;
import com.myco.framework.cache.annotations.Group;
import com.myco.framework.cache.annotations.KeyProperty;
import com.xk.search.domain.model.searchIndex.entity.SearchIndexEntity;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Cache(level = CacheLevel.REDIS)
@AutoMappers({@AutoMapper(target = SearchIndexEntity.class, reverseConvertGenerate = false)})
public class SearchIndex {

    @KeyProperty(value = 1)
    private Long id;

    /**
     * 业务类型
     */
    @Group(name = "searchIndexGroup", value = 1)
    private String bizType;

    /**
     * 板块
     */
    private Integer idxType;

    /**
     * 板块
     */
    private Integer idxStatus;

    /**
     * 搜索类型：1、redisSearch 2、es 3、tairSearch
     */
    @Group(name = "searchIndexGroup", value = 2)
    private Integer channelType;

    /**
     * 索引名
     */
    private String searchIndex;

}
