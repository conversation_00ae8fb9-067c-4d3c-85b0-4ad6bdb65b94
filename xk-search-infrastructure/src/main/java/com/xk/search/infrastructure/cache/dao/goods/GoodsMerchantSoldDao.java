package com.xk.search.infrastructure.cache.dao.goods;

import com.myco.mydata.infrastructure.cache.dao.StringCacheDao;
import com.xk.search.infrastructure.cache.key.goods.GoodsMerchantRecommendKey;
import com.xk.search.infrastructure.cache.key.goods.GoodsMerchantSoldKey;
import org.springframework.stereotype.Repository;

@Repository
public class GoodsMerchantSoldDao extends StringCacheDao<GoodsMerchantSoldKey, String> {
}
