package com.xk.search.infrastructure.repository.search;

import com.myco.mydata.commons.annotation.BeansOfTypeToMap;
import com.xk.search.domain.model.search.SearchRoot;
import com.xk.search.domain.model.search.valobj.SearchDocumentValObj;
import com.xk.search.domain.repository.search.SearchRootQueryRepository;
import com.xk.search.domain.service.search.SearchAdapterService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/8/8 17:29
 */
@Repository
@RequiredArgsConstructor
public class SearchRootQueryRepositoryImpl implements SearchRootQueryRepository {

    @BeansOfTypeToMap(value = SearchAdapterService.class, methodName = "getChannelType")
    private final Map<Integer, SearchAdapterService> searchAdapterServiceMap;

    @Override
    public Mono<SearchDocumentValObj> idxSearchDocument(SearchRoot root) {
        return Mono.fromCallable(() -> {
            SearchAdapterService adapterService = searchAdapterServiceMap
                    .get(root.getIdentifier().getSearchChannelType().getType());
            return adapterService.idxSearchDocument(root);
        });
    }

    @Override
    public Mono<SearchDocumentValObj> idxMSearchDocument(SearchRoot root) {
        return Mono.fromCallable(() -> {
            SearchAdapterService adapterService = searchAdapterServiceMap
                    .get(root.getIdentifier().getSearchChannelType().getType());
            return adapterService.idxMSearchDocument(root);
        });
    }
}
