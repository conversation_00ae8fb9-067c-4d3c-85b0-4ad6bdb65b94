package com.xk.search.infrastructure.config.search;

import java.util.function.Function;

import com.aliyun.tair.tairsearch.TairSearch;

import io.valkey.JedisPool;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TairSearchService {

    @Getter
    private final String type;

    private final TairSearch tairSearch;

    private final JedisPool jedisPool;

    @Builder
    public TairSearchService(String type, TairSearch tairSearch, JedisPool jedisPool) {
        this.type = type;
        this.tairSearch = tairSearch;
        this.jedisPool = jedisPool;
    }

    public <R> R performOperationTairSearch(Function<TairSearch, R> operation) {
        try {
            return operation.apply(tairSearch);
        } catch (Exception e) {
            log.error("", e);
            // 处理异常
            throw new RuntimeException("Failed to execute Redis operation", e);
        }
    }

    public <R> R performOperationJedisPool(Function<JedisPool, R> operation) {
        try {
            return operation.apply(jedisPool);
        } catch (Exception e) {
            // 处理异常
            log.error("", e);
            throw new RuntimeException("Failed to execute Redis operation", e);
        }
    }

}
