package com.xk.search.infrastructure.cache.dao.retry;

import com.myco.mydata.infrastructure.cache.dao.ListCacheDao;
import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.infrastructure.cache.key.retry.RetryErrKey;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR>
 */
@Repository
public class RetryErrDao extends ListCacheDao<RetryErrKey, RetrySearchRoot> {

}
