<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.search.infrastructure.data.persistence.searchindex.SearchIndexMapper">
    <resultMap id="BaseResultMap" type="com.xk.search.infrastructure.data.po.searchIndex.SearchIndex">
        <!--@mbg.generated-->
        <!--@Table search_index-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <id column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <id column="idx_type" jdbcType="VARCHAR" property="idxType"/>
        <id column="idx_status" jdbcType="VARCHAR" property="idxStatus"/>
        <id column="channel_type" jdbcType="INTEGER" property="channelType"/>
        <result column="search_index" jdbcType="VARCHAR" property="searchIndex"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, biz_type, idx_type,idx_status, channel_type, search_index
    </sql>
    <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from search_index
        where biz_type = #{bizType,jdbcType=VARCHAR}
        and idx_type = #{idxType,jdbcType=VARCHAR}
        and idx_status = #{idxStatus,jdbcType=VARCHAR}
        and channel_type = #{channelType,jdbcType=INTEGER}
    </select>
    <delete id="deleteByType" parameterType="map">
        <!--@mbg.generated-->
        delete from search_index
        where biz_type = #{bizType,jdbcType=VARCHAR}
        <if test="idxType != null">
            and idx_type = #{idxType,jdbcType=VARCHAR}
        </if>
        <if test="idxStatus != null">
            and idx_status = #{idxStatus,jdbcType=VARCHAR}
        </if>
        and channel_type = #{channelType,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.xk.search.infrastructure.data.po.searchIndex.SearchIndex">
        <!--@mbg.generated-->
        insert into search_index (biz_type, idx_type,idx_status,
        channel_type, search_index)
        values (#{bizType,jdbcType=VARCHAR}, #{idxType,jdbcType=INTEGER},#{idxStatus,jdbcType=INTEGER}
        #{channelType,jdbcType=INTEGER}, #{searchIndex,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.xk.search.infrastructure.data.po.searchIndex.SearchIndex">
        <!--@mbg.generated-->
        insert into search_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="bizType != null">
                biz_type,
            </if>
            <if test="idxType != null">
                idx_type,
            </if>
            <if test="idxStatus != null">
                idx_status,
            </if>
            <if test="channelType != null">
                channel_type,
            </if>
            <if test="searchIndex != null">
                search_index,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=INTEGER},
            <if test="bizType != null">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="idxType != null">
                #{idxType,jdbcType=INTEGER},
            </if>
            <if test="idxStatus != null">
                #{idxStatus,jdbcType=INTEGER},
            </if>
            <if test="channelType != null">
                #{channelType,jdbcType=INTEGER},
            </if>
            <if test="searchIndex != null">
                #{searchIndex,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xk.search.infrastructure.data.po.searchIndex.SearchIndex">
        <!--@mbg.generated-->
        update search_index
        <set>
            <if test="searchIndex != null">
                search_index = #{searchIndex,jdbcType=VARCHAR},
            </if>
        </set>
        where biz_type = #{bizType,jdbcType=VARCHAR}
        <if test="idxType != null">
            and idx_type = #{idxType,jdbcType=VARCHAR}
        </if>
        <if test="idxStatus != null">
            and idx_status = #{idxStatus,jdbcType=VARCHAR}
        </if>
        and channel_type = #{channelType,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.search.infrastructure.data.po.searchIndex.SearchIndex">
        <!--@mbg.generated-->
        update search_index
        set search_index = #{searchIndex,jdbcType=VARCHAR}
        where biz_type = #{bizType,jdbcType=VARCHAR}
        <if test="idxType != null">
            and idx_type = #{idxType,jdbcType=VARCHAR}
        </if>
        <if test="idxStatus != null">
            and idx_status = #{idxStatus,jdbcType=VARCHAR}
        </if>
        and channel_type = #{channelType,jdbcType=INTEGER}
    </update>

    <select id="selectByType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM search_index
        WHERE biz_type = #{bizType,jdbcType=VARCHAR}
        and channel_type = #{channelType,jdbcType=INTEGER}
    </select>
</mapper>