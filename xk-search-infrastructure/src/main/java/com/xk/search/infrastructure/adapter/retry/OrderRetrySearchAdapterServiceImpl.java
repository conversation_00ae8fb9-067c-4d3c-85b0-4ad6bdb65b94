package com.xk.search.infrastructure.adapter.retry;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.search.domain.model.order.OrderSearchRoot;
import com.xk.search.domain.model.order.id.OrderSearchIdentifier;
import com.xk.search.domain.model.order.valobj.OrderIndexValueObject;
import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.domain.model.searchIndex.SearchIndexRoot;
import com.xk.search.domain.model.searchIndex.entity.SearchIndexEntity;
import com.xk.search.domain.model.searchIndex.id.SearchIndexIdentifier;
import com.xk.search.domain.repository.order.OrderSearchQueryRepository;
import com.xk.search.domain.service.order.OrderSearchRootService;
import com.xk.search.domain.service.retry.RetrySearchAdapterService;
import com.xk.search.domain.service.retry.RetrySearchRootService;
import com.xk.search.domain.service.searchIndex.SearchIndexRootService;
import com.xk.search.enums.retry.RetryBizTypeEnum;
import com.xk.search.enums.search.SearchChannelTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderRetrySearchAdapterServiceImpl implements RetrySearchAdapterService {

    private final OrderSearchQueryRepository orderSearchQueryRepository;

    private final OrderSearchRootService orderSearchRootService;

    private final SearchIndexRootService searchIndexRootService;

    private final RetrySearchRootService retrySearchRootService;

    @Override
    public RetryBizTypeEnum getBizType() {
        return RetryBizTypeEnum.ORDER;
    }

    @Override
    public Mono<Void> updateDocument(Mono<RetrySearchRoot> retrySearchRootMono) {
        return retrySearchRootMono.flatMap(root -> orderSearchQueryRepository
                .getOrderById(Mono
                        .just(StringIdentifier.builder().id(root.getIdentifier().getId()).build()))
                .flatMap(orderSearchRootService::updateDocument).onErrorResume(throwable -> {
                    log.error("订单更新文档失败：{}", throwable.getMessage());
                    if (root.getRetrySearchEntity().getRetryCount() > 10) {
                        // 大于10 则不进行重试
                        return retrySearchRootService.addErr(root);
                    } else {
                        return retrySearchRootMono
                                .flatMap(identifier -> retrySearchRootService.add(root));
                    }
                }));
    }

    @Override
    public Mono<Void> deleteDocument(Mono<RetrySearchRoot> retrySearchRootMono) {
        return retrySearchRootMono
                .flatMap(root -> orderSearchQueryRepository
                        .getOrderById(Mono.just(StringIdentifier.builder()
                                .id(root.getIdentifier().getId()).build()))
                        .map(searchRoot -> searchIndexRootService
                                .getIndexList(SearchIndexRoot
                                        .builder().identifier(SearchIndexIdentifier
                                                .builder()
                                                .channelType(root.getRetrySearchEntity()
                                                        .getChannelType())
                                                .bizType(
                                                        searchRoot.getOrderIndexValueObject()
                                                                .getSearchBizTypeEnum().getCode())
                                                .build())
                                        .searchIndexEntity(SearchIndexEntity
                                                .builder().channelType(root.getRetrySearchEntity()
                                                        .getChannelType())
                                                .bizType(searchRoot.getOrderIndexValueObject()
                                                        .getSearchBizTypeEnum().getCode())
                                                .build())
                                        .build())
                                .flatMap(searchIndexEntity -> orderSearchRootService.deleteDocument(
                                        OrderSearchRoot.builder().identifier(OrderSearchIdentifier
                                                .builder().orderNo(root.getIdentifier().getId())
                                                .searchChannelType(SearchChannelTypeEnum
                                                        .getByType(root.getRetrySearchEntity()
                                                                .getChannelType()))
                                                .build())
                                                .orderIndexValueObject(OrderIndexValueObject
                                                        .builder()
                                                        .searchBizTypeEnum(searchRoot
                                                                .getOrderIndexValueObject()
                                                                .getSearchBizTypeEnum())
                                                        .searchIndexTypeEnum(searchRoot
                                                                .getOrderIndexValueObject()
                                                                .getSearchIndexTypeEnum())
                                                        .productType(searchIndexEntity.getIdxType())
                                                        .orderStatus(
                                                                searchIndexEntity.getIdxStatus())
                                                        .build())
                                                .build())))
                        .then().onErrorResume(throwable -> {
                            log.error("订单删除文档失败：{}", throwable.getMessage());
                            if (root.getRetrySearchEntity().getRetryCount() > 10) {
                                // 大于10 则不进行重试
                                return retrySearchRootService.addErr(root);
                            } else {
                                return retrySearchRootMono
                                        .flatMap(identifier -> retrySearchRootService.add(root));
                            }
                        }));
    }

}
