package com.xk.search.infrastructure.repository.search;

import java.util.Map;

import org.springframework.stereotype.Repository;

import com.myco.mydata.commons.annotation.BeansOfTypeToMap;
import com.myco.mydata.domain.model.Identifier;
import com.xk.search.domain.model.search.SearchRoot;
import com.xk.search.domain.repository.search.SearchRootRepository;
import com.xk.search.domain.service.search.SearchAdapterService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * <AUTHOR>
 * @Date 2024/8/8 17:22
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class SearchRootRepositoryImpl implements SearchRootRepository {

    @BeansOfTypeToMap(value = SearchAdapterService.class, methodName = "getChannelType")
    private Map<Integer, SearchAdapterService> searchAdapterServiceMap;

    @Override
    public Mono<Void> saveIndex(SearchRoot root) {
        return Mono.fromRunnable(() -> {
            SearchAdapterService adapterService = searchAdapterServiceMap
                    .get(root.getIdentifier().getSearchChannelType().getType());
            adapterService.createIndex(root);
        });
    }

    @Override
    public Mono<Void> removeIndex(SearchRoot root) {
        return Mono.fromRunnable(() -> {
            SearchAdapterService adapterService = searchAdapterServiceMap
                    .get(root.getIdentifier().getSearchChannelType().getType());
            adapterService.deleteIndex(root);
        });
    }

    @Override
    public Mono<Void> alter(SearchRoot root) {
        return Mono.fromRunnable(() -> {
            SearchAdapterService adapterService = searchAdapterServiceMap
                    .get(root.getIdentifier().getSearchChannelType().getType());
            adapterService.alter(root);
        });
    }

    @Override
    public Mono<Void> updateIndex(SearchRoot root) {
        return Mono.fromRunnable(() -> {
            SearchAdapterService adapterService = searchAdapterServiceMap
                    .get(root.getIdentifier().getSearchChannelType().getType());
            adapterService.updateIndex(root);
        });
    }

    @Override
    public Mono<String> getIndex(SearchRoot root) {
        return Mono.fromCallable(() -> {
            SearchAdapterService adapterService = searchAdapterServiceMap
                    .get(root.getIdentifier().getSearchChannelType().getType());
            return adapterService.getIndex(root);
        });
    }

    @Override
    public Mono<Void> save(SearchRoot root) {
        return Mono.fromRunnable(() -> {
            SearchAdapterService adapterService = searchAdapterServiceMap
                    .get(root.getIdentifier().getSearchChannelType().getType());
            adapterService.createDocument(root);
        }).then();
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(SearchRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(SearchRoot root) {
        return Mono.fromRunnable(() -> {
            SearchAdapterService adapterService = searchAdapterServiceMap
                    .get(root.getIdentifier().getSearchChannelType().getType());
            adapterService.updateDocument(root);
        });
    }

    @Override
    public Mono<Void> remove(SearchRoot root) {
        return Mono.fromRunnable(() -> {
            SearchAdapterService adapterService = searchAdapterServiceMap
                    .get(root.getIdentifier().getSearchChannelType().getType());
            adapterService.deleteDocument(root);
        });
    }
}
