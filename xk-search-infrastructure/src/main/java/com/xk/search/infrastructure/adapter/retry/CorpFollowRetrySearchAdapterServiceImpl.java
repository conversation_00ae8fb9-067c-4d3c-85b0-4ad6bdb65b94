package com.xk.search.infrastructure.adapter.retry;

import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.search.domain.commons.StringUtils;
import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.domain.repository.corp.CorpSearchQueryRepository;
import com.xk.search.domain.service.corp.CorpSearchRootService;
import com.xk.search.domain.service.retry.RetrySearchAdapterService;
import com.xk.search.domain.service.retry.RetrySearchRootService;
import com.xk.search.domain.service.searchIndex.SearchIndexRootService;
import com.xk.search.enums.retry.RetryBizTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class CorpFollowRetrySearchAdapterServiceImpl implements RetrySearchAdapterService {

    private final CorpSearchQueryRepository corpSearchQueryRepository;

    private final CorpSearchRootService corpSearchRootService;

    private final SearchIndexRootService searchIndexRootService;

    private final RetrySearchRootService retrySearchRootService;

    @Override
    public RetryBizTypeEnum getBizType() {
        return RetryBizTypeEnum.CORP_FOLLOW;
    }

    @Override
    public Mono<Void> updateDocument(Mono<RetrySearchRoot> retrySearchRootMono) {
        return retrySearchRootMono.flatMap(root -> corpSearchQueryRepository
                .getCorpFollowById(Mono.just(LongIdentifier.builder().id(StringUtils.toLong(root.getIdentifier().getId())).build()))
                .flatMap(corpSearchRootService::updateDocumentField).onErrorResume(throwable -> {
                    log.error("商家粉丝数更新文档失败：{}", throwable.getMessage());
                    if (root.getRetrySearchEntity().getRetryCount() > 10) {
                        // 大于10 则不进行重试
                        return retrySearchRootService.addErr(root);
                    } else {
                        return retrySearchRootMono
                                .flatMap(identifier -> retrySearchRootService.add(root));
                    }
                }));
    }

    @Override
    public Mono<Void> deleteDocument(Mono<RetrySearchRoot> retrySearchRootMono) {
        return Mono.empty();
    }


}
