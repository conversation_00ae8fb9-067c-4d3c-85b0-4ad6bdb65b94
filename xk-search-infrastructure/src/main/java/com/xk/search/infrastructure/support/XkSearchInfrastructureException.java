package com.xk.search.infrastructure.support;

import com.myco.mydata.domain.model.exception.wrapper.InfrastructureWrapperThrowable;
import com.xk.search.infrastructure.commons.XkSearchInfrastructureErrorEnum;

/**
 * @author: killer
 **/
public class XkSearchInfrastructureException extends InfrastructureWrapperThrowable {

    public XkSearchInfrastructureException(XkSearchInfrastructureErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

    public XkSearchInfrastructureException(XkSearchInfrastructureErrorEnum exceptionIdentifier,
            Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

}
