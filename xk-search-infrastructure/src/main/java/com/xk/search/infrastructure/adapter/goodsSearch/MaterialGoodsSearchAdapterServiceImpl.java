package com.xk.search.infrastructure.adapter.goodsSearch;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.search.domain.commons.StringUtils;
import com.xk.search.domain.model.goods.GoodsSearchRoot;
import com.xk.search.domain.model.goods.valobj.GoodsIndexValueObject;
import com.xk.search.domain.model.retrySearch.RetrySearchEntity;
import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.domain.model.retrySearch.id.RetrySearchIdentifier;
import com.xk.search.domain.model.search.id.SearchIdentifier;
import com.xk.search.domain.model.searchIndex.SearchIndexRoot;
import com.xk.search.domain.model.searchIndex.entity.SearchIndexEntity;
import com.xk.search.domain.model.searchIndex.id.SearchIndexIdentifier;
import com.xk.search.domain.repository.goods.GoodsSearchQueryRepository;
import com.xk.search.domain.service.goods.GoodsSearchAdapterService;
import com.xk.search.domain.service.goods.GoodsSearchRootService;
import com.xk.search.domain.service.retry.RetrySearchRootService;
import com.xk.search.domain.service.searchIndex.SearchIndexRootService;
import com.xk.search.enums.retry.OptTypeEnum;
import com.xk.search.enums.retry.RetryBizTypeEnum;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelDefaultEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialGoodsSearchAdapterServiceImpl implements GoodsSearchAdapterService {

    private final GoodsSearchQueryRepository goodsSearchQueryRepository;

    private final GoodsSearchRootService goodsSearchRootService;

    private final SearchIndexRootService searchIndexRootService;

    private final RetrySearchRootService retrySearchRootService;

    @Override
    public Integer getBizType() {
        return GoodsTypeEnum.MATERIAL_PRODUCT.getCode();
    }

    @Override
    public Mono<Void> createDocument(Mono<LongIdentifier> identifierMono) {
        return updateDocument(identifierMono);
    }

    @Override
    public Mono<Void> updateDocument(Mono<LongIdentifier> identifierMono) {
        return goodsSearchQueryRepository.getGoodsMaterialById(identifierMono)
                .flatMap(goodsSearchRootService::updateDocument).onErrorResume(throwable -> {
                    log.error("物料商品更新文档失败：{}", throwable.getMessage());
                    return identifierMono.flatMap(
                            identifier -> retrySearchRootService.add(RetrySearchRoot.builder()
                                    .identifier(RetrySearchIdentifier.builder()
                                            .bizType(RetryBizTypeEnum.MERCHANT_PRODUCT)
                                            .id(StringUtils.toString(identifier.id())).build())
                                    .retrySearchEntity(RetrySearchEntity.builder()
                                            .id(StringUtils.toString(identifier.id())).retryCount(1)
                                            .optType(OptTypeEnum.CREATE.getCode())
                                            .channelType(
                                                    SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType().getType())
                                            .bizType(RetryBizTypeEnum.MERCHANT_PRODUCT)
                                            .build())
                                    .build()));
                });
    }

    @Override
    public Mono<Void> updateDocumentField(Mono<GoodsSearchRoot> searchRootMono) {
        return null;
    }

    @Override
    public Mono<Void> deleteDocument(Mono<LongIdentifier> identifierMono) {
        return identifierMono.flatMap(identifier -> goodsSearchQueryRepository
                .getGoodsMaterialById(identifierMono)
                .switchIfEmpty(searchIndexRootService.getIndexList(SearchIndexRoot
                                .builder()
                                .identifier(SearchIndexIdentifier
                                        .builder().bizType(SearchBizTypeEnum.GOODS_MATERIAL.getCode())
                                        .channelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType().getType()).build())
                                .searchIndexEntity(SearchIndexEntity.builder()
                                        .bizType(SearchBizTypeEnum.GOODS_MATERIAL.getCode())
                                        .channelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType().getType()).build())
                                .build())
                        .flatMap(searchIndexEntity -> goodsSearchRootService
                                .deleteDocument(GoodsSearchRoot.builder()
                                        .identifier(LongIdentifier.builder().id(identifier.id())
                                                .build())
                                        .searchIdentifier(SearchIdentifier.builder()
                                                .searchChannelType(
                                                        SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType())
                                                .build())
                                        .goodsIndexValueObject(GoodsIndexValueObject.builder()
                                                .blockType(searchIndexEntity.getIdxType())
                                                .searchBizTypeEnum(SearchBizTypeEnum.GOODS_MATERIAL)
                                                .build())
                                        .build()))
                        .then(Mono.empty()))
                .onErrorResume(throwable -> {
                    log.error("物料商品删除文档失败：{}", throwable.getMessage());
                    return retrySearchRootService.add(RetrySearchRoot.builder()
                            .identifier(RetrySearchIdentifier.builder()
                                    .bizType(RetryBizTypeEnum.MERCHANT_PRODUCT)
                                    .id(StringUtils.toString(identifier.id())).build())
                            .retrySearchEntity(RetrySearchEntity.builder()
                                    .id(StringUtils.toString(identifier.id())).retryCount(1)
                                    .optType(OptTypeEnum.DELETE.getCode())
                                    .channelType(
                                            SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType().getType())
                                    .bizType(RetryBizTypeEnum.MERCHANT_PRODUCT).build())
                            .build()).then(Mono.empty());
                }).then());
    }

}
