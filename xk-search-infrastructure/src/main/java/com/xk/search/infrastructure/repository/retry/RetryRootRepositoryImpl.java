package com.xk.search.infrastructure.repository.retry;


import com.xk.search.domain.model.retrySearch.RetrySearchRoot;
import com.xk.search.domain.repository.retry.RetryRootRepository;
import com.xk.search.infrastructure.cache.dao.retry.RetryDao;
import com.xk.search.infrastructure.cache.dao.retry.RetryErrDao;
import com.xk.search.infrastructure.cache.key.retry.RetryErrKey;
import com.xk.search.infrastructure.cache.key.retry.RetryKey;
import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/8 17:22
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class RetryRootRepositoryImpl implements RetryRootRepository {

    private final RetryDao retryDao;

    private final RetryErrDao retryErrDao;

    @Override
    public Mono<Void> add(RetrySearchRoot root) {
        retryDao.addValue(RetryKey.builder()
                .channelType(root.getRetrySearchEntity().getChannelType())
                .optType(root.getRetrySearchEntity().getOptType())
                .build(),root);
        return Mono.empty();
    }

    @Override
    public Mono<Void> addErr(RetrySearchRoot root) {
        retryErrDao.addValue(RetryErrKey.builder()
                .channelType(root.getRetrySearchEntity().getChannelType())
                .optType(root.getRetrySearchEntity().getOptType())
                .build(),root);
        return Mono.empty();
    }

    @Override
    public Mono<Void> save(RetrySearchRoot root) {
        return null;
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(RetrySearchRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(RetrySearchRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(RetrySearchRoot root) {
        return null;
    }
}
