<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.search</groupId>
        <artifactId>xk-search</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-search-infrastructure</artifactId>
    <packaging>jar</packaging>
    <name>xk-search-infrastructure</name>
    <description>xk-search-infrastructure</description>
    <properties>
        <mapstruct-plus.mapperPackage>com.xk.search.infrastructure.convertor.auto</mapstruct-plus.mapperPackage>
        <mapstruct-plus.adapterPackage>com.xk.search.infrastructure.convertor.adapter</mapstruct-plus.adapterPackage>
        <mapstruct-plus.autoConfigPackage>com.xk.search.infrastructure.config</mapstruct-plus.autoConfigPackage>
        <mapstruct-plus.adapterClassName>XkSearchInfrastructureConverterMapperAdapter</mapstruct-plus.adapterClassName>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.search</groupId>
            <artifactId>xk-search-domain-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.search</groupId>
            <artifactId>xk-search-domain-enum</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.tair</groupId>
            <artifactId>alibabacloud-tairjedis-sdk</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*Mapper.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>

</project>
