package com.xk.message.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import com.xk.corp.interfaces.query.corp.CorpQueryService;
import com.xk.interfaces.query.object.CorpObjectQueryService;
import com.xk.interfaces.query.object.GoodsObjectQueryService;
import com.xk.interfaces.query.object.UserObjectQueryService;
import com.xk.tp.interfaces.service.sms.SmsService;

/**
 * @author: killer
 **/
public class XkMessageServiceConfig {
//    @Bean
//    public UserObjectQueryService userObjectQueryService(
//            HttpServiceProxyFactory xkAcctHttpServiceProxyFactory) {
//        return xkAcctHttpServiceProxyFactory.createClient(UserObjectQueryService.class);
//    }

//    @Bean
//    public CorpObjectQueryService corpObjectQueryService(
//            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
//        return xkCorpHttpServiceProxyFactory.createClient(CorpObjectQueryService.class);
//    }


    @Bean
    public SmsService smsService(HttpServiceProxyFactory xkThirdPartyHttpServiceProxyFactory) {
        return xkThirdPartyHttpServiceProxyFactory.createClient(SmsService.class);
    }

    @Bean
    public CorpQueryService corpQueryService(
            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpQueryService.class);
    }

//    @Bean
//    public GoodsObjectQueryService goodsObjectQueryService(
//            HttpServiceProxyFactory xkGoodsHttpServiceProxyFactory) {
//        return xkGoodsHttpServiceProxyFactory.createClient(GoodsObjectQueryService.class);
//    }
}
