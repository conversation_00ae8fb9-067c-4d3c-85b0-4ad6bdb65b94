package com.xk.acct.gateway.config;

import com.xk.interfaces.query.object.CorpObjectQueryService;
import com.xk.interfaces.query.object.GoodsObjectQueryService;
import org.springframework.context.annotation.Bean;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import com.xk.auth.interfaces.service.auth.userrole.UserRoleService;
import com.xk.corp.interfaces.query.corp.CorpQueryService;
import com.xk.message.interfaces.query.validate.ValidateCodeQueryService;
import com.xk.message.interfaces.service.validate.ValidateCodeService;
import com.xk.tp.interfaces.service.auth.ThirdAuthService;

/**
 * @author: killer
 **/
public class XkAcctServiceConfig {

    @Bean
    public CorpObjectQueryService corpObjectQueryService(
            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpObjectQueryService.class);
    }

    @Bean
    public GoodsObjectQueryService goodsObjectQueryService(
            HttpServiceProxyFactory xkGoodsHttpServiceProxyFactory) {
        return xkGoodsHttpServiceProxyFactory.createClient(GoodsObjectQueryService.class);
    }

    @Bean
    public ValidateCodeService validateCodeService(
            HttpServiceProxyFactory xkMessageHttpServiceProxyFactory) {
        return xkMessageHttpServiceProxyFactory.createClient(ValidateCodeService.class);
    }

    @Bean
    public ValidateCodeQueryService validateCodeQueryService(
            HttpServiceProxyFactory xkMessageHttpServiceProxyFactory) {
        return xkMessageHttpServiceProxyFactory.createClient(ValidateCodeQueryService.class);
    }

    @Bean
    public ThirdAuthService thirdAuthService(HttpServiceProxyFactory xkThirdPartyHttpServiceProxyFactory) {
        return xkThirdPartyHttpServiceProxyFactory.createClient(ThirdAuthService.class);
    }

    @Bean
    public UserRoleService userRoleService(HttpServiceProxyFactory xkAuthHttpServiceProxyFactory) {
        return xkAuthHttpServiceProxyFactory.createClient(UserRoleService.class);
    }

    @Bean
    public CorpQueryService corpQueryService(HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpQueryService.class);
    }
}
