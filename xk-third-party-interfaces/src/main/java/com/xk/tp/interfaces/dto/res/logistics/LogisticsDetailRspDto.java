package com.xk.tp.interfaces.dto.res.logistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogisticsDetailRspDto {

    /**
     * 物流事件时间
     */
    private String trackTime;

    /**
     * 物流描述
     */
    private String trackDescribe;

    /**
     * 事件类型
     */
    private String trackType;

}
