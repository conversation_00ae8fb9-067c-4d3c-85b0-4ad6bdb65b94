package com.xk.tp.interfaces.api.logistics;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xk.tp.interfaces.dto.req.logistics.LogisticsReqDto;
import com.xk.tp.interfaces.dto.res.logistics.LogisticsDetailRspDto;
import com.xk.tp.interfaces.query.logistics.LogisticsQueryService;

import reactor.core.publisher.Mono;

/**
 * 物流快递服务
 *
 * <AUTHOR> date 2024/07/26
 */
@RestController
@RequestMapping("/tp/logistics/query")
public interface LogisticsQueryDocService extends LogisticsQueryService {

    /**
     * 物流轨迹查询
     * 
     * @param dtoMono dtoMono
     * @return Mono<List<LogisticsDetailRspDto>>
     */
    @PostMapping("/detail")
    @Override
    Mono<List<LogisticsDetailRspDto>> detail(@RequestBody Mono<LogisticsReqDto> dtoMono);

}
