package com.xk.tp.interfaces.dto.req.logistics;

import com.myco.mydata.domain.model.action.session.AbstractSession;

import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogisticsReqDto extends AbstractSession {

    /**
     * 物流公司名称
     */
    private String logisticsCorpName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 收货人手机号
     */
    private String receivingMobile;

}
