package com.xk.tp.interfaces.query.logistics;

import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.mydata.interfaces.query.IQueryService;
import com.xk.tp.interfaces.dto.req.logistics.LogisticsReqDto;
import com.xk.tp.interfaces.dto.res.logistics.LogisticsDetailRspDto;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR> date 2024/07/22
 */
@HttpExchange("/tp/logistics/query")
public interface LogisticsQueryService extends IQueryService {

    /**
     * 物流轨迹查询
     * 
     * @param dtoMono dtoMono
     * @return Mono<List<LogisticsDetailRspDto>>
     */
    @PostExchange("/detail")
    Mono<List<LogisticsDetailRspDto>> detail(@RequestBody Mono<LogisticsReqDto> dtoMono);


}
