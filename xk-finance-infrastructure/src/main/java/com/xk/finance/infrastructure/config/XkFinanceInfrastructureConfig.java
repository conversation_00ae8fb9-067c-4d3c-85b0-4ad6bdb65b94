package com.xk.finance.infrastructure.config;

import com.myco.mydata.infrastructure.data.annotation.Repository;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author: killer
 **/
@Configuration
@MapperScan(basePackages = "com.xk.finance.infrastructure.data.persistence", annotationClass = Repository.class, sqlSessionFactoryRef = "defaultSqlSessionFactory")
@ComponentScan({"com.xk.finance.infrastructure.convertor"
        , "com.xk.finance.infrastructure.cache.dao"
        , "com.xk.finance.infrastructure.repository"
        , "com.xk.finance.infrastructure.service"
        , "com.xk.finance.infrastructure.adapter"})
public class XkFinanceInfrastructureConfig {
}
