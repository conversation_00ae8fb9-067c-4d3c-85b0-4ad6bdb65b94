package com.xk.finance.infrastructure.support;

import com.xk.finance.infrastructure.commons.XkFinanceInfrastructureErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.wrapper.InfrastructureWrapperThrowable;

/**
 * @author: killer
 **/
public class XkFinanceInfrastructureException extends InfrastructureWrapperThrowable {

    public XkFinanceInfrastructureException(XkFinanceInfrastructureErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

    public XkFinanceInfrastructureException(XkFinanceInfrastructureErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

}
