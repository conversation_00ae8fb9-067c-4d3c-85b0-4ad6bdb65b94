package com.xk.corp.application.query.recommend;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.corp.application.action.query.recommend.CorpRecommendGoodsQuery;
import com.xk.corp.application.action.query.recommend.CorpRecommendPagerQuery;
import com.xk.corp.enums.corp.CorpRecommendBlockTypeEnum;
import com.xk.corp.enums.corp.CorpRecommendRandomEnum;
import com.xk.corp.interfaces.dto.req.recommend.CorpRecommendGoodsReqDto;
import com.xk.corp.interfaces.dto.req.recommend.CorpRecommendPagerReqDto;
import com.xk.corp.interfaces.dto.req.recommend.CorpRecommendRandomQueryReqDto;
import com.xk.corp.interfaces.dto.res.recommend.CorpRecommendGoodsResDto;
import com.xk.corp.interfaces.dto.res.recommend.CorpRecommendRandomResDto;
import com.xk.corp.interfaces.query.recommend.CorpRecommendQueryService;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.repository.config.BusinessConfigRootQueryRepository;
import com.xk.enums.config.BusinessConfigGroupTypeEnum;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.util.Locale;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CorpRecommendQueryServiceImpl implements CorpRecommendQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    private final BusinessConfigRootQueryRepository businessConfigRootQueryRepository;

    private final Converter converter;

    @BusiCode
    @Override
    public Mono<Pagination> searchCorpRecommendPager(Mono<CorpRecommendPagerReqDto> mono) {
        return mono.flatMap(dto -> actionQueryDispatcher.executeQuery(mono, CorpRecommendPagerQuery.class, Pagination.class));
    }

    @BusiCode
    @Override
    public Mono<CorpRecommendRandomResDto> searchRandom(Mono<CorpRecommendRandomQueryReqDto> mono) {
        return mono.flatMap(dto -> {
            // 1. 准备查询条件
            BusinessConfigEntity entity = new BusinessConfigEntity();
            entity.setBusinessType(BusinessTypeEnum.XING_KA.getValue());
            entity.setGroupType(BusinessConfigGroupTypeEnum.CORP.name().toLowerCase(Locale.ROOT));
            entity.setKey(CorpRecommendBlockTypeEnum.getEnum(dto.getBlockType()).name());
            entity.setGroupId("0");

            // 2. 查询现有配置
            return businessConfigRootQueryRepository.findByParams(entity)
                    .next() //获取第一个元素
                    .flatMap(existingConfig -> {
                        CorpRecommendRandomResDto resDto = CorpRecommendRandomResDto.builder()
                                .corpRecommendRandom(Integer.valueOf(existingConfig.getVal()))
                                .build();
                        return Mono.just(resDto);
                    })
                    // 不存在则返回默认值
                    .switchIfEmpty(Mono.just(
                            CorpRecommendRandomResDto.builder()
                                    .corpRecommendRandom(0)
                                    .build()
                    )); // 直接返回 Mono<CorpRecommendRandomResDto>
        });
    }

    @BusiCode
    @Override
    public Mono<CorpRecommendGoodsResDto> searchGoods(Mono<CorpRecommendGoodsReqDto> mono) {
        return mono.flatMap(dto -> actionQueryDispatcher.executeQuery(mono, CorpRecommendGoodsQuery.class, CorpRecommendGoodsResDto.class));
    }
}
