package com.xk.corp.application.action.query.corp;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.corp.interfaces.dto.req.user.CorpUserCreateReqDto;
import com.xk.corp.interfaces.dto.res.corp.CorpInfoIdentifierResDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CorpUserCreateReqDto.class, convertGenerate = false),
        @AutoMapper(target = CorpInfoIdentifierResDto.class, convertGenerate = false)})
public class CorpInfoIdentifierQuery implements IActionQuery {

    /**
     * 公司id
     */
    private Long corpInfoId;
}
