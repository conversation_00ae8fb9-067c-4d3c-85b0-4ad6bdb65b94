package com.xk.corp.application.dto.user;

import java.util.Date;

import com.xk.corp.domain.model.user.entity.CorpUserEntity;
import com.xk.corp.infrastructure.convertor.CorpUserRoleEnumConvertor;
import com.xk.corp.interfaces.dto.res.user.CorpUserIdentifierResDto;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CorpUserIdentifierResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = CorpUserEntity.class,
                uses = {CommonStatusEnumConvertor.class, CorpUserRoleEnumConvertor.class},
                convertGenerate = false)})
public class CorpUserAppDto {
    /**
     * 商户成员ID（主键）
     */
    private Long corpUserId;

    /**
     * 商户ID
     */
    private Long corpInfoId;

    /**
     * 用户ID（全局唯一）
     */
    private Long userId;

    /**
     * 成员权限角色
     */
    private Integer corpUserRole;

    /**
     * 员工身份名称
     */
    private String corpUserRoleName;

    /**
     * 成员业务权限角色
     */
    private String corpUserBusinessRole;

    /**
     * 成员适用平台
     */
    private String corpUserPlatformType;

    /**
     * 成员姓名
     */
    private String corpUserName;

    /**
     * 成员手机号
     */
    private String corpUserPhone;

    /**
     * 成员状态（0-禁用 1-启用）
     */
    private Integer corpUserStatus;

    /**
     * 创建人ID
     */
    @AutoMappings({@AutoMapping(targetClass = CorpUserEntity.class, source = "createId",
            target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({@AutoMapping(targetClass = CorpUserEntity.class, source = "createTime",
            target = "createValObj.createTime")})
    private Date createTime;

    /**
     * 更新人ID
     */
    @AutoMappings({@AutoMapping(targetClass = CorpUserEntity.class, source = "updateId",
            target = "updateValObj.updateId")})
    private Long updateId;

    /**
     * 更新时间
     */
    @AutoMappings({@AutoMapping(targetClass = CorpUserEntity.class, source = "updateTime",
            target = "updateValObj.updateTime")})
    private Date updateTime;
}
