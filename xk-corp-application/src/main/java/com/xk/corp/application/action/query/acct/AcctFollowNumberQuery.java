
package com.xk.corp.application.action.query.acct;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.corp.interfaces.dto.req.corp.CorpInfoDetailReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AcctFollowNumberQuery implements IActionQuery {

    /**
     * 商户id
     */
    private Long corpInfoId;

}
