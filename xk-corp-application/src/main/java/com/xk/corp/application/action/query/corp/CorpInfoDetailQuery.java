
package com.xk.corp.application.action.query.corp;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.corp.interfaces.dto.req.corp.CorpInfoDetailReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CorpInfoDetailReqDto.class, convertGenerate = false),})
public class CorpInfoDetailQuery extends PagerQuery implements IActionQuery {

    /**
     * 商户id
     */
    private Long corpInfoId;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 创建开始时间
     */
    private String createStartTime;

    /**
     * 创建结束时间
     */
    private String createEndTime;
}
