
package com.xk.corp.application.action.query.corp;

import java.util.Date;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.corp.interfaces.dto.req.corp.CorpInfoPagerReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CorpInfoPagerReqDto.class, convertGenerate = false),})
public class CorpInfoPagerQuery extends PagerQuery implements IActionQuery {

    /**
     * 商户id
     */
    private Long corpInfoId;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 微信号
     */
    private String wechatId;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    private String alipayAccountName;

    /**
     * 银行卡号
     */
    private String bankAccount;

    /**
     * 银行卡账户名
     */
    private String bankAccountName;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;

    /**
     * 回款账期
     */
    private Integer paymentCycle;

    /**
     * 商户状态
     */
    private Integer corpStatus;

    /**
     * 上架状态
     */
    private Integer launchStatus;
}
