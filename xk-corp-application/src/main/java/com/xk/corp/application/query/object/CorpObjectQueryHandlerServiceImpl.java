package com.xk.corp.application.query.object;

import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.corp.interfaces.query.object.CorpObjectQueryHandlerService;
import com.xk.interfaces.dto.req.object.CorpObjectReqDto;
import com.xk.interfaces.dto.rsp.object.corp.CorpObjectRspDto;
import com.xk.interfaces.query.object.CorpObjectQueryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Service
@RequiredArgsConstructor
public class CorpObjectQueryHandlerServiceImpl implements CorpObjectQueryHandlerService {

    private final CorpObjectQueryService corpObjectQueryService;

    @BusiCode
    @Override
    public Mono<CorpObjectRspDto> getCorpObject(Mono<CorpObjectReqDto> corpObjectReqDtoMono) {
        return corpObjectQueryService.getCorpObject(corpObjectReqDtoMono);
    }
}
