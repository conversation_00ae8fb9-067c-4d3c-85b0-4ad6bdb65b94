package com.xk.corp.application.action.query.corp;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.corp.interfaces.dto.req.corp.CorpWonderfulMomentReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers(@AutoMapper(target = CorpWonderfulMomentReqDto.class, convertGenerate = false))
public class CorpLiveReplayQuery extends PagerQuery implements IActionQuery {

    /**
     * 商户id
     */
    private Long corpInfoId;
}
