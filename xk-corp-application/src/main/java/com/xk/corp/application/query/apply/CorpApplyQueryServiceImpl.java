package com.xk.corp.application.query.apply;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.corp.application.action.query.apply.CorpApplyPagerQuery;
import com.xk.corp.application.commons.XkCorpApplicationErrorEnum;
import com.xk.corp.application.dto.apply.CorpApplyAppDto;
import com.xk.corp.application.dto.apply.CorpApplyCountAppDto;
import com.xk.corp.application.support.XkCorpApplicationException;
import com.xk.corp.domain.service.apply.CorpApplyRootService;
import com.xk.corp.interfaces.dto.req.apply.CorpApplyPagerReqDto;
import com.xk.corp.interfaces.dto.res.apply.CorpApplyCountResDto;
import com.xk.corp.interfaces.dto.res.apply.CorpApplySessionResDto;
import com.xk.corp.interfaces.query.apply.CorpApplyQueryService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class CorpApplyQueryServiceImpl implements CorpApplyQueryService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final CorpApplyRootService corpApplyRootService;
    private final Converter converter;

    @BusiCode
    @Override
    public Mono<Pagination> searchApplyPager(Mono<CorpApplyPagerReqDto> mono) {
        return queryDispatcher.executeQuery(mono, CorpApplyPagerQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<CorpApplySessionResDto> searchBySession(Mono<RequireSessionDto> mono) {
        return corpApplyRootService.findBySession()
                .map(entity -> converter.convert(converter.convert(entity, CorpApplyAppDto.class),
                        CorpApplySessionResDto.class))
                .switchIfEmpty(Mono.error(new XkCorpApplicationException(
                        XkCorpApplicationErrorEnum.APPLY_QUERY_NOT_EXIST)));
    }

    @BusiCode
    @Override
    public Mono<CorpApplyCountResDto> searchCount(Mono<RequireSessionDto> mono) {
        return corpApplyRootService.searchCount().map(valObj -> {
            return converter.convert(converter.convert(valObj, CorpApplyCountAppDto.class),
                    CorpApplyCountResDto.class);
        });
    }
}
