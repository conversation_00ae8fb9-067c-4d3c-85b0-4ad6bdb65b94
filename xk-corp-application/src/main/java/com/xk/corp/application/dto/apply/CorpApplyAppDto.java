package com.xk.corp.application.dto.apply;

import java.util.Date;

import com.xk.corp.domain.model.apply.entity.CorpApplyEntity;
import com.xk.corp.infrastructure.convertor.AuditStatusEnumConvertor;
import com.xk.corp.infrastructure.convertor.PayTypeEnumConvertor;
import com.xk.corp.interfaces.dto.res.apply.CorpApplyIdentifierResDto;
import com.xk.corp.interfaces.dto.res.apply.CorpApplyPagerResDto;
import com.xk.corp.interfaces.dto.res.apply.CorpApplySessionResDto;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CorpApplyIdentifierResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = CorpApplySessionResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = CorpApplyPagerResDto.class, reverseConvertGenerate = false),
        @AutoMapper(
                target = CorpApplyEntity.class, uses = {CommonStatusEnumConvertor.class,
                        PayTypeEnumConvertor.class, AuditStatusEnumConvertor.class},
                convertGenerate = false),})
public class CorpApplyAppDto {

    /**
     * 公司申请ID
     */
    private Long corpApplyId;

    /**
     * 公司全称
     */
    private String corpName;

    /**
     * 商户详细介绍
     */
    private String corpDescribe;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 微信ID
     */
    private String wechatId;

    /**
     * 微信二维码
     */
    private String wechatQrCode;

    /**
     * 申请人用户ID
     */
    private Long applyUserId;

    /**
     * 申请提交时间
     */
    private Date applyTime;

    /**
     * 累计申请次数
     */
    private Integer applyCount;

    /**
     * LOGO存储路径
     */
    private String logo;

    /**
     * 补充说明图片路径
     */
    private String additionalPic;

    /**
     * 营业执照存储路径
     */
    private String businessLicense;

    /**
     * 是否添加客服(0-否 1-是)
     */
    private Integer addSupport;

    /**
     * 是否入驻其他平台(0-否 1-是)
     */
    private Integer regOtherPlatform;

    /**
     * 支付偏好(0-任意 1-银行卡 2-支付宝 3-微信支付)
     */
    private Integer payTypeLike;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 支付宝实名名称
     */
    private String alipayAccountName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 银行账户名称
     */
    private String bankAccountName;

    /**
     * 开户行全称
     */
    private String bankName;


    /**
     * 审核状态（0-待审核 1-通过 2-拒绝）
     */
    private Integer auditStatus;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 驳回原因
     */
    private String refuseRemark;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核人ID
     */
    private Long auditUserId;
}
