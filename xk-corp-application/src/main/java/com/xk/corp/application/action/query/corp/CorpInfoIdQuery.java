
package com.xk.corp.application.action.query.corp;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.corp.interfaces.dto.req.corp.CorpIdSearchReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CorpIdSearchReqDto.class, convertGenerate = false),})
public class CorpInfoIdQuery implements IActionQuery {

    /**
     * 商户id
     */
    private Long corpInfoId;

}
