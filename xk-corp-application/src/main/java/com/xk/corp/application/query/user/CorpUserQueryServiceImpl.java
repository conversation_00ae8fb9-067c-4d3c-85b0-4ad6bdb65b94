package com.xk.corp.application.query.user;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.auth.enums.corp.CorpUserRoleEnum;
import com.xk.corp.application.action.query.user.CorpUserPagerQuery;
import com.xk.corp.domain.service.corp.CorpRootService;
import com.xk.corp.interfaces.dto.req.user.CorpUserPagerAdminReqDto;
import com.xk.corp.interfaces.dto.req.user.CorpUserPagerReqDto;
import com.xk.corp.interfaces.query.user.CorpUserQueryService;
import com.xk.enums.common.CommonStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class CorpUserQueryServiceImpl implements CorpUserQueryService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final CorpRootService corpRootService;

    @BusiCode
    @Override
    public Mono<Pagination> searchCorpUserPagerAdmin(Mono<CorpUserPagerAdminReqDto> mono) {
        return queryDispatcher.executeQuery(mono, CorpUserPagerQuery.class, query -> {
            query.setCorpUserStatus(CommonStatusEnum.ENABLE.getCode());
            query.setCorpUserRole(CorpUserRoleEnum.USER.getCode());
            return query;
        }, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchShowPager(Mono<CorpUserPagerReqDto> mono) {
        return corpRootService.findIdBySession().flatMap(corpInfoId -> queryDispatcher
                .executeQuery(mono, CorpUserPagerQuery.class, query -> {
                    query.setCorpInfoId(corpInfoId);
                    query.setCorpUserStatus(CommonStatusEnum.ENABLE.getCode());
                    query.setCorpUserRole(CorpUserRoleEnum.USER.getCode());
                    return query;
                }, Pagination.class));
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchPager(Mono<CorpUserPagerReqDto> mono) {
        return corpRootService.findIdBySession().flatMap(corpInfoId -> queryDispatcher
                .executeQuery(mono, CorpUserPagerQuery.class, query -> {
                    query.setCorpInfoId(corpInfoId);
                    return query;
                }, Pagination.class));
    }
}
