package com.xk.corp.application.task;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.corp.application.action.query.recommend.CorpRecommendPagerQuery;
import com.xk.corp.domain.event.corp.CorpGoodToCacheJobEvent;
import com.xk.corp.domain.event.corp.CorpHotToCacheJobEvent;
import com.xk.corp.domain.event.corp.CorpRecommendBannerToCacheJobEvent;
import com.xk.corp.domain.event.corp.CorpRecommendHomePageToCacheJobEvent;
import com.xk.corp.domain.event.dto.CorpHotInfo;
import com.xk.corp.domain.model.corp.CorpRecommendRoot;
import com.xk.corp.domain.model.corp.entity.CorpRecommendEntity;
import com.xk.corp.domain.model.corp.entity.CorpRecommendGoodsEntity;
import com.xk.corp.domain.repository.recommend.CorpRecommendRootQueryRepository;
import com.xk.corp.domain.repository.recommend.CorpRecommendRootRepository;
import com.xk.corp.enums.corp.CorpGroupTypeEnum;
import com.xk.corp.enums.corp.CorpRecommendTypeEnum;
import com.xk.corp.enums.corp.SortTypeEnum;
import com.xk.corp.infrastructure.cache.dao.CorpRecommendBannerDao;
import com.xk.corp.infrastructure.cache.key.CorpRecommendBannerKey;
import com.xk.corp.interfaces.task.CorpRecommendToCacheService;
import com.xk.goods.domain.event.merchant.MerchantTopRecommendUpdateGoodsEvent;
import com.xk.goods.enums.common.BlockTypeEnum;
import com.xk.goods.enums.goods.ListingStatusEnum;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdInnerReqDto;
import com.xk.goods.interfaces.query.goods.MerchantProductQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CorpRecommendToCacheServiceImpl implements CorpRecommendToCacheService {

    private final CorpRecommendRootQueryRepository corpRecommendRootQueryRepository;

    private final CorpRecommendRootRepository corpRecommendRootRepository;

    private final EventRootService eventRootService;

    private final MerchantProductQueryService merchantProductQueryService;

    private final SelectorRootService selectorRootService;

    private final CorpRecommendBannerDao corpRecommendBannerDao;

    @Override
    public Mono<Void> updateHomePageHotCorp() {
        BiFunction<Pagination, Integer, Mono<List<CorpRecommendEntity>>> searchEntities =
                (pagination, sortType) -> {
                    Flux<CorpRecommendEntity> flux =
                            corpRecommendRootQueryRepository.searchCorpRecommends(pagination)
                                    .filter(entity -> entity.getCorpId() != null);

                    if (SortTypeEnum.SHUFFLE.getCode() == sortType) {
                        return flux.collectList().map(entities -> {
                            if (!entities.isEmpty()) {
                                Collections.shuffle(entities);
                            }
                            return entities;
                        });
                    } else {
                        return flux.collectList(); // 直接收集为列表并返回
                    }
                };


        return Flux.fromArray(BlockTypeEnum.values())
                .flatMap(blockType -> Flux.fromArray(SortTypeEnum.values()).flatMap(sortType -> {
                    int batchSize = 4;

                    // 查询条件
                    CorpRecommendPagerQuery query = new CorpRecommendPagerQuery();
                    query.setCorpRecommendType(CorpRecommendTypeEnum.HOME_PAGE_HOT_CORP.getCode());
                    query.setBlockType(blockType.getCode());

                    Pagination pagination = new Pagination();
                    pagination.setOffset(query.getOffset());
                    pagination.setLimit(1000);
                    pagination.setCriteria(CollectionHelper.converBeanToMap(query));

                    return searchEntities.apply(pagination, sortType.getCode())
                            .flatMapMany(Flux::fromIterable)
                            .filter(entity -> entity.getCorpId() != null).collectList()
                            .flatMapMany(filteredEntities -> {

                                // 处理第一批次（双倍大小）
                                int firstBatchSize = batchSize * 2;
                                List<CorpRecommendEntity> firstBatch =
                                        filteredEntities.stream().limit(firstBatchSize).toList();

                                // 处理剩余批次
                                List<List<CorpRecommendEntity>> remainingBatches = Lists.partition(
                                        filteredEntities.stream().skip(firstBatchSize).toList(),
                                        batchSize);

                                // 合并所有批次
                                List<List<CorpRecommendEntity>> allBatches = new ArrayList<>();
                                if (!firstBatch.isEmpty()) {
                                    allBatches.add(firstBatch);
                                }
                                allBatches.addAll(remainingBatches);

                                return Flux.fromIterable(allBatches).index().flatMap(tuple -> {
                                    List<CorpHotInfo> corpInfos = tuple.getT2().stream()
                                            .map(item -> CorpHotInfo.builder()
                                                    .corpId(item.getCorpId()) // 假设corpId来自item
                                                    .corpName(item.getCorpName()) // 假设corpName来自item
                                                    .build())
                                            .toList();

                                    EventRoot eventRoot = EventRoot.builder()
                                            .domainEvent(CorpRecommendHomePageToCacheJobEvent
                                                    .builder()
                                                    .identifier(EventRoot
                                                            .getCommonsDomainEventIdentifier(
                                                                    CorpRecommendHomePageToCacheJobEvent.class))
                                                    .corpInfos(corpInfos)
                                                    .blockType(blockType.getCode())
                                                    .pageNum((int) (tuple.getT1() + 1))
                                                    .sortType(sortType.getCode()).build())
                                            .isQueue(false).build();

                                    return eventRootService.publishByMonoThrow(eventRoot);
                                });
                            }).then();
                })).then();
    }


    @Override
    public Mono<Void> updateBannerCorpGoods() {
        log.info("=== 开始执行Banner商家商品更新定时任务 ===");

        return Flux.fromArray(BlockTypeEnum.values())
                .doOnNext(blockType -> log.info("开始处理板块类型: {}", blockType))
                .flatMap(blockType -> Flux
                        .fromArray(CorpGroupTypeEnum.values()).doOnNext(groupType -> log
                                .info("处理板块[{}] - 商家类型: {}", blockType, groupType.getName()))
                        .flatMap(groupType -> {
                            int batchSize = 4;

                            // 查询条件
                            CorpRecommendPagerQuery query = new CorpRecommendPagerQuery();
                            query.setCorpRecommendType(
                                    CorpRecommendTypeEnum.HOME_PAGER_BANNER.getCode());
                            query.setBlockType(blockType.getCode());
                            query.setGroupType(groupType.getCode());

                            Pagination pagination = new Pagination();
                            pagination.setOffset(query.getOffset());
                            pagination.setLimit(1000);
                            pagination.setCriteria(CollectionHelper.converBeanToMap(query));

                            return corpRecommendRootQueryRepository.searchCorpRecommends(pagination)
                                    .doOnNext(entity -> log.debug(
                                            "查询到推荐商家: corpRecommendId={}, corpName={}",
                                            entity.getCorpRecommendId(), entity.getCorpName()))
                                    .filter(entity -> entity.getCorpId() != null)
                                    .flatMap(entity -> {
                                        // 查询商品信息
                                        Mono<CorpRecommendGoodsEntity> goodsMono =
                                                corpRecommendRootQueryRepository
                                                        .getCorpRecommendGoods(LongIdentifier
                                                                .builder()
                                                                .id(entity.getCorpRecommendId())
                                                                .build());

                                        // 使用 flatMap 而不是 map，确保条件判断在响应式流中正确执行
                                        return goodsMono
                                                .defaultIfEmpty(new CorpRecommendGoodsEntity())
                                                .flatMap(goodsEntity -> {
                                                    // 验证 goodsIds 是否有效
                                                    if (goodsEntity.getGoodsIds() == null
                                                            || goodsEntity.getGoodsIds()
                                                                    .isEmpty()) {
                                                        log.debug(
                                                                "过滤掉无商品的推荐商家: corpRecommendId={}, corpName={}",
                                                                entity.getCorpRecommendId(),
                                                                entity.getCorpName());
                                                        return Mono.empty(); // 过滤掉无效实体
                                                    }
                                                    String[] goodsIdArray =
                                                            goodsEntity.getGoodsIds().split(";");
                                                    if (goodsIdArray.length == 0) {
                                                        log.debug(
                                                                "过滤掉商品ID为空的推荐商家: corpRecommendId={}, corpName={}",
                                                                entity.getCorpRecommendId(),
                                                                entity.getCorpName());
                                                        return Mono.empty(); // 过滤掉无效实体
                                                    }
                                                    log.debug(
                                                            "保留有效推荐商家: corpRecommendId={}, corpName={}, 商品数量={}",
                                                            entity.getCorpRecommendId(),
                                                            entity.getCorpName(),
                                                            goodsIdArray.length);
                                                    return Mono.just(entity); // 保留有效实体
                                                });
                                    }).collectList().flatMapMany(filteredEntities -> {
                                        log.info("板块[{}]-商家类型[{}] 过滤后有效商家数量: {}", blockType,
                                                groupType.getName(), filteredEntities.size());

                                        // 如果为空，也需要更新
                                        if (filteredEntities.isEmpty()) {
                                            log.debug("板块[{}]-商家类型[{}] 无有效商家，更新推荐位为空", blockType,
                                                    groupType.getName());
                                            return clearRecommend(blockType.getCode(), groupType.getCode());
                                        }

                                        // 处理第一批次（双倍大小）
                                        int firstBatchSize = batchSize * 2;
                                        List<CorpRecommendEntity> firstBatch = filteredEntities
                                                .stream().limit(firstBatchSize).toList();

                                        // 处理剩余批次
                                        List<List<CorpRecommendEntity>> remainingBatches =
                                                Lists.partition(
                                                        filteredEntities.stream()
                                                                .skip(firstBatchSize).toList(),
                                                        batchSize);

                                        // 合并所有批次
                                        List<List<CorpRecommendEntity>> allBatches =
                                                new ArrayList<>();
                                        if (!firstBatch.isEmpty()) {
                                            allBatches.add(firstBatch);
                                        }
                                        allBatches.addAll(remainingBatches);

                                        log.info(
                                                "板块[{}]-商家类型[{}] 分批处理: 总批次数={}, 第一批大小={}, 标准批次大小={}",
                                                blockType, groupType.getName(), allBatches.size(),
                                                firstBatch.size(), batchSize);

                                        return Flux.fromIterable(allBatches).index()
                                                .flatMap(tuple -> {
                                                    int pageNum = (int) (tuple.getT1() + 1);
                                                    List<CorpRecommendEntity> batch = tuple.getT2();

                                                    log.info(
                                                            "发送Banner缓存事件 - 板块[{}]-商家类型[{}] 第{}批: 商家数量={}",
                                                            blockType, groupType.getName(), pageNum,
                                                            batch.size());

                                                    List<Long> corpRecommendIds = batch.stream()
                                                            .map(CorpRecommendEntity::getCorpRecommendId)
                                                            .toList();
                                                    Map<Long, Long> corpIdMap = batch.stream()
                                                            .collect(Collectors.toMap(
                                                                    CorpRecommendEntity::getCorpRecommendId,
                                                                    CorpRecommendEntity::getCorpId));

                                                    Map<Long, String> corpNameMap = batch.stream()
                                                            .collect(Collectors.toMap(
                                                                    CorpRecommendEntity::getCorpRecommendId,
                                                                    CorpRecommendEntity::getCorpName));

                                                    log.debug(
                                                            "事件详情 - 板块[{}]-商家类型[{}] 第{}批: corpRecommendIds={}",
                                                            blockType, groupType.getName(), pageNum,
                                                            corpRecommendIds);

                                                    EventRoot eventRoot = EventRoot.builder()
                                                            .domainEvent(
                                                                    CorpRecommendBannerToCacheJobEvent
                                                                            .builder()
                                                                            .identifier(EventRoot
                                                                                    .getCommonsDomainEventIdentifier(
                                                                                            CorpRecommendBannerToCacheJobEvent.class))
                                                                            .corpRecommendIds(
                                                                                    corpRecommendIds)
                                                                            .corpIdMap(corpIdMap)
                                                                            .corpNameMap(
                                                                                    corpNameMap)
                                                                            .blockType(blockType
                                                                                    .getCode())
                                                                            .groupType(groupType
                                                                                    .getCode())
                                                                            .pageNum(pageNum)
                                                                            .build())
                                                            .isQueue(false).build();

                                                    return eventRootService
                                                            .publishByMonoThrow(eventRoot)
                                                            .doOnSuccess(v -> log.info(
                                                                    "Banner缓存事件发送成功 - 板块[{}]-商家类型[{}] 第{}批",
                                                                    blockType, groupType.getName(),
                                                                    pageNum))
                                                            .doOnError(e -> log.error(
                                                                    "Banner缓存事件发送失败 - 板块[{}]-商家类型[{}] 第{}批: {}",
                                                                    blockType, groupType.getName(),
                                                                    pageNum, e.getMessage(), e));
                                                });
                                    }).then();
                        }))
                .then().doOnSuccess(v -> log.info("=== Banner商家商品更新定时任务执行完成 ===")).doOnError(
                        e -> log.error("=== Banner商家商品更新定时任务执行失败，错误: {} ===", e.getMessage(), e));

    }

    @Override
    public Mono<Void> updateSearchHotCorp() {
        CorpRecommendPagerQuery query = new CorpRecommendPagerQuery();
        query.setCorpRecommendType(CorpRecommendTypeEnum.RECOMMEND_HOT_CORP.getCode());

        Pagination pagination = new Pagination();
        pagination.setOffset(query.getOffset());
        pagination.setLimit(1000);
        pagination.setCriteria(CollectionHelper.converBeanToMap(query));

        return corpRecommendRootQueryRepository.searchCorpRecommends(pagination)
                .filter(entity -> entity.getCorpId() != null)
                .map(entity -> CorpHotInfo.builder().corpId(entity.getCorpId())
                        .corpName(entity.getCorpName()).build())
                .buffer(100) // 每100个元素一批（可根据需求调整）
                .flatMap(corpHotInfos -> {
                    EventRoot eventRoot = EventRoot.builder()
                            .domainEvent(CorpHotToCacheJobEvent.builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            CorpHotToCacheJobEvent.class))
                                    .corpHotInfos(corpHotInfos).build())
                            .isQueue(false).build();

                    return eventRootService.publishByMonoThrow(eventRoot);
                }).then(); // 返回Mono<Void>表示全部完成
    }

    @Override
    public Mono<Void> updateSearchSuperNewCorp() {
        CorpRecommendPagerQuery query = new CorpRecommendPagerQuery();
        query.setCorpRecommendType(CorpRecommendTypeEnum.RECOMMEND_GOOD_CORP.getCode());

        Pagination pagination = new Pagination();
        pagination.setOffset(query.getOffset());
        pagination.setLimit(1000);
        pagination.setCriteria(CollectionHelper.converBeanToMap(query));

        return corpRecommendRootQueryRepository.searchCorpRecommends(pagination)
                .filter(entity -> entity.getCorpId() != null)
                .map(entity -> CorpHotInfo.builder().corpId(entity.getCorpId())
                        .corpName(entity.getCorpName()).build())
                .buffer(100) // 每100个元素一批（可根据需求调整）
                .flatMap(corpHotInfos -> {
                    EventRoot eventRoot = EventRoot.builder()
                            .domainEvent(CorpGoodToCacheJobEvent.builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            CorpGoodToCacheJobEvent.class))
                                    .corpHotInfos(corpHotInfos).build())
                            .isQueue(false).build();

                    return eventRootService.publishByMonoThrow(eventRoot);
                }).then(); // 返回Mono<Void>表示全部完成
    }

    @Override
    public Mono<Void> updateRecommendGoods() {
        return corpRecommendRootQueryRepository.searchAllCorpRecommendGoods()
                .filter(entity -> entity.getGoodsIds() != null && !entity.getGoodsIds().isEmpty())
                .flatMap(goodsEntity -> {
                    List<Long> goodsIds = Arrays.stream(goodsEntity.getGoodsIds().split(";"))
                            .map(Long::valueOf).toList();

                    log.info("开始处理推荐商品，corpRecommendId: {}, 原商品数量: {}, 商品IDs: {}",
                            goodsEntity.getCorpRecommendId(), goodsIds.size(), goodsIds);

                    // 发送删除某个商品的事件
                    Function<Long, Mono<Void>> removeEventFunc = (goodsId) -> {
                        return corpRecommendRootQueryRepository
                                .getCorpRecommend(LongIdentifier.builder()
                                        .id(goodsEntity.getCorpRecommendId()).build())
                                .flatMap(recommendEntity -> {
                                    if (recommendEntity
                                            .getCorpRecommendType() != CorpRecommendTypeEnum.HOME_PAGER_GOODS
                                                    .getCode()) {
                                        log.info("推荐类型不是HOME_PAGER_GOODS，跳过删除事件发送，类型: {}",
                                                recommendEntity.getCorpRecommendType());
                                        return Mono.empty();
                                    }

                                    log.info("准备发送删除事件，商品ID: {}, corpRecommendId: {}", goodsId,
                                            recommendEntity.getCorpRecommendId());

                                    EventRoot eventRoot = EventRoot.builder()
                                            .domainEvent(MerchantTopRecommendUpdateGoodsEvent
                                                    .builder()
                                                    .identifier(EventRoot
                                                            .getCommonsDomainEventIdentifier(
                                                                    MerchantTopRecommendUpdateGoodsEvent.class))
                                                    .goodsId(goodsId)
                                                    .blockType(recommendEntity.getBlockType())
                                                    .topRecommendScore(0).build())
                                            .build();

                                    return eventRootService.publisheByMono(eventRoot).then()
                                            .doOnSuccess(v -> log.info(
                                                    "RecommendGoodsUpdateSchedule 删除旧商品事件发布完成: {}",
                                                    recommendEntity.getCorpRecommendId()));
                                });
                    };

                    // 发送添加事件
                    Function<Long, Mono<Void>> addEventFunc = (goodsId) -> {
                        return corpRecommendRootQueryRepository
                                .getCorpRecommend(LongIdentifier.builder()
                                        .id(goodsEntity.getCorpRecommendId()).build())
                                .flatMap(recommendEntity -> {
                                    if (recommendEntity
                                            .getCorpRecommendType() != CorpRecommendTypeEnum.HOME_PAGER_GOODS
                                                    .getCode()) {
                                        log.info("推荐类型不是HOME_PAGER_GOODS，跳过新增事件发送，类型: {}",
                                                recommendEntity.getCorpRecommendType());
                                        return Mono.empty();
                                    }

                                    log.info("准备发送新增事件，商品ID: {}, corpRecommendId: {}", goodsId,
                                            recommendEntity.getCorpRecommendId());

                                    EventRoot eventRoot = EventRoot.builder()
                                            .domainEvent(MerchantTopRecommendUpdateGoodsEvent
                                                    .builder()
                                                    .identifier(EventRoot
                                                            .getCommonsDomainEventIdentifier(
                                                                    MerchantTopRecommendUpdateGoodsEvent.class))
                                                    .goodsId(goodsId)
                                                    .blockType(recommendEntity.getBlockType())
                                                    .topRecommendScore(
                                                            recommendEntity.getCorpRecommendNo())
                                                    .build())
                                            .build();

                                    return eventRootService.publisheByMono(eventRoot).then()
                                            .doOnSuccess(v -> log.info(
                                                    "RecommendGoodsUpdateSchedule 添加新商品事件发布完成: {}",
                                                    recommendEntity.getCorpRecommendId()));
                                });
                    };

                    // 更新商品信息
                    Function<List<Long>, Mono<Void>> updateGoodsFunc =
                            newGoodsIds -> corpRecommendRootRepository.update(CorpRecommendRoot
                                    .builder()
                                    .identifier(LongIdentifier.builder()
                                            .id(goodsEntity.getCorpRecommendId()).build())
                                    .corpRecommendGoodsEntity(CorpRecommendGoodsEntity.builder()
                                            .corpRecommendId(goodsEntity.getCorpRecommendId())
                                            .goodsIds(newGoodsIds.stream().map(String::valueOf)
                                                    .collect(Collectors.joining(";")))
                                            .updateId(-1L).updateTime(new Date()).build())
                                    .build()).then();

                    // 使用 Flux 并行处理每个商品ID
                    return Flux.fromIterable(goodsIds).flatMap(goodsId -> {
                        GoodsIdInnerReqDto reqDto = new GoodsIdInnerReqDto();
                        reqDto.setGoodsId(goodsId);

                        return merchantProductQueryService.innerDetail(Mono.just(reqDto))
                                .flatMap(detailResDto -> {
                                    log.debug("商品ID: {}, 上架状态: {}, 计划下架时间: {}, 售罄状态：{}", goodsId,
                                            detailResDto.getListingStatus(),
                                            detailResDto.getPlanDownTime(),
                                            detailResDto.getSoldOutStatus());

                                    if (Objects.equals(detailResDto.getListingStatus(),
                                            ListingStatusEnum.UP.getCode())
                                            && (detailResDto.getPlanDownTime() == null
                                                    || detailResDto.getPlanDownTime()
                                                            .after(new Date()))
                                            && detailResDto.getSoldOutStatus() == 0) {
                                        log.debug("商品ID: {} 保留", goodsId);
                                        return Mono.just(goodsId);
                                    } else {
                                        log.info("商品ID: {} 被过滤，状态: {}, 计划下架时间: {}, 售罄状态：{}",
                                                goodsId, detailResDto.getListingStatus(),
                                                detailResDto.getPlanDownTime(),
                                                detailResDto.getSoldOutStatus());
                                        return Mono.empty();
                                    }
                                }).onErrorResume(e -> {
                                    log.error("查询商品详情失败，商品ID: {}, 错误: {}", goodsId, e.getMessage(),
                                            e);
                                    return Mono.just(goodsId);
                                });
                    }).collectList().flatMap(newGoodsIds -> {
                        log.info("处理完成，corpRecommendId: {}, 原商品数量: {}, 新商品数量: {}, 新商品IDs: {}",
                                goodsEntity.getCorpRecommendId(), goodsIds.size(),
                                newGoodsIds.size(), newGoodsIds);

                        // 如果有商品下架，更新推荐商品
                        if (newGoodsIds.size() != goodsIds.size()) {
                            log.info("检测到商品变化，开始更新推荐商品，corpRecommendId: {}",
                                    goodsEntity.getCorpRecommendId());
                            if (newGoodsIds.isEmpty()) {
                                log.info("发送删除事件 - 原因：新商品为空");
                                return removeEventFunc.apply(goodsIds.getFirst())
                                        .then(updateGoodsFunc.apply(newGoodsIds));
                            } else if (!newGoodsIds.getFirst().equals(goodsIds.getFirst())) {
                                log.info("发送删除事件和新增事件 - 原因：推荐位第一个商品被修改");
                                return removeEventFunc.apply(goodsIds.getFirst())
                                        .then(addEventFunc.apply(newGoodsIds.getFirst()))
                                        .then(updateGoodsFunc.apply(newGoodsIds));
                            }
                        } else {
                            log.debug("商品无变化，无需更新，corpRecommendId: {}",
                                    goodsEntity.getCorpRecommendId());
                        }
                        return Mono.empty();
                    });
                }).then();
    }

    private Mono<Void> clearRecommend(Integer blockType, Integer groupType) {
        log.info("开始清空缓存 - 板块类型: {}, 商家类型: {}, 页码: {}", blockType, groupType, 1);

        // 生成缓存的KEY
        CorpRecommendBannerKey key = CorpRecommendBannerKey.builder()
                .blockType(blockType)
                .groupType(groupType)
                .pageNum(1)
                .build();

        return Mono.fromRunnable(() -> {
            try {
                corpRecommendBannerDao.addValue(key, "{}");
                log.info("清空缓存成功 - 板块类型: {}, 商家类型: {}, 页码: {}", blockType, groupType, 1);
            } catch (Exception e) {
                log.error("清空缓存失败 - 板块类型: {}, 商家类型: {}, 页码: {}, 错误: {}",
                        blockType, groupType, 1, e.getMessage(), e);
                throw e;
            }
        });
    }

}
