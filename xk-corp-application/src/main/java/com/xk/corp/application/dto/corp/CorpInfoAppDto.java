package com.xk.corp.application.dto.corp;

import com.xk.corp.domain.model.corp.entity.CorpInfoEntity;
import com.xk.corp.interfaces.dto.res.corp.CorpInfoIdentifierResDto;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = CorpInfoIdentifierResDto.class,
                uses = {CommonStatusEnumConvertor.class}),
        @AutoMapper(target = CorpInfoEntity.class),})
public class CorpInfoAppDto {

    /**
     * 公司ID
     */
    private Long corpInfoId;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 商户状态（1-正常 0-禁用）
     */
    private CommonStatusEnum corpStatus;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 管理员用户ID
     */
    private Long adminUserId;
}
