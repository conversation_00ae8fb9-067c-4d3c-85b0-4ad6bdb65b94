package com.xk.corp.application.dto.apply;

import com.xk.corp.domain.model.apply.valobj.CorpApplyCountValObj;
import com.xk.corp.interfaces.dto.res.apply.CorpApplyCountResDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({@AutoMapper(target = CorpApplyCountValObj.class, convertGenerate = false),
        @AutoMapper(target = CorpApplyCountResDto.class, reverseConvertGenerate = false)})
public class CorpApplyCountAppDto {

    /**
     * 首次申请数量
     */
    private Integer firstCount;

    /**
     * 多次申请数量
     */
    private Integer multiCount;

    /**
     * 通过数量
     */
    private Integer passCount;

    /**
     * 未通过数量
     */
    private Integer unPassCount;
}
