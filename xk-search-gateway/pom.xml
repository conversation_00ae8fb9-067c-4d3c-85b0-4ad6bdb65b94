<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.search</groupId>
        <artifactId>xk-search</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-search-gateway</artifactId>
    <name>xk-search-gateway</name>
    <packaging>jar</packaging>
    <description>xk-search-gateway</description>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.search</groupId>
            <artifactId>xk-search-domain-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.search</groupId>
            <artifactId>xk-search-domain-enum</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.goods</groupId>
            <artifactId>xk-goods-interfaces</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.order</groupId>
            <artifactId>xk-order-interfaces</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.corp</groupId>
            <artifactId>xk-corp-interfaces</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.acct</groupId>
            <artifactId>xk-acct-interfaces</artifactId>
        </dependency>
    </dependencies>
</project>
