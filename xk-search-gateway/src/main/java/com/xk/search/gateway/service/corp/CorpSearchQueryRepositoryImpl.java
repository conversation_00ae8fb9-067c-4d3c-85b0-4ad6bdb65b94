package com.xk.search.gateway.service.corp;

import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.action.session.AbstractSession;
import com.myco.mydata.domain.model.session.SessionRoot;
import com.myco.mydata.domain.service.object.SelectorObjectRootService;
import com.xk.acct.interfaces.dto.req.follow.CorpFollowQueryReqDto;
import com.xk.acct.interfaces.query.UserFollowCorpQueryService;
import com.xk.corp.interfaces.dto.req.corp.CorpIdSearchReqDto;
import com.xk.corp.interfaces.query.corp.CorpQueryService;
import com.xk.search.domain.model.corp.CorpSearchEntity;
import com.xk.search.domain.model.corp.CorpSearchRoot;
import com.xk.search.domain.model.corp.valobj.CorpIndexValueObject;
import com.xk.search.domain.model.search.id.SearchIdentifier;
import com.xk.search.domain.repository.corp.CorpSearchQueryRepository;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelDefaultEnum;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class CorpSearchQueryRepositoryImpl implements CorpSearchQueryRepository {

    private final CorpQueryService corpQueryService;

    private final UserFollowCorpQueryService userFollowCorpQueryService;

    private final SelectorObjectRootService selectorObjectRootService;

    private final Converter converter;

    @Override
    public Mono<CorpSearchRoot> getCorpById(Mono<LongIdentifier> mono) {
        return mono.flatMap(identifier -> {
                    CorpIdSearchReqDto reqDto = CorpIdSearchReqDto.builder().corpInfoId(identifier.id()).build();
                    reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
                    return corpQueryService.getCorpById(Mono.just(reqDto));
                }
        ).flatMap(dto -> {
            CorpSearchEntity searchEntity =
                    converter.convert(dto, CorpSearchEntity.class);
            searchEntity.setCorpFollow(0);
            searchEntity.setGoodsQuantity(0);
            return Mono.just(CorpSearchRoot.builder()
                    .identifier(LongIdentifier.builder().id(dto.getCorpInfoId()).build())
                    .corpIndexValueObject(CorpIndexValueObject.builder()
                            .searchBizTypeEnum(SearchBizTypeEnum.CORP).build())
                    .searchIdentifier(SearchIdentifier.builder()
                            .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType()).build())
                    .corpSearchEntity(searchEntity).build());
        });
    }

    @Override
    public Mono<List<Long>> getCorpList() {
        return corpQueryService.searchCorp(Mono.just(new AbstractSession())).flatMap(resDto -> Mono.just(resDto.getCorpInfoIdList()));
    }

    @Override
    public Mono<CorpSearchRoot> getCorpFollowById(Mono<LongIdentifier> mono) {
        return mono.flatMap(identifier -> userFollowCorpQueryService
                .followNumber(Mono.just(CorpFollowQueryReqDto.builder().corpInfoId(identifier.id()).build()))
                .flatMap(corpFollowNumberRspDtp -> Mono.just(CorpSearchRoot.builder()
                        .identifier(LongIdentifier.builder().id(identifier.id()).build())
                                .corpIndexValueObject(CorpIndexValueObject.builder()
                                        .searchBizTypeEnum(SearchBizTypeEnum.CORP)
                                        .build())
                                .searchIdentifier(SearchIdentifier.builder()
                                        .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType())
                                        .build())
                        .corpSearchEntity(CorpSearchEntity.builder().corpFollow(corpFollowNumberRspDtp.getNumber()).build())
                        .build())));
    }
}
