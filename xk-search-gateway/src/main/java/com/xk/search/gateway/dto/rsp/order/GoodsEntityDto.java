package com.xk.search.gateway.dto.rsp.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsEntityDto {

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 收藏卡名称
     */
    private Long collectionCardId;

    /**
     * 收藏卡名称
     */
    private String collectionCardName;

    /**
     * 购买商品数量
     */
    private Integer goodsNum;

    /**
     * 商品单价
     */
    private Long amount;

    /**
     * 商品主图
     */
    private String goodsImages;

    /**
     * 收藏卡主图
     */
    private String collectionCardImages;

    /**
     * 板块
     */
    private String blockType;

    /**
     * 随机方式
     */
    private Integer randomType;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 规格单位
     */
    private String unitType;

    /**
     * 商品描述
     */
    private String goodsDescription;

    /**
     * 剩余随机状态 1-开启 0-关闭
     */
    private Integer remainRandomStatus;

}
