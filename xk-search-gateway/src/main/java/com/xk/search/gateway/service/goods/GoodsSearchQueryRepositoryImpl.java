package com.xk.search.gateway.service.goods;

import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.object.corp.CorpObjectIdentifier;
import com.myco.mydata.domain.model.session.SessionRoot;
import com.myco.mydata.domain.service.object.SelectorObjectRootService;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdSearchReqDto;
import com.xk.goods.interfaces.query.goods.GoodsSearchQueryService;
import com.xk.search.domain.commons.StringUtils;
import com.xk.search.domain.model.goods.*;
import com.xk.search.domain.model.goods.valobj.GoodsIndexValueObject;
import com.xk.search.domain.model.search.id.SearchIdentifier;
import com.xk.search.domain.repository.goods.GoodsSearchQueryRepository;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchChannelDefaultEnum;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsSearchQueryRepositoryImpl implements GoodsSearchQueryRepository {

    private final GoodsSearchQueryService goodsSearchQueryService;

    private final SelectorObjectRootService selectorObjectRootService;

    private final Converter converter;

    @Override
    public Mono<GoodsSearchRoot> getGoodsMallById(Mono<LongIdentifier> mono) {
        return mono.flatMap(identifier -> {
            GoodsIdSearchReqDto reqDto = GoodsIdSearchReqDto.builder().goodsId(identifier.id()).build();
            reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
            return goodsSearchQueryService.getGoodsMallById(Mono.just(reqDto));
        }).flatMap(dto -> {
            GoodsMallSearchEntity searchEntity =
                    converter.convert(dto, GoodsMallSearchEntity.class);
            return Mono.just(GoodsSearchRoot.builder()
                    .identifier(LongIdentifier.builder().id(dto.getGoodsId()).build())
                    .goodsIndexValueObject(GoodsIndexValueObject.builder()
                            .searchBizTypeEnum(SearchBizTypeEnum.GOODS_MALL).build())
                    .searchIdentifier(SearchIdentifier.builder()
                            .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType()).build())
                    .goodsMallSearchEntity(searchEntity).build());
        });
    }

    @Override
    public Mono<GoodsSearchRoot> getGoodsMerchantById(Mono<LongIdentifier> mono) {
        return mono.flatMap(identifier -> {
                            GoodsIdSearchReqDto reqDto = GoodsIdSearchReqDto.builder().goodsId(identifier.id()).build();
                            reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
                            return goodsSearchQueryService.getGoodsMerchantById(Mono.just(reqDto));
                        }
                )
                .flatMap(dto -> {
                    GoodsIdSearchReqDto reqDto = GoodsIdSearchReqDto.builder().goodsId(dto.getCollectibleCardId()).build();
                    reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
                    return goodsSearchQueryService.getGoodsCollectibleById(Mono.just(reqDto))
                            .flatMap(goodsSearchCollectibleResDto -> {
                                GoodsMerchantSearchEntity searchEntity =
                                        converter.convert(dto, GoodsMerchantSearchEntity.class);
                                searchEntity.setSeriesName(goodsSearchCollectibleResDto.getSeriesName());
                                searchEntity.setCollectibleCardName(goodsSearchCollectibleResDto.getGoodsName());
                                searchEntity.setBlockType(goodsSearchCollectibleResDto.getBlockType());
                                return Mono.just(searchEntity);
                            }).flatMap(goodsMerchantSearchEntity -> selectorObjectRootService
                                    .getCorpObjectRoot(Mono.just(CorpObjectIdentifier.builder()
                                            .corpId(StringUtils.toLong(goodsMerchantSearchEntity.getCorpInfoId())).build()))
                                    .flatMap(corpObjectRoot -> {
                                        goodsMerchantSearchEntity.setCorpLogo(corpObjectRoot.getCorpInfoObjectEntity().getCorpLogo());
                                        goodsMerchantSearchEntity.setCorpInfoName(corpObjectRoot.getCorpInfoObjectEntity().getCorpName());
                                        return Mono.just(goodsMerchantSearchEntity);
                                    })
                            ).flatMap(goodsMerchantSearchEntity -> Mono.just(GoodsSearchRoot.builder()
                                    .identifier(LongIdentifier.builder().id(dto.getGoodsId()).build())
                                    .goodsIndexValueObject(GoodsIndexValueObject.builder()
                                            .searchBizTypeEnum(SearchBizTypeEnum.GOODS_MERCHANT)
                                            .blockType(goodsMerchantSearchEntity.getBlockType())
                                            .build())
                                    .searchIdentifier(SearchIdentifier.builder()
                                            .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType()).build())
                                    .goodsMerchantSearchEntity(goodsMerchantSearchEntity).build()));
                });

    }

    @Override
    public Mono<GoodsSearchRoot> getGoodsCollectibleById(Mono<LongIdentifier> mono) {
        return mono.flatMap(identifier -> {
                            GoodsIdSearchReqDto reqDto = GoodsIdSearchReqDto.builder().goodsId(identifier.id()).build();
                            reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
                            return goodsSearchQueryService.getGoodsCollectibleById(Mono.just(reqDto));
                        }
                )
                .flatMap(dto -> {
                    CollectibleCardSearchEntity searchEntity =
                            converter.convert(dto, CollectibleCardSearchEntity.class);
                    return Mono.just(GoodsSearchRoot.builder()
                            .identifier(LongIdentifier.builder().id(dto.getGoodsId()).build())
                            .goodsIndexValueObject(GoodsIndexValueObject.builder()
                                    .searchBizTypeEnum(SearchBizTypeEnum.COLLECTIBLE_CARD).build())
                            .searchIdentifier(SearchIdentifier.builder()
                                    .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType()).build())
                            .collectibleCardSearchEntity(searchEntity).build());
                });
    }

    @Override
    public Mono<GoodsSearchRoot> getGoodsMaterialById(Mono<LongIdentifier> mono) {
        return mono.flatMap(identifier -> {
                            GoodsIdSearchReqDto reqDto = GoodsIdSearchReqDto.builder().goodsId(identifier.id()).build();
                            reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
                            return goodsSearchQueryService.getGoodsMaterialById(Mono.just(reqDto));
                        }
                )
                .flatMap(dto -> {
                    GoodsMaterialSearchEntity searchEntity =
                            converter.convert(dto, GoodsMaterialSearchEntity.class);
                    return Mono.just(GoodsSearchRoot.builder()
                            .identifier(LongIdentifier.builder().id(dto.getGoodsId()).build())
                            .goodsIndexValueObject(GoodsIndexValueObject.builder()
                                    .searchBizTypeEnum(SearchBizTypeEnum.GOODS_MATERIAL).build())
                            .searchIdentifier(SearchIdentifier.builder()
                                    .searchChannelType(SearchChannelDefaultEnum.DEFAULT_SEARCH.getSearchChannelType()).build())
                            .goodsMaterialSearchEntity(searchEntity).build());
                });
    }

}
