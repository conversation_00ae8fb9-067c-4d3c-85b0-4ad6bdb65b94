package com.xk.search.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import com.xk.acct.interfaces.query.UserFollowCorpQueryService;
import com.xk.corp.interfaces.query.corp.CorpQueryService;
import com.xk.goods.interfaces.query.goods.GoodsSearchQueryService;
import com.xk.goods.interfaces.query.goods.MerchantProductQueryService;
import com.xk.interfaces.query.object.CorpObjectQueryService;
import com.xk.interfaces.query.object.GoodsObjectQueryService;
import com.xk.interfaces.query.object.UserObjectQueryService;
import com.xk.order.interfaces.query.order.OrderQueryService;
import com.xk.order.interfaces.service.logistics.LogisticsOrderQueryService;

/**
 * @author: killer
 **/
public class XkSearchServiceConfig {

    @Bean
    public UserObjectQueryService userObjectQueryService(
            HttpServiceProxyFactory xkAcctHttpServiceProxyFactory) {
        return xkAcctHttpServiceProxyFactory.createClient(UserObjectQueryService.class);
    }

    @Bean
    public GoodsObjectQueryService goodsObjectQueryService(
            HttpServiceProxyFactory xkGoodsHttpServiceProxyFactory) {
        return xkGoodsHttpServiceProxyFactory.createClient(GoodsObjectQueryService.class);
    }

    @Bean
    public CorpObjectQueryService corpObjectQueryService(
            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpObjectQueryService.class);
    }

    @Bean
    public MerchantProductQueryService merchantProductQueryService(
            HttpServiceProxyFactory xkGoodsHttpServiceProxyFactory) {
        return xkGoodsHttpServiceProxyFactory.createClient(MerchantProductQueryService.class);
    }

    @Bean
    public CorpQueryService corpQueryService(
            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpQueryService.class);
    }

    @Bean
    public GoodsSearchQueryService validateCodeQueryService(
            HttpServiceProxyFactory xkGoodsHttpServiceProxyFactory) {
        return xkGoodsHttpServiceProxyFactory.createClient(GoodsSearchQueryService.class);
    }

    @Bean
    public UserFollowCorpQueryService userFollowCorpQueryService(
            HttpServiceProxyFactory xkAcctHttpServiceProxyFactory) {
        return xkAcctHttpServiceProxyFactory.createClient(UserFollowCorpQueryService.class);
    }

    @Bean
    public OrderQueryService orderQueryService(
            HttpServiceProxyFactory xkOrderHttpServiceProxyFactory) {
        return xkOrderHttpServiceProxyFactory.createClient(OrderQueryService.class);
    }

    @Bean
    public LogisticsOrderQueryService logisticsOrderQueryService(
            HttpServiceProxyFactory xkOrderHttpServiceProxyFactory) {
        return xkOrderHttpServiceProxyFactory.createClient(LogisticsOrderQueryService.class);
    }



}
