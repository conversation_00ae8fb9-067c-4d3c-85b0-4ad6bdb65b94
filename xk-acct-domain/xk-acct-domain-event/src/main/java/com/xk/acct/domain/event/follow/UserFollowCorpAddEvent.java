package com.xk.acct.domain.event.follow;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Date;
import java.util.Map;

@EventDefinition(
        appName = AppNameEnum.YD_USER,
        domainName = DomainNameEnum.USER
)
@Getter
public class UserFollowCorpAddEvent extends AbstractCommonsDomainEvent {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商户ID
     */
    private Long corpInfoId;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否删除（0：不删除，1：已删除）
     */
    private Integer isDelete;

    @Builder
    public UserFollowCorpAddEvent(@NonNull Long identifier, Map<String, Object> context, Long userId, Long corpInfoId, Long createId, Date createTime, Integer isDelete) {
        super(identifier, context);
        this.userId = userId;
        this.corpInfoId = corpInfoId;
        this.createId = createId;
        this.createTime = createTime;
        this.isDelete = isDelete;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }

}
