package com.xk.acct.domain.event.follow;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Date;
import java.util.Map;

@EventDefinition(
        appName = AppNameEnum.YD_USER,
        domainName = DomainNameEnum.USER
)
@Getter
public class UserFollowCorpSearchEvent extends AbstractCommonsDomainEvent {

    /**
     * 商户ID
     */
    private Long corpInfoId;

    /**
     * 关注数
     */
    private Integer number;

    @Builder
    public UserFollowCorpSearchEvent(@NonNull Long identifier, Map<String, Object> context, Long corpInfoId, Integer number) {
        super(identifier, context);
        this.corpInfoId = corpInfoId;
        this.number = number;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }

}
