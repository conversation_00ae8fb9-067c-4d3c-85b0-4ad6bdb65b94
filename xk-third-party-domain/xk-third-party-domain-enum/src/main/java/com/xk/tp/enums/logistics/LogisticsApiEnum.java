package com.xk.tp.enums.logistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @<PERSON> liucaihong
 * @Date 2024/8/16 10:09
 */
@Getter
@AllArgsConstructor
public enum LogisticsApiEnum {

    KUAI_DI_100(1, "快递100");

    private static final Map<Integer, LogisticsApiEnum> MAP;

    static {
        MAP = Arrays.stream(LogisticsApiEnum.values())
                .collect(Collectors.toMap(LogisticsApiEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static LogisticsApiEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
