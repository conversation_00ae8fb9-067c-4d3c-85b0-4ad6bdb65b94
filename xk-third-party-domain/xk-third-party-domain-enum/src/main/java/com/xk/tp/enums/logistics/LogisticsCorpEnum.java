package com.xk.tp.enums.logistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @<PERSON> liu<PERSON>ihong
 * @Date 2024/8/16 10:09
 */
@Getter
@AllArgsConstructor
public enum LogisticsCorpEnum {

    SF("顺丰快递", LogisticsApiEnum.KUAI_DI_100 ,"shunfengkuaiyun"),
    JD_KY("京东快递",LogisticsApiEnum.KUAI_DI_100 , "jingdongkuaiyun"),
    ;

    private static final Map<String, LogisticsCorpEnum> MAP;

    static {
        MAP = Arrays.stream(LogisticsCorpEnum.values())
                .collect(Collectors.toMap(LogisticsCorpEnum::getCode, enumValue -> enumValue));
    }

    private final String code;
    private final LogisticsApiEnum logisticsApi;
    private final String msg;

    public static LogisticsCorpEnum getByCode(String code) {
        return MAP.get(code);
    }
}
