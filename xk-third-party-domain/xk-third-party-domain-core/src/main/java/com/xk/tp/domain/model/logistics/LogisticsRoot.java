package com.xk.tp.domain.model.logistics;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.logistics.id.LogisticsIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class LogisticsRoot extends DomainRoot<LogisticsIdentifier> {

    private final LogisticsCorpEntity logisticsCorpEntity;

    @Builder
    public LogisticsRoot(LogisticsIdentifier identifier, LogisticsCorpEntity logisticsCorpEntity) {
        super(identifier);
        this.logisticsCorpEntity = logisticsCorpEntity;
    }

    @Override
    public Validatable<LogisticsIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

}
