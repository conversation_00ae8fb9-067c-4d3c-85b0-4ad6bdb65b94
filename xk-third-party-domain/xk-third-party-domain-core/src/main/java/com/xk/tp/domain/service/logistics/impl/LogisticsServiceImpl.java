package com.xk.tp.domain.service.logistics.impl;


import java.util.Map;

import org.springframework.stereotype.Service;

import com.myco.mydata.commons.annotation.BeansOfTypeToMap;
import com.xk.tp.domain.model.logistics.LogisticsDetailValObj;
import com.xk.tp.domain.model.logistics.LogisticsRoot;
import com.xk.tp.domain.service.logistics.LogisticsAdapterService;
import com.xk.tp.domain.service.logistics.LogisticsService;
import com.xk.tp.enums.logistics.LogisticsCorpEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsServiceImpl implements LogisticsService {

    @BeansOfTypeToMap(value = LogisticsAdapterService.class, methodName = "getAccountType")
    private Map<Integer, LogisticsAdapterService> searchAdapterServiceMap;

    @Override
    public Flux<LogisticsDetailValObj> detail(Mono<LogisticsRoot> queryMono) {
        return queryMono.flatMapMany(logisticsRoot -> {
            Integer apiCode = LogisticsCorpEnum
                    .getByCode(logisticsRoot.getIdentifier().getLogisticsCorpName())
                    .getLogisticsApi().getCode();
            return searchAdapterServiceMap.get(apiCode).detail(queryMono);
        });
    }
}
