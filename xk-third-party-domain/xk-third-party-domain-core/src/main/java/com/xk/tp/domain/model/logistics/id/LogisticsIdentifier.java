package com.xk.tp.domain.model.logistics.id;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogisticsIdentifier implements Identifier<LogisticsIdentifier> {

    /**
     * 物流公司名称
     */
    private String logisticsCorpName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    @NonNull
    @Override
    public LogisticsIdentifier getIdentifier() {
        return LogisticsIdentifier.builder().logisticsCorpName(logisticsCorpName)
                .logisticsNo(logisticsNo).build();
    }

}
