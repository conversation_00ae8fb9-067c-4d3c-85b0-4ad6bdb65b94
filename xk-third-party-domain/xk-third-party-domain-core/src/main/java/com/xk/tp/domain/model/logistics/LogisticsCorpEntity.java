package com.xk.tp.domain.model.logistics;

import java.util.List;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.logistics.id.LogisticsIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
public class LogisticsCorpEntity implements Entity<LogisticsIdentifier> {

    /**
     * 物流公司名称
     */
    private String logisticsCorpName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 收货人手机号
     */
    private String receivingMobile;

    /**
     * 物流轨迹
     */
    private List<LogisticsDetailValObj> logisticsDetailValObjList;


    @Override
    public @NonNull LogisticsIdentifier getIdentifier() {
        return LogisticsIdentifier.builder().logisticsCorpName(logisticsCorpName)
                .logisticsNo(logisticsNo).build();
    }

    @Override
    public Validatable<LogisticsIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
