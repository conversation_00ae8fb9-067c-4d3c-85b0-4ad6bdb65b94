<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.tp</groupId>
        <artifactId>xk-third-party-domain</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-third-party-domain-event</artifactId>
    <packaging>jar</packaging>
    <name>xk-third-party-domain-event</name>
    <description>xk-third-party-domain-event</description>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-domain-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.tp</groupId>
            <artifactId>xk-third-party-domain-enum</artifactId>
        </dependency>
    </dependencies>
</project>
