package com.xk.tp.domain.event.sms;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractTpDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * 支付完成事件
 */
@Getter
public class SendSmsEvent extends AbstractTpDomainEvent {

    /**
     * 手机号
     */
    private final String phoneNumber;
    /**
     * 【测试】验证码123
     */
    private final String content;

    /**
     * 扩展号
     */
    private final String ext;

    private final Integer businessType;

    @Builder
    public SendSmsEvent(@NonNull Long identifier, Map<String, Object> context, String phoneNumber,
            String content, String ext, Integer businessType) {
        super(identifier, context);
        this.phoneNumber = phoneNumber;
        this.content = content;
        this.ext = ext;
        this.businessType = businessType;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).phoneNumber(phoneNumber).content(content)
                .ext(ext).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
