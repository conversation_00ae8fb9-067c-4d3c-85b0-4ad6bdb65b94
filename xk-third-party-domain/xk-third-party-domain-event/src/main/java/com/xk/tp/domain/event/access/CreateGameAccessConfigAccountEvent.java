package com.xk.tp.domain.event.access;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_THIRD_PARTY,
        domainName = DomainNameEnum.THIRD_PARTY)
public class CreateGameAccessConfigAccountEvent extends AbstractCommonsDomainEvent {

    private final Long accessId;

    private final String accessName;

    private final Integer upDownStatus;

    private final Long initFee;

    private final Long renewFee;

    private final Long renewMonth;

    private final Long createId;

    @Builder
    public CreateGameAccessConfigAccountEvent(@NonNull Long identifier, Map<String, Object> context, Long accessId,
                                              String accessName, Integer upDownStatus, Long initFee, Long renewFee,
                                              Long renewMonth, Long createId) {
        super(identifier, context);
        this.accessName = accessName;
        this.upDownStatus = upDownStatus;
        this.initFee = initFee;
        this.renewFee = renewFee;
        this.accessId = accessId;
        this.renewMonth = renewMonth;
        this.createId = createId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).accessId(accessId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
