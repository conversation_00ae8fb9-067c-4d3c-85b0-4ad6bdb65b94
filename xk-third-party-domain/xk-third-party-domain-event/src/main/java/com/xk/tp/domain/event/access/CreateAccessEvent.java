package com.xk.tp.domain.event.access;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public class CreateAccessEvent extends AbstractGoodsDomainEvent {

    private final Long accessId;

    @Builder
    public CreateAccessEvent(@NonNull Long identifier, Map<String, Object> context, Long accessId) {
        super(identifier, context);
        this.accessId = accessId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).accessId(accessId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
