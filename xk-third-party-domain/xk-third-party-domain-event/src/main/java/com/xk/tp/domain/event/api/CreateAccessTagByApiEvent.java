package com.xk.tp.domain.event.api;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_THIRD_PARTY,
        domainName = DomainNameEnum.THIRD_PARTY)
public class CreateAccessTagByApiEvent extends AbstractCommonsDomainEvent {

    private final Long accessId;

    private final String tpTagId;

    private final String tpTagName;

    private final String tpGameId;

    @Builder
    public CreateAccessTagByApiEvent(@NonNull Long identifier, Map<String, Object> context, Long accessId,
                                     String tpTagId, String tpTagName, String tpGameId) {
        super(identifier, context);
        this.accessId = accessId;
        this.tpTagId = tpTagId;
        this.tpTagName = tpTagName;
        this.tpGameId = tpGameId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).accessId(accessId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
