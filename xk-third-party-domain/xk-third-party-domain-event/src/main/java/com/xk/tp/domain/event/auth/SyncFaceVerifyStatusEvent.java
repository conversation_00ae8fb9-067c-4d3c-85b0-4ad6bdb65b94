package com.xk.tp.domain.event.auth;

import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_THIRD_PARTY, domainName = DomainNameEnum.THIRD_PARTY)
public class SyncFaceVerifyStatusEvent extends AbstractCommonsDomainEvent {

    private final String authFlowId;
    private final Long userId;

    @Builder
    public SyncFaceVerifyStatusEvent(@NonNull Long identifier, Map<String, Object> context, String authFlowId,
                                     Long userId) {
        super(identifier, context);
        this.authFlowId = authFlowId;
        this.userId = userId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).authFlowId(authFlowId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
