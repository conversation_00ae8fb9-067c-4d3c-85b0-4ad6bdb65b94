package com.xk.tp.domain.event.access;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;

/**
 * 支付完成事件
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_THIRD_PARTY,
        domainName = DomainNameEnum.THIRD_PARTY)
public class UpdateAccessAccountEvent extends AbstractCommonsDomainEvent {

    private final Long accessAccountId;
    private final Long accessId;

    @Builder
    public UpdateAccessAccountEvent(@NonNull Long identifier, Map<String, Object> context, Long accessAccountId,
                                    Long accessId) {
        super(identifier, context);
        this.accessAccountId = accessAccountId;
        this.accessId = accessId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).accessAccountId(accessAccountId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
