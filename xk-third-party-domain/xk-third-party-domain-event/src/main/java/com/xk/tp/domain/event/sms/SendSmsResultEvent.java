package com.xk.tp.domain.event.sms;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractTpDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * 支付完成事件
 */
@Getter
public class SendSmsResultEvent extends AbstractTpDomainEvent {

    private final Boolean isSuccess;
    private final String message;
    private final Long templateId;
    private final String sendPhone;
    private final Map<String,String> sendParams;

    @Builder
    public SendSmsResultEvent(@NonNull Long identifier, Map<String, Object> context, Boolean isSuccess, String message,
                              Long templateId, String sendPhone, Map<String, String> sendParams) {
        super(identifier, context);
        this.isSuccess = isSuccess;
        this.message = message;
        this.templateId = templateId;
        this.sendPhone = sendPhone;
        this.sendParams = sendParams;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
