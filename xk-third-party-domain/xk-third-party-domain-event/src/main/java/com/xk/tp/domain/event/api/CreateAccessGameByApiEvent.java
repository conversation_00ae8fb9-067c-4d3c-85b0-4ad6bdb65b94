package com.xk.tp.domain.event.api;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_THIRD_PARTY,
        domainName = DomainNameEnum.THIRD_PARTY)
public class CreateAccessGameByApiEvent extends AbstractCommonsDomainEvent {

    private final Long accessId;

    private final String tpGameId;

    private final String tpgGameName;

    @Builder
    public CreateAccessGameByApiEvent(@NonNull Long identifier, Map<String, Object> context, Long accessId,
                                      String tpGameId, String tpgGameName) {
        super(identifier, context);
        this.accessId = accessId;
        this.tpGameId = tpGameId;
        this.tpgGameName = tpgGameName;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).accessId(accessId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
