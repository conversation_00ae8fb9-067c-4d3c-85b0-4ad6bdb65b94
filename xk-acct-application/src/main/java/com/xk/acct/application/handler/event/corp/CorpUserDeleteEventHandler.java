package com.xk.acct.application.handler.event.corp;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.acct.application.action.command.user.EditUserDataCommand;
import com.xk.acct.application.action.query.user.UserDataIdQuery;
import com.xk.acct.application.commons.XkAcctApplicationErrorEnum;
import com.xk.acct.application.support.XkAcctApplicationException;
import com.xk.acct.domain.model.user.UserDataEntity;
import com.xk.corp.domain.event.user.CorpUserDeleteEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CorpUserDeleteEventHandler extends AbstractEventVerticle<CorpUserDeleteEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CorpUserDeleteEvent> mono) {
        Function<CorpUserDeleteEvent, Mono<UserDataEntity>> queryUser = (event) -> {
            return queryDispatcher
                    .executeQuery(
                            Mono.just(UserDataIdQuery.builder().userId(event.getUserId()).build()),
                            UserDataIdQuery.class, UserDataEntity.class)
                    .switchIfEmpty(Mono.error(new XkAcctApplicationException(
                            XkAcctApplicationErrorEnum.USER_NOT_EXIST)));
        };

        Function<UserDataEntity, Mono<Void>> executeCommand = (entity) -> {
            // 修改
            EditUserDataCommand command = new EditUserDataCommand();
            command.setUserId(entity.getUserId());
            command.setUserType(UserTypeEnum.USER);
            return commandDispatcher.executeCommand(Mono.just(command), EditUserDataCommand.class);
        };

        return mono.flatMap(queryUser).flatMap(executeCommand);
    }
}
