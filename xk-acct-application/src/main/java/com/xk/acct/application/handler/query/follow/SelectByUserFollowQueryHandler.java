package com.xk.acct.application.handler.query.follow;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.xk.acct.application.action.query.follow.SelectByUserFollowQuery;
import com.xk.acct.domain.model.follow.UserFollowCorpEntity;
import com.xk.acct.domain.repository.follow.UserFollowCorpQueryRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class SelectByUserFollowQueryHandler implements IActionQueryManyHandler<SelectByUserFollowQuery, Long> {

    private final UserFollowCorpQueryRepository userFollowCorpQueryRepository;
    private final Converter converter;

    @Override
    public Flux<Long> execute(Mono<SelectByUserFollowQuery> mono) {
        return mono.flatMapMany(query -> userFollowCorpQueryRepository
                .selectByUserId(query.getUserId())
                .map(UserFollowCorpEntity::getCorpInfoId)
                .filter(corpInfoId -> query.getCorpInfoIdList().contains(corpInfoId))
        );
    }
}