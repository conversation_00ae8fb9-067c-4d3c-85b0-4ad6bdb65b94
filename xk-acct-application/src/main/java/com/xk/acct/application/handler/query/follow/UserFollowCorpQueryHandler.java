package com.xk.acct.application.handler.query.follow;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.acct.application.action.query.follow.UserFollowCorpQuery;
import com.xk.acct.domain.repository.follow.UserFollowCorpQueryRepository;
import com.xk.acct.interfaces.dto.rsp.follow.UserFollowCorpRspDtp;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class UserFollowCorpQueryHandler implements IActionQueryHandler<UserFollowCorpQuery, Pagination> {

    private final UserFollowCorpQueryRepository userFollowCorpQueryRepository;
    private final SelectorRootService selectorRootService;
    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<UserFollowCorpQuery> mono) {
        return mono.flatMap(query -> {
            query.setSort(query.getSort() == null ? null : StringUtils.join(
                    StringUtils.splitByCharacterTypeCamelCase(query.getSort()),
                    "_"
            ).toLowerCase());
            return ReadSynchronizationUtils.getUserIdMono()
                    .flatMap(selectorRootService::getUserObject)
                    .flatMap(userObjectRoot -> {
                        UserTypeEnum userType = userObjectRoot.getUserDataObjectEntity().getUserType();
                        if (UserTypeEnum.MERCHANT_KAS.equals(userType)) {
                            query.setCorpInfoId(userObjectRoot.getUserDataObjectEntity().getCorpId());
                        }
                        Pagination pagination = new Pagination();
                        pagination.setLimit(query.getLimit());
                        pagination.setOffset(query.getOffset());
                        pagination.setCriteria(CollectionHelper.converBeanToMap(query));
                        return Mono.just(pagination);
                    }).flatMap(pagination -> userFollowCorpQueryRepository.searchUserFollowCorp(pagination)
                            .flatMap(userFollowCorpEntity -> Mono.just(converter.convert(userFollowCorpEntity, UserFollowCorpRspDtp.class))).collectList().flatMap(list -> {
                                pagination.setRecords(list);
                                return Mono.just(pagination);
                            }));
        });
    }
}
