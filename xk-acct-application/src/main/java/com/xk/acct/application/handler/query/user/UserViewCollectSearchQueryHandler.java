package com.xk.acct.application.handler.query.user;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.acct.application.action.query.user.UserViewPagePaginationQuery;
import com.xk.acct.domain.model.user.UserCollectViewsEntity;
import com.xk.acct.domain.repository.user.UserRootQueryRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class UserViewCollectSearchQueryHandler implements IActionQueryHandler<UserViewPagePaginationQuery, Pagination> {

    private final UserRootQueryRepository userRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<UserViewPagePaginationQuery> query) {
        return this.execute(query,
                (searchQuery) -> {
                    Pagination pagination = new Pagination();
                    pagination.setLimit(searchQuery.getLimit());
                    pagination.setOffset(searchQuery.getOffset());
                    pagination.setCriteria(CollectionHelper.converBeanToMap(searchQuery));
                    return pagination;
                },
                userRootQueryRepository::findCollectViewPage,
                UserCollectViewsEntity.class, (data, clazz) -> {
                    return data;
                });
    }
}
