package com.xk.acct.application.handler.query.user;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.acct.application.action.query.user.UserDataIdQuery;
import com.xk.acct.application.action.query.user.UserDataMobileQuery;
import com.xk.acct.domain.model.user.UserDataEntity;
import com.xk.acct.domain.repository.user.UserRootQueryRepository;
import com.xk.acct.enums.user.IdentifyBindStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2025/5/13 18:42
 */
@Component
@RequiredArgsConstructor
public class UserDataMobileQueryHandler
        implements IActionQueryHandler<UserDataMobileQuery, UserDataEntity> {
    private final UserRootQueryRepository userRootQueryRepository;

    @Override
    public Mono<UserDataEntity> execute(Mono<UserDataMobileQuery> userDataMobileQueryMono) {
        return execute(userDataMobileQueryMono,
                userDataMobileQuery -> UserDataEntity.builder()
                        .mobile(userDataMobileQuery.getMobile())
                        .mobileStatus(IdentifyBindStatusEnum.BIND).build(),
                userRootQueryRepository::getUserDataByMobile);
    }
}
