package com.xk.acct.application.service;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.model.session.Session;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.acct.application.action.command.session.UnbindSessionCommand;
import com.xk.acct.application.action.command.session.UpdateSessionCommand;
import com.xk.acct.interfaces.dto.req.session.UpdateSessionReqDto;
import com.xk.acct.interfaces.service.SessionService;

import lombok.AllArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Service
@AllArgsConstructor
public class SessionServiceImpl implements SessionService {

    private final ActionCommandDispatcher<AbstractActionCommand> actionCommandDispatcher;

    @BusiCode
    @Override
    public Mono<Void> unbindSession(Mono<RequireSessionDto> requireSessionMono) {
        return actionCommandDispatcher.executeCommand(requireSessionMono,
                UnbindSessionCommand.class);
    }

    @BusiCode
    @Override
    public Mono<Session> updateSession(Mono<UpdateSessionReqDto> updateSessionReqDtoMono) {
        return ReadSynchronizationUtils.getSessionObjectMono()
                .flatMap(session -> updateSessionReqDtoMono.flatMap(updateSessionReqDto -> {
                    boolean change = false;
                    String securityCode = updateSessionReqDto.getSecurityCode();
                    if (StringUtils.hasText(securityCode) && securityCode.length() <= 12) {
                        session.setSecurityCode(securityCode);
                        change = true;
                    }
                    SystemLanguageLocale languageMode =
                            updateSessionReqDto.getSystemLanguageLocale();
                    if (languageMode != null) {
                        session.setSystemLanguageLocale(languageMode);
                        change = true;
                    }
                    if (change) {
                        return actionCommandDispatcher
                                .executeCommand(Mono.just(session), UpdateSessionCommand.class)
                                .thenReturn(session);
                    } else {
                        UnbindSessionCommand command = new UnbindSessionCommand();
                        command.setSessionId(updateSessionReqDto.getSessionId());
                        return actionCommandDispatcher
                                .executeCommand(Mono.just(command), UnbindSessionCommand.class)
                                .then(Mono.empty());
                    }
                }));
    }
}
