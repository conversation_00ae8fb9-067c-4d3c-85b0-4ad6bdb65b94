package com.xk.acct.application.handler.event.user;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.acct.application.action.command.count.CreateUserCountCommand;
import com.xk.acct.application.action.command.count.RemoveUserCountCacheCommand;
import com.xk.acct.application.action.query.count.UserCountDbQuery;
import com.xk.acct.application.action.query.count.UserCountQuery;
import com.xk.acct.domain.event.user.UserCountEvent;
import com.xk.acct.enums.count.UserCountTypeEnum;
import com.xk.acct.interfaces.dto.rsp.count.UserCountRspDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserCountIntoDbEventHandler extends AbstractEventVerticle<UserCountEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    public static List<String> getLastFiveDays(LocalDate givenDate) {
        List<String> lastFiveDays = new ArrayList<>();

        // 计算近五天的日期，注意不包含当天
        for (int i = 1; i <= 5; i++) {
            LocalDate date = givenDate.minusDays(i);
            // 格式化为 yyyy-MM-dd 格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            lastFiveDays.add(date.format(formatter));
        }

        return lastFiveDays;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<UserCountEvent> event) {
        return event.flatMap(data -> {
            List<Integer> codeList = Arrays.stream(UserCountTypeEnum.values())
                    .map(UserCountTypeEnum::getCode)
                    .toList();
            return Flux.fromIterable(codeList).flatMap(code -> {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate givenDate = LocalDate.parse(data.getDay(), formatter);

                // 获取近五天的日期集合
                List<String> lastFiveDays = getLastFiveDays(givenDate);

                return Flux.fromIterable(lastFiveDays).flatMap(day -> {
                    UserCountQuery userCountQuery = UserCountQuery.builder().type(code).day(day).build();
                    return this.actionQueryDispatcher.process(Mono.just(userCountQuery), UserCountQuery.class,
                                    UserCountRspDto.class)
                            .flatMap(userCountRspDto -> {
                                UserCountDbQuery userCountDbQuery = UserCountDbQuery.builder()
                                        .type(code)
                                        .day(day)
                                        .build();
                                return this.actionQueryDispatcher.process(Mono.just(userCountDbQuery),
                                                UserCountDbQuery.class, UserCountRspDto.class)
                                        .defaultIfEmpty(UserCountRspDto.builder().build())
                                        .flatMap(dbUserCount -> {
                                            Mono<Void> createMono = Mono.empty();
                                            if (dbUserCount.getType() == null) {
                                                CreateUserCountCommand createUserCountCommand = CreateUserCountCommand.builder()
                                                        .day(day)
                                                        .type(code)
                                                        .createTime(new Date())
                                                        .num(userCountRspDto.getNum())
                                                        .build();
                                                createMono = this.commandDispatcher.process(
                                                        Mono.just(createUserCountCommand), CreateUserCountCommand.class,
                                                        Void.class);
                                            }
                                            return createMono.then(Mono.defer(() -> {
                                                RemoveUserCountCacheCommand removeUserCountCacheCommand = RemoveUserCountCacheCommand.builder()
                                                        .type(code)
                                                        .day(day)
                                                        .build();
                                                return this.commandDispatcher.process(
                                                        Mono.just(removeUserCountCacheCommand),
                                                        RemoveUserCountCacheCommand.class, Void.class);
                                            }));
                                        });
                            });
                }).collectList();
            }).collectList();
        }).then();
    }
}
