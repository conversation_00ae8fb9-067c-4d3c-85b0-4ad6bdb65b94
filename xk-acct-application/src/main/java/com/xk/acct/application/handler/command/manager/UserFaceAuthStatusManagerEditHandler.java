package com.xk.acct.application.handler.command.manager;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.acct.application.action.command.manager.UserFaceAuthStatusManagerEditCommand;
import com.xk.acct.domain.model.user.UserDataEntity;
import com.xk.acct.domain.model.user.UserRoot;
import com.xk.acct.domain.repository.user.UserRootRepository;
import io.github.linpeilie.Converter;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2025/5/23 15:15
 */
@Component
@AllArgsConstructor
public class UserFaceAuthStatusManagerEditHandler implements IActionCommandHandler<UserFaceAuthStatusManagerEditCommand, Void> {

    private final UserRootRepository userRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UserFaceAuthStatusManagerEditCommand> commandMono) {
        return commandMono.flatMap(command -> {
            UserDataEntity userDataEntity = converter.convert(command, UserDataEntity.class);
            userDataEntity.setLastUpdateTime(new Date());
            return userRootRepository.update(UserRoot.builder().userData(userDataEntity)
                    .identifier(LongIdentifier.builder().id(command.getUserId()).build())
                    .build());
        });
    }
}
