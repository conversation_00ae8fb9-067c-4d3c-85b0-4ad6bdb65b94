package com.xk.acct.application.handler.query.user;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.acct.application.action.query.user.UserSecurityIdQuery;
import com.xk.acct.domain.model.user.UserSecurityEntity;
import com.xk.acct.domain.repository.user.UserRootQueryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class UserSecurityIdQueryHandler implements IActionQueryHandler<UserSecurityIdQuery, UserSecurityEntity> {

    private final UserRootQueryRepository userRootQueryRepository;

    @Override
    public Mono<UserSecurityEntity> execute(Mono<UserSecurityIdQuery> query) {
        return execute(query, UserSecurityIdQuery::getIdentifier, userRootQueryRepository::getUserSecurityById);
    }

}
