package com.xk.acct.application.action.command.user;

import com.xk.acct.domain.model.user.UserInsuranceEntity;
import com.xk.acct.interfaces.dto.req.user.UserInsuranceEditReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@AutoMappers({
        @AutoMapper(target = UserInsuranceEditReqDto.class),
        @AutoMapper(target = UserInsuranceEntity.class),
})
@Data
public class EditUserInsuranceCommand extends AbstractUserCommand {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * qq
     */
    private String qq;

    /**
     * 微信
     */
    private String wechat;

    /**
     * 省的中文名称(app端直接使用中文)
     */
    private String province;

    /**
     * 县市中文名称(app端直接使用中文)
     */
    private String city;

    /**
     * 区
     */
    private String county;

    /**
     * 父亲姓名
     */
    private String fatherName;

    /**
     * 母亲姓名
     */
    private String motherName;

    /**
     * 父亲手机号
     */
    private String fatherMobile;

    /**
     * 母亲手机号
     */
    private String motherMobile;

    /**
     * 网购截图
     */
    private String screenshot;

    /**
     * 详细地址
     */
    private String address;
}
