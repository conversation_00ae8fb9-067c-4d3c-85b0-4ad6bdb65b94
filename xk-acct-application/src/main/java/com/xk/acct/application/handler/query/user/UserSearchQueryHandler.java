package com.xk.acct.application.handler.query.user;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.acct.application.action.query.user.UserSearchQuery;
import com.xk.acct.domain.model.user.UserDataEntity;
import com.xk.acct.domain.repository.user.UserRootQueryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class UserSearchQueryHandler implements IActionQueryHandler<UserSearchQuery, Pagination> {

    private final UserRootQueryRepository userRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<UserSearchQuery> queryMono) {
        return execute(queryMono, userSearchQuery -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(userSearchQuery.getLimit());
            pagination.setOffset(userSearchQuery.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(userSearchQuery));
            return pagination;
        }, userRootQueryRepository::searchUserData, UserDataEntity.class);
    }
}
