package com.xk.acct.application.handler.query.follow;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.acct.application.action.query.follow.UserCorpFollowStatusQuery;
import com.xk.acct.infrastructure.cache.dao.follow.UserFollowCorpQueryDao;
import com.xk.acct.infrastructure.cache.key.follow.UserFollowCorpQueryKey;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class UserCorpFollowStatusQueryHandler implements IActionQueryHandler<UserCorpFollowStatusQuery, Double> {

    private final UserFollowCorpQueryDao userFollowCorpQueryDao;
    private final Converter converter;

    @Override
    public Mono<Double> execute(Mono<UserCorpFollowStatusQuery> mono) {
        return mono.flatMap(query -> {
            Double value = userFollowCorpQueryDao.getValue(UserFollowCorpQueryKey.builder().corpInfoId(query.getCorpInfoId()).build(), query.getUserId());
            return value == null ? Mono.empty() : Mono.just(value);
        });
    }
}