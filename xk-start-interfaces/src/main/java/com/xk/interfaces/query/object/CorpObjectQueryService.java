package com.xk.interfaces.query.object;

import com.xk.interfaces.dto.req.object.CorpObjectReqDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.xk.interfaces.dto.rsp.object.corp.CorpObjectRspDto;

import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@HttpExchange("/corp/object")
public interface CorpObjectQueryService {

    /**
     * 获取商家大对象
     * 
     * @param corpObjectReqDtoMono corpObjectReqDtoMono
     * @return Mono<CorpObjectRspDto>
     */
    @PostExchange("/getCorpObject")
    Mono<CorpObjectRspDto> getCorpObject(@RequestBody Mono<CorpObjectReqDto> corpObjectReqDtoMono);
}
