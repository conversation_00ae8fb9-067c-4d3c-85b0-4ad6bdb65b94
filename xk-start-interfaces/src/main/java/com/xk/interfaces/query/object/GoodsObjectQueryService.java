package com.xk.interfaces.query.object;

import com.xk.interfaces.dto.req.object.GoodsObjectReqDto;
import com.xk.interfaces.dto.rsp.object.goods.GoodsObjectRspDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import reactor.core.publisher.Mono;

/**
 * 用户查询
 *
 * @author: killer
 **/
@HttpExchange("/goods/object")
public interface GoodsObjectQueryService {

    /**
     * 获取商品大对象
     * @param goodsObjectReqDtoMono goodsObjectReqDtoMono
     * @return Mono<GoodsObjectRspDto>
     */
    @PostExchange("/getGoodsObject")
    Mono<GoodsObjectRspDto> getGoodsObject(@RequestBody Mono<GoodsObjectReqDto> goodsObjectReqDtoMono);

}
