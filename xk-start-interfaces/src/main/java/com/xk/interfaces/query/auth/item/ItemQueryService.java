package com.xk.interfaces.query.auth.item;

import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.interfaces.query.IQueryService;
import com.xk.interfaces.dto.req.auth.item.ItemByMenuIdReqDto;
import com.xk.interfaces.dto.req.auth.item.ItemIdReqDto;
import com.xk.interfaces.dto.req.auth.item.ItemQueryReqDto;
import com.xk.interfaces.dto.rsp.auth.item.ItemRspDto;

import reactor.core.publisher.Mono;

/**
 * 权限查询
 *
 * <AUTHOR>
 */
@HttpExchange("/auth/item/query")
public interface ItemQueryService extends IQueryService {


    /**
     * 查询权限分页列表
     *
     * @param dto
     * @return
     */
    @PostExchange("/searchItemPager")
    Mono<Pagination> searchItemPager(@RequestBody Mono<ItemQueryReqDto> dto);

    /**
     * 根据菜单id查询权限列表(包括子权限的)
     *
     * @param dto
     * @return
     */
    @PostExchange("/searchAllItemList")
    Mono<List<ItemRspDto>> searchAllItemList(@RequestBody Mono<ItemByMenuIdReqDto> dto);

    /**
     * 根据id查询权限
     *
     * @param dto
     * @return
     */
    @PostExchange("/getById")
    Mono<ItemRspDto> getById(@RequestBody Mono<ItemIdReqDto> dto);
}
