package com.xk.interfaces.query.object;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.xk.interfaces.dto.req.object.UserObjectReqDto;
import com.xk.interfaces.dto.rsp.object.user.UserObjectRspDto;

import reactor.core.publisher.Mono;

/**
 * 用户查询
 *
 * @author: killer
 **/
@HttpExchange("/acct/object")
public interface UserObjectQueryService {

    /**
     * 获取用户对象
     * 
     * @param userObjectReqDtoMono userObjectReqDtoMono
     * @return Mono<UserObjectRspDto>
     */
    @PostExchange("/getUserObject")
    Mono<UserObjectRspDto> getUserObject(@RequestBody Mono<UserObjectReqDto> userObjectReqDtoMono);

}
