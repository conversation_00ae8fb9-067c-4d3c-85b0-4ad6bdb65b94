package com.xk.interfaces.query.auth.role;

import java.util.List;

import com.xk.interfaces.dto.req.auth.item.ItemQueryReqDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.interfaces.query.IQueryService;
import com.xk.interfaces.dto.req.auth.role.RoleByGroupIdReqDto;
import com.xk.interfaces.dto.req.auth.role.RoleIdReqDto;
import com.xk.interfaces.dto.req.auth.role.RolePagerReqDto;
import com.xk.interfaces.dto.rsp.auth.item.ItemRspDto;
import com.xk.interfaces.dto.rsp.auth.role.RoleRspDto;
import com.xk.interfaces.dto.tree.TreeNodeRspDto;

import reactor.core.publisher.Mono;

/**
 * 角色查询
 *
 * <AUTHOR>
 */
@HttpExchange("/auth/role/query")
public interface RoleQueryService extends IQueryService {

    /**
     * 查询角色分页列表
     *
     * @param dto
     * @return
     */
    @PostExchange("/searchRolePager")
    Mono<Pagination> searchRolePager(@RequestBody Mono<RolePagerReqDto> dto);

    /**
     * 根据id查询角色
     *
     * @param dto
     * @return
     */
    @PostExchange("/getById")
    Mono<RoleRspDto> getById(@RequestBody Mono<RoleIdReqDto> dto);

    /**
     * 根据分组id查询角色列表(包括子分组的)
     *
     * @param dto
     * @return
     */
    @PostExchange("/searchAllRoleByGroupId")
    Mono<List<RoleRspDto>> searchAllRoleByGroupId(@RequestBody Mono<RoleByGroupIdReqDto> dto);

    /**
     * 根据角色id查询权限列表
     *
     * @param dto
     * @return
     */
    @PostExchange("/searchItemByRoleId")
    Mono<List<ItemRspDto>> searchItemByRoleId(@RequestBody Mono<ItemQueryReqDto> dto);

    /**
     * 根据id查询菜单树
     *
     * @param dto dto
     * @return 菜单树
     */
    @PostExchange("/searchMenuTreeById")
    Mono<List<TreeNodeRspDto>> searchMenuTreeById(@RequestBody Mono<RoleIdReqDto> dto);
}
