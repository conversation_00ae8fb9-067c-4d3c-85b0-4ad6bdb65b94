package com.xk.interfaces.query.auth.userrole;

import java.util.List;

import com.xk.interfaces.dto.req.auth.userrole.MenuBySessionReqDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.mydata.interfaces.query.IQueryService;
import com.xk.interfaces.dto.req.auth.userrole.UserRoleReqDto;
import com.xk.interfaces.dto.rsp.auth.menu.MenuRspDto;
import com.xk.interfaces.dto.rsp.auth.userrole.UserRoleRspDto;

import reactor.core.publisher.Mono;

/**
 * 用户角色查询
 *
 * <AUTHOR>
 */
@HttpExchange("/auth/user/role/query")
public interface UserRoleQueryService extends IQueryService {

    /**
     * 根据id查询角色
     *
     * @param dto
     * @return
     */
    @PostExchange("/getByUserId")
    Mono<UserRoleRspDto> getByUserId(@RequestBody Mono<UserRoleReqDto> dto);


    /**
     * 根据会话查询所有菜单
     *
     * @param dto
     * @return
     */
    @PostExchange("/getMenusBySession")
    Mono<List<MenuRspDto>> getMenusBySession(@RequestBody Mono<MenuBySessionReqDto> dto);
}
