package com.xk.interfaces.query.auth.menu;

import com.myco.mydata.interfaces.query.IQueryService;
import com.xk.interfaces.dto.req.auth.menu.MenuIdReqDto;
import com.xk.interfaces.dto.req.auth.menu.MenuQueryReqDto;
import com.xk.interfaces.dto.rsp.auth.menu.MenuRspDto;
import com.xk.interfaces.dto.tree.TreeNodeRspDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 菜单查询
 *
 * <AUTHOR>
 */
@HttpExchange("/auth/menu/query")
public interface MenuQueryService extends IQueryService {

    /**
     * 查询菜单树
     *
     * @param dto
     * @return
     */
    @PostExchange("/searchMenuTree")
    Mono<List<TreeNodeRspDto>> searchMenuTree(@RequestBody Mono<MenuQueryReqDto> dto);

    /**
     * 根据id查询菜单
     *
     * @param dto
     * @return
     */
    @PostExchange("/getById")
    Mono<MenuRspDto> getById(@RequestBody Mono<MenuIdReqDto> dto);
}
