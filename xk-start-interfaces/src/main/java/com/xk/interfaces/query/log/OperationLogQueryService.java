package com.xk.interfaces.query.log;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.interfaces.query.IQueryService;
import com.xk.interfaces.dto.req.log.OperationLogPagerReqDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;


@HttpExchange("/operation/log/query")
public interface OperationLogQueryService extends IQueryService {

    /**
     * 操作日志分页列表
     *
     * @param dtoMono
     * @return
     */
    @PostExchange("/searchPager")
    Mono<Pagination> searchPager(@RequestBody Mono<OperationLogPagerReqDto> dtoMono);
}
