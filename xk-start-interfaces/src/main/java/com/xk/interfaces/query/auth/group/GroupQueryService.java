package com.xk.interfaces.query.auth.group;

import com.myco.mydata.interfaces.query.IQueryService;
import com.xk.interfaces.dto.req.auth.group.GroupIdReqDto;
import com.xk.interfaces.dto.req.auth.group.GroupQueryReqDto;
import com.xk.interfaces.dto.rsp.auth.group.GroupRspDto;
import com.xk.interfaces.dto.tree.TreeNodeRspDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 分组查询
 *
 * <AUTHOR>
 */
@HttpExchange("/auth/group/query")
public interface GroupQueryService extends IQueryService {

    /**
     * 查询分组树
     *
     * @param dto
     * @return
     */
    @PostExchange("/searchGroupTree")
    Mono<List<TreeNodeRspDto>> searchGroupTree(@RequestBody Mono<GroupQueryReqDto> dto);

    /**
     * 查询分组列表
     *
     * @param dto
     * @return
     */
    @PostExchange("/searchGroupList")
    Mono<List<GroupRspDto>> searchGroupList(@RequestBody Mono<GroupQueryReqDto> dto);

    /**
     * 根据id查询分组
     *
     * @param dto
     * @return
     */
    @PostExchange("/getById")
    Mono<GroupRspDto> getById(@RequestBody Mono<GroupIdReqDto> dto);
}
