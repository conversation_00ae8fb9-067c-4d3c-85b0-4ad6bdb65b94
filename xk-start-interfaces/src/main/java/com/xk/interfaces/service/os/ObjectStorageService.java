package com.xk.interfaces.service.os;


import com.myco.mydata.interfaces.service.IApplicationService;
import com.xk.interfaces.dto.req.ObjectStorageReqDto;
import com.xk.interfaces.dto.req.ObjectStorageUploadConfirmReqDto;
import com.xk.interfaces.dto.rsp.ObjectStorageRspDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 对象存储服务
 * @author: killer
 **/
@HttpExchange("config/os")
public interface ObjectStorageService extends IApplicationService {

    /**
     * 获取Token
     * */
    @PostExchange("/applyToken")
    Mono<ObjectStorageRspDto> applyToken(@RequestBody Mono<ObjectStorageReqDto> objectStorageRootMono);

    /**
     * 上传文件确认
     * @param objectStorageUploadConfirmReqDtoMono objectStorageUploadConfirmReqDtoMono
     * @return Mono<Void>
     */
    @PostExchange("/uploadConfirm")
    Mono<Void> updateUpload(@RequestBody Mono<ObjectStorageUploadConfirmReqDto> objectStorageUploadConfirmReqDtoMono);


}
