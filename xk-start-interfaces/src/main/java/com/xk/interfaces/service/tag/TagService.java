package com.xk.interfaces.service.tag;

import com.myco.mydata.interfaces.service.IApplicationService;
import com.xk.interfaces.dto.tag.TagIdentifierDto;
import com.xk.interfaces.dto.tag.TagInfoDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@HttpExchange("/tag")
public interface TagService extends IApplicationService {

    @PostExchange("/save")
    Mono<Void> save(@RequestBody Mono<TagInfoDto> dto);

    @PostExchange("/update")
    Mono<Void> update(@RequestBody Mono<TagInfoDto> dto);

    @PostExchange("/remove")
    Mono<Void> remove(@RequestBody Mono<TagIdentifierDto> dto);

}
