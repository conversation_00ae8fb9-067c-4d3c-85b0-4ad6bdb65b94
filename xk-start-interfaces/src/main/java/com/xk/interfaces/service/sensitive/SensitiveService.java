package com.xk.interfaces.service.sensitive;

import com.myco.mydata.interfaces.service.IApplicationService;
import com.xk.interfaces.dto.config.ImportReqDto;
import com.xk.interfaces.dto.req.sensitive.SensitiveConfigEditReqDto;
import com.xk.interfaces.dto.req.sensitive.SensitiveConfigIdsReqDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;


@HttpExchange("/config/sensitive")
public interface SensitiveService extends IApplicationService {

    /**
     * 添加敏感词
     * @param dtoMono
     * @return
     */
    @PostExchange("/saveSensitiveConfig")
    Mono<Void> saveSensitiveConfig(@RequestBody Mono<SensitiveConfigEditReqDto> dtoMono);

    /**
     * 修改敏感词
     * @param dtoMono
     * @return
     */
    @PostExchange("/updateSensitiveConfig")
    Mono<Void> updateSensitiveConfig(@RequestBody Mono<SensitiveConfigEditReqDto> dtoMono);


    /**
     * 修改敏感词启用
     * @param dtoMono
     * @return
     */
    @PostExchange("/updateSensitiveConfigEnable")
    Mono<Void> updateSensitiveConfigEnable(@RequestBody Mono<SensitiveConfigIdsReqDto> dtoMono);

    /**
     * 修改敏感词禁用
     * @param dtoMono
     * @return
     */
    @PostExchange("/updateSensitiveConfigDisable")
    Mono<Void> updateSensitiveConfigDisable(@RequestBody Mono<SensitiveConfigIdsReqDto> dtoMono);

    /**
     * 删除敏感词
     * @param dtoMono
     * @return
     */
    @PostExchange("/deleteSensitiveConfig")
    Mono<Void> deleteSensitiveConfig(@RequestBody Mono<SensitiveConfigIdsReqDto> dtoMono);


    /**
     * 上传文件添加
     * @param dtoMono
     * @return
     */
    @PostExchange("/uploadSensitiveConfig")
    Mono<Void> commitUploadSensitiveConfig(@RequestBody Mono<ImportReqDto> dtoMono);
}
