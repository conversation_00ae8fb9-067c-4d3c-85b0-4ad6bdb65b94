package com.xk.interfaces.dto.config;

import java.util.List;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import lombok.*;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BusinessConfigDto extends RequireSessionDto {
    /**
     * 配置id
     */
    private Long businessConfigId;

    private Integer businessType;

    private String groupType;

    private String groupId;
    /**
     * key
     */
    private String key;
    /**
     * 值
     */
    private String val;

    private List<String> keys;
}
