package com.xk.interfaces.dto.req.auth.role;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveGroupRoleReqDto extends RequireSessionDto {

    /**
     * 角色id
     */
    @NotNull
    private Long roleId;
    /**
     * 分组id集合
     */
    @NotEmpty
    private Set<Long> groupIds;

}