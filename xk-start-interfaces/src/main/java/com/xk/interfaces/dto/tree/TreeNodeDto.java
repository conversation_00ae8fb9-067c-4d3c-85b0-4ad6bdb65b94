package com.xk.interfaces.dto.tree;

import java.util.List;

import com.myco.mydata.domain.model.CompositeIdentifier;

import lombok.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TreeNodeDto extends TreeNodeIdentifierDto{

    private Long parentId;

    private String name;

    private Integer sort;

    private Integer status;

    private Integer heightLimit;

    private CompositeIdentifier compositeIdentifier;

    private List<TreeNodeDto> childrenDto;

}
