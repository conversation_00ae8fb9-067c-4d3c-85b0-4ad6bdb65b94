package com.xk.interfaces.dto.config;

import com.myco.mydata.domain.model.action.session.AbstractSession;
import lombok.*;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class BusinessConfigInfoReqDto extends AbstractSession {

    private Integer businessType;

    private String groupType;

    private String groupId;
    /**
     * key
     */
    private String key;

}
