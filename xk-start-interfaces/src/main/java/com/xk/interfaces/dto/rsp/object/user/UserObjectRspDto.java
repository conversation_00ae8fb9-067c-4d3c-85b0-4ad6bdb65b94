package com.xk.interfaces.dto.rsp.object.user;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserObjectRspDto {

    private Long userId;

    private UserDataObjectRspDto userData;

    private List<UserConfigObjectRspDto> configList;
}
