package com.xk.interfaces.dto.req.auth.role;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * t_sensitive_config
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateRoleReqDto extends RoleIdReqDto {

    /**
     * 角色名称
     */
    @NotBlank
    private String roleName;

    /**
     * 角色英文名称
     */
    @NotBlank
    private String roleNameEn;

    /**
     * 描述
     */
    private String roleDesc;

    /**
     * 1:正常；2：停用
     */
    @NotNull
    private Integer status;

    /**
     * 排序
     */
    @Min(0)
    @NotNull
    private Integer sort;

    /**
     * 分组id
     */
    private Long groupId;
}