package com.xk.interfaces.dto.tag;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TagInfoDto extends TagIdentifierDto{

    private Long tagId;

    private Long groupId;

    private String groupBusinessType;

    private Integer busiType;

    private String tagName;

    private Integer status;

    private Integer sort;

    private String cateName;

    /**
     * 抓取类型
     */
    private Integer grabType;
}
