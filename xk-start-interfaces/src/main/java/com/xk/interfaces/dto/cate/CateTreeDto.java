package com.xk.interfaces.dto.cate;

import com.myco.mydata.domain.model.CompositeIdentifier;
import com.xk.interfaces.dto.tag.TagInfoDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CateTreeDto{

    private Long nodeId;

    private Long parentId;

    private String name;

    private Integer sort;

    private Integer status;

    private CompositeIdentifier compositeIdentifier;

    private List<TagInfoDto> tagInfoDtos;

    private List<CateTreeDto> childrenDto;

    private Map<String, Object> busiAttr;

}
