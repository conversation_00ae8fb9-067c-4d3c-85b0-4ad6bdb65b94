package com.xk.interfaces.dto.req.sensitive;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/27 15:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BatchAddSensitiveConfigReqDto extends RequireSessionDto {

    /**
     * 列表
     */
    private List<SensitiveConfigEditReqDto> sensitiveConfigs;
}
