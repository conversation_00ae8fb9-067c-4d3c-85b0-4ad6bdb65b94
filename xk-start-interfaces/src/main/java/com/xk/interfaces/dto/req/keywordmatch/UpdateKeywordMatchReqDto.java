package com.xk.interfaces.dto.req.keywordmatch;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * t_sensitive_config
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateKeywordMatchReqDto extends RequireSessionDto {
    /**
     * 业务id
     */
    @NotBlank
    private String busiId;

    /**
     * 业务类型
     */
    @NotNull
    private Integer busiType;

    /**
     * 关键词
     */
    @Size(max = 1000)
    private String keyword;

}