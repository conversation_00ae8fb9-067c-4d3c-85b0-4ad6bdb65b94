package com.xk.interfaces.dto.rsp.object.goods;

import java.util.List;

import com.myco.mydata.domain.model.object.corp.CorpObjectIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoodsObjectRspDto {
    /**
     * 商品信息
     */
    private GoodsInfoObjectRspDto goodsInfo;
    /**
     * 商品资源
     */
    private List<GoodsResObjectRspDto> resList;

    /**
     * 规格列表
     */
    private List<SpecificationObjectRspDto> specificationList;

    /**
     * 商家id
     */
    private CorpObjectIdentifier corpObjectIdentifier;

}
