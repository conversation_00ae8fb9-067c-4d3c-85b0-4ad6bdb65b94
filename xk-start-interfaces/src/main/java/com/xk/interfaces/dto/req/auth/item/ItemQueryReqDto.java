package com.xk.interfaces.dto.req.auth.item;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ItemQueryReqDto extends RequireSessionDtoPager {

    /**
     * 名称
     */
    private String itemName;

    /**
     * 英文名称
     */
    private String itemNameEn;

    /**
     * 格式：urimodule/controll/action
     */
    private String uri;



    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 是否显示uri(1 显示 0 不显示)
     */
    private Integer showUri;

    /**
     * 在菜单里是否显示(1 显示 0 不显示）
     */
    private Integer showInMenu;


    /**
     * 位置
     */
    private String itemPosition;

    /**
     * 是否记录日志 0否 1是
     */
    private Integer isLog;
    /**
     * 是否验证 0否 1是
     */
    private Integer isAuth;
    /**
     * 菜单id
     */
    private Long menuId;

    /**
     * 权限id
     */
    private String itemId;

    /**
     * 角色Id
     */
    private Long roleId;
}