package com.xk.interfaces.dto.req.auth.role;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RolePagerReqDto extends RequireSessionDtoPager {

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色英文名称
     */
    private String roleNameEn;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 1:正常；2：停用
     */
    private Integer status;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 分组id
     */
    private Long groupId;
}