package com.xk.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ObjectStorageUploadConfirmReqDto extends RequireSessionDto {

    @Valid
    @NotEmpty
    private List<ObjectStorageUploadConfirmDto> uploads;
}
