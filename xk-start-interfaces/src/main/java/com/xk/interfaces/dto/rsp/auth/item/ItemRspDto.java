package com.xk.interfaces.dto.rsp.auth.item;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * t_sensitive_config
 */
@Data
public class ItemRspDto {
    /**
     * 权限id
     */
    private String itemId;

    /**
     * 名称
     */
    private String itemName;

    /**
     * 英文名称
     */
    private String itemNameEn;

    /**
     * 格式：urimodule/controll/action
     */
    private String uri;

    /**
     * 描述
     */
    private String itemDesc;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 是否显示uri(1 显示 0 不显示)
     */
    private Integer showUri;

    /**
     * 在菜单里是否显示(1 显示 0 不显示）
     */
    private Integer showInMenu;

    /**
     *  排序
     */
    private Integer sort;

    /**
     * 位置
     */
    private String itemPosition;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    /**
     * 修改人id
     */
    private Long updateId;

    /**
     * 是否记录日志 0否 1是
     */
    private Integer isLog;
    /**
     * 是否验证 0否 1是
     */
    private Integer isAuth;

}