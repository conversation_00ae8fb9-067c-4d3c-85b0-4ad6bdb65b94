package com.xk.interfaces.dto.req.object;

import com.myco.mydata.domain.model.action.session.AbstractSession;
import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsObjectReqDto extends AbstractSession {

    private Long goodsId;

    @Builder
    public GoodsObjectReqDto(String sessionId, Long goodsId) {
        super(sessionId);
        this.goodsId = goodsId;
    }
}
