package com.xk.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: killer
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ConversationDefaultIdReqDto extends RequireSessionDto {

    /**
     * 会话类型
     */
    private Integer conversationType;
    /**
     * 会话业务类型
     */
    private String conversationBusinessType;
    /**
     * 会话业务id
     */
    private String businessId;

}
