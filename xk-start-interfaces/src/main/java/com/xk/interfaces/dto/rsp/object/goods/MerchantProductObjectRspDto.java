package com.xk.interfaces.dto.rsp.object.goods;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantProductObjectRspDto {
    /**
     * 商家商品类型（原盒，搓卡密等）
     */
    private String productType;

    /**
     * 收藏卡id
     */
    private Long collectibleCardId;

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 收藏卡名称
     */
    private String collectibleCardName;

    /**
     * 卡密数量
     */
    private Long serialItemCount;

    /**
     * 赠品系列
     */
    private String giftSeriesConfig;

    /**
     * 赠品规格
     */
    private String giftSpecification;

    /**
     * 赠品总数
     */
    private Long giftTotal;

    /**
     * 开售时间
     */
    private Date soldTime;

    /**
     * 赠品方式
     */
    private String giftWay;

    /**
     * 随机方式
     */
    private String randomWay;

    /**
     * 活动名称集合（前5个）
     */
    private List<String> promotionNameList;

    /**
     * 商户id
     */
    private Long corpId;

    /**
     * 首购优惠状态
     */
    private Integer firstBuyDiscountStatus;

    /**
     * 首购优惠金额
     */
    private Long firstBuyDiscountAmount;

    /**
     * 需要关注才可购买
     */
    private Integer followBuyStatus;

    /**
     * 满减配置状态
     */
    private Integer discountStatus;

    /**
     * 附加搜索词状态
     */
    private Integer addKeywordStatus;

    /**
     * 活动配置状态
     */
    private Integer promotionStatus;

    /**
     * 榜单状态
     */
    private Integer rankingStatus;

    /**
     * 赠品配置状态
     */
    private Integer giftStatus;

    /**
     * 是否开启限购
     */
    private Integer limitStatus;

    /**
     * 限购数量
     */
    private Integer limitAmount;

    /**
     * 是否开启定时限购
     */
    private Integer limitTimeStatus;

    /**
     * 限购1个间隔时长(小时)
     */
    private Integer limitTimeInterval;

    /**
     * 收藏卡单价
     */
    private Long collectiveCardUnitPrice;

    /**
     * 随机模式 1-选队/随机球员 2-选队/随机卡种 10-非选队/随机卡种(不带编) 11-非选队/随机卡种(带编) 12-非选队/随机球队 13-非选队/随机球员
     */
    private Integer randomType;

    /**
     * 剩余随机状态
     */
    private Integer remainRandomStatus;

    /**
     * 剩余随机金额
     */
    private Long remainRandomAmount;

    /**
     * 剩余随机分发id
     */
    private Long remainRandomDistributionId;

    /**
     * 商品满减列表
     */
    private List<GoodsDiscountObjectRspDto> goodsDiscountList;

    /**
     * 商品榜单列表
     */
    private List<GoodsRankingObjectRspDto> goodsRankingList;
}
