package com.xk.interfaces.dto.req.user;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import jakarta.validation.constraints.NotNull;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserIdRequireReqDto extends RequireSessionDto {
    /**
     * 用户id
     */
    @NotNull
    private Long userId;

    @Builder
    public UserIdRequireReqDto(String sessionId, Long userId) {
        super(sessionId);
        this.userId = userId;
    }
}
