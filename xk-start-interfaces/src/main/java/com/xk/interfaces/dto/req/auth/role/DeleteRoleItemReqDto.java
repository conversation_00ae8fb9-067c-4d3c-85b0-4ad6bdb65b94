package com.xk.interfaces.dto.req.auth.role;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeleteRoleItemReqDto extends RequireSessionDto {

    /**
     * 角色id
     */
    @NotNull
    private Long roleId;
    /**
     * 权限id
     */
    @NotEmpty
    private String itemId;

}