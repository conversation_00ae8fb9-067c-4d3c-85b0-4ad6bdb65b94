package com.xk.interfaces.dto.cate;

import com.myco.mydata.domain.model.CompositeIdentifier;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class CategoryDto extends CateIdentifierDto{

    private String cateName;

    private Long parentId;

    private Integer sort;

    private Integer status;

    private Set<CompositeIdentifier> compositeIdentifiers;

}
