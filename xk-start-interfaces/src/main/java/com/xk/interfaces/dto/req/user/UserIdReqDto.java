package com.xk.interfaces.dto.req.user;

import com.myco.mydata.domain.model.action.session.AbstractSession;

import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserIdReqDto extends AbstractSession {

    /**
     * 用户id
     */
    @NotNull
    private Long userId;

    @Builder
    public UserIdReqDto(String sessionId, Long userId) {
        super(sessionId);
        this.userId = userId;
    }

}
