package com.xk.interfaces.dto.req.auth.menu;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveMenuReqDto extends RequireSessionDto {

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 菜单名
     */
    @NotBlank
    private String menuName;

    /**
     * 菜单英文名
     */
    private String menuNameEn;

    /**
     * 业务类型
     */
    @NotNull
    private Integer businessType;

    /**
     * 平台类型
     */
    @NotNull
    private Integer platformType;

    /**
     * 排序
     */
    @NotNull
    private Integer sort;

    /**
     * 菜单等级
     */
    @NotNull
    private Integer menuLevel;

    /**
     * 菜单url
     */
    private String menuUrl;

    /**
     * 菜单约定显示位置
     */
    private String menuPosition;

    /**
     * 菜单图标名称（根据图标名称加载本地图片文件）
     */
    private String iconName;


    /**
     * 菜单描述
     */
    private String menuDesc;

}