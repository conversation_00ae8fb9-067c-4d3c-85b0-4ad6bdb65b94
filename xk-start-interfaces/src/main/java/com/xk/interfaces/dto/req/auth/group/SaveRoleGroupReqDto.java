package com.xk.interfaces.dto.req.auth.group;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveRoleGroupReqDto extends RequireSessionDto {

    /**
     * 分组id
     */
    @NotNull
    private Long groupId;
    /**
     * 角色id集合
     */
    @NotEmpty
    private Set<Long> roleIds;

}