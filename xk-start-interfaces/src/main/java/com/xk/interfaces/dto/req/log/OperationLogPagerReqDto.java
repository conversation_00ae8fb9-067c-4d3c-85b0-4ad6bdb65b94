package com.xk.interfaces.dto.req.log;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * t_sensitive_config
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OperationLogPagerReqDto extends RequireSessionDtoPager {

    /**
     * 日志id
     */
    private Long logId;

    /**
     * 权限名称
     */
    private String itemName;


    /**
     * 参数
     */
    private String params;

    /**
     * 地址
     */
    private String url;

    /**
     * 用户id
     */
    private Long userId;


    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 业务id
     */
    private Long busiId;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
}