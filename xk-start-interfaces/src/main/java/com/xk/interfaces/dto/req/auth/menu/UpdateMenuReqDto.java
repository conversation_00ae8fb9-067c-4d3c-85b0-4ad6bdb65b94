package com.xk.interfaces.dto.req.auth.menu;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * t_sensitive_config
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateMenuReqDto extends MenuIdReqDto {


    /**
     * 菜单名
     */
    @NotBlank
    private String menuName;

    /**
     * 菜单英文名
     */
    private String menuNameEn;


    /**
     * 排序
     */
    @NotNull
    private Integer sort;

    /**
     * 菜单等级
     */
    @NotNull
    private Integer menuLevel;

    /**
     * 菜单url
     */
    private String menuUrl;

    /**
     * 菜单约定显示位置
     */
    private String menuPosition;

    /**
     * 菜单图标名称（根据图标名称加载本地图片文件）
     */
    private String iconName;


    /**
     * 菜单描述
     */
    private String menuDesc;

}