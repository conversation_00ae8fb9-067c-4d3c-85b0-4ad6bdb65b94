package com.xk.interfaces.dto.req.auth.userrole;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateUserRoleReqDto extends RequireSessionDto {

    /**
     * 用户id
     */
    @NotNull
    private Long userId;
    /**
     * 角色id
     */
    private Long roleId = 0L;
    /**
     * 分组id
     */
    private Long groupId = 0L;
}