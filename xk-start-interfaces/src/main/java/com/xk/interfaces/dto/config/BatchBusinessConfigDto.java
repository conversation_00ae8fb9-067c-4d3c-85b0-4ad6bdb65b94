package com.xk.interfaces.dto.config;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * 申请
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchBusinessConfigDto extends RequireSessionDto {
    /**
     * 配置列表
     */
    @NotEmpty
    private List<BusinessConfigDto> BusinessConfigDtos;

}
