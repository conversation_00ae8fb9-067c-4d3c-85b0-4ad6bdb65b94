package com.xk.interfaces.dto.req.sensitive;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * t_sensitive_config
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SensitiveConfigIdsReqDto extends RequireSessionDto {
    /**
     * 敏感词ID自增长集合
     */
    @NotNull
    private Set<Long> sensitiveIds;

}