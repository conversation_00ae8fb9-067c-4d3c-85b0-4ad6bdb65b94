package com.xk.interfaces.dto.rsp.object.corp;

import com.myco.mydata.domain.enums.object.CorpCountType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: killer
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class  CorpObjectRspDto {

    /**
     * 公司信息
     */
    private CorpInfoObjectRspDto corpInfo;

    /**
     * 业务配置
     */
    private BusinessConfigRspDto businessConfig;

    /**
     * 公司统计
     */
    private Map<CorpCountType, Integer> corpCountMap;
}
