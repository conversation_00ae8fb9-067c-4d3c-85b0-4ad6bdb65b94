package com.xk.interfaces.dto.req.auth.menu;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveMenuItemReqDto extends RequireSessionDto {

    /**
     * 菜单ID
     */
    @NotNull
    private Long menuId;

    /**
     * 权限id集合
     */
    @NotEmpty
    private Set<String> itemIds;
}