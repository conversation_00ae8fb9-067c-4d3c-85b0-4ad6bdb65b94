package com.xk.interfaces.dto.config;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * 申请
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateBusinessConfigDto extends RequireSessionDto {
    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 分组
     */
    private String groupType;

    private List<KeyDto> keys;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class KeyDto {
        /**
         * key
         */
        private String key;
        /**
         * 值
         */
        private String val;
    }

}
