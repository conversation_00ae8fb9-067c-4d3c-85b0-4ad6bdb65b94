package com.xk.interfaces.dto.req.auth.group;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveGroupReqDto extends RequireSessionDto {

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 分组名称
     */
    @NotBlank
    private String name;

    /**
     * 分组英文名称
     */
    private String nameEn;

    /**
     * 显示状态；1显示 0不显示
     */
    @NotNull
    private Integer isEnabled;

    /**
     * 排序
     */
    @NotNull
    private Integer sort;

    /**
     * 业务类型
     */
    @NotNull
    private Integer businessType;

    /**
     * 平台类型
     */
    @NotNull
    private Integer platformType;


    /**
     * 描述
     */
    private String description;

    /**
     * 分组类型（1:角色，2：功能）
     */
    @NotNull
    private Integer groupType;

}