package com.xk.interfaces.dto.rsp.object.goods;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpecificationObjectRspDto {

    /**
     * 规格id
     */
    private Long specificationId;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 规格单位
     */
    private String unitType;

    /**
     * 规格价格
     */
    private Long amount;

    /**
     * 货币类型
     */
    private Integer currencyType;

    /**
     * 库存id
     */
    private Long stockId;

    /**
     * 价格id
     */
    private Long priceId;

    /**
     * 分发id
     */
    private Long distributionId;
}
