package com.xk.interfaces.dto.req.object;

import com.myco.mydata.domain.model.action.session.AbstractSession;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class UserObjectReqDto extends AbstractSession {
	
	@NotNull
	@Min(1)
	private Long userId;


	@Builder
	public UserObjectReqDto(String sessionId, Long userId) {
		super(sessionId);
		this.userId = userId;
	}
}