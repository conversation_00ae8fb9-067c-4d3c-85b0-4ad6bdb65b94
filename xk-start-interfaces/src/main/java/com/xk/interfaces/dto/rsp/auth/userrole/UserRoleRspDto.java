package com.xk.interfaces.dto.rsp.auth.userrole;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * t_sensitive_config
 */
@Data
public class UserRoleRspDto {
    /**
     * 用户授权id
     */
    private Long userRoleId;

    /**
     * 用户UID
     */
    private Long userId;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 描述
     */
    private String authorityDesc;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    /**
     * 修改人id
     */
    private Long updateId;

    /**
     * 1:正常；2：停用：3：删除
     */
    private Integer status;

    private String roleName;

    private String groupName;
}