package com.xk.interfaces.dto.rsp.object.corp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorpInfoObjectRspDto {

    private Long corpId;
    /**
     * 公司名称
     */
    private String corpName;
    /**
     * 商家头像
     */
    private String corpLogo;

    /**
     * 商户状态（1-正常 0-禁用）
     */
    private Integer corpStatus;

    /**
     * 商户暂压状态（1-正常 0-禁用）
     */
    private Integer delayStatus;

    /**
     * 商户上架状态 （1-正常 0-禁用）
     */
    private Integer launchStatus;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 微信id
     */
    private String wechatId;

    /**
     * 回款周期
     */
    private Integer paymentCycle;

    /**
     * 管理员用户ID
     */
    private Long adminUserId;
    /**
     * 公司简介
     */
    private String corpIntro;
}
