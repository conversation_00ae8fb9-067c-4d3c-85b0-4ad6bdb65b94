package com.xk.interfaces.dto.req.auth.menu;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MenuQueryReqDto extends RequireSessionDto {

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 状态
     */
    private Integer status;

}