package com.xk.interfaces.dto.rsp.object.corp;

import com.xk.enums.common.CommonStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessConfigRspDto {

    /**
     * 卡背图 URL
     */
    private String cardBack;

    private String logo;

    /**
     * 发货二维码 URL
     */
    private String deliverQrCode;

    /**
     * 支付偏好
     */
    private Integer payTypeLike;

    /**
     * 下架机台权限
     */
    private CommonStatusEnum removeRole;

    /**
     * 是否可创建边锋盒子
     */
    private CommonStatusEnum createRole;

    /**
     * 优惠券支付权限
     */
    private CommonStatusEnum couponRole;

    /**
     * 卡商踢单功能权限
     */
    private CommonStatusEnum cancelOrderRole;

    /**
     * 卡密奖励配置权限
     */
    private CommonStatusEnum rewardRole;
}
