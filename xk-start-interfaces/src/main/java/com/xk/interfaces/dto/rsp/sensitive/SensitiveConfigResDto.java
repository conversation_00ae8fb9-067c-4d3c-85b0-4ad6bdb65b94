package com.xk.interfaces.dto.rsp.sensitive;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * t_sensitive_config
 */
@Data

public class SensitiveConfigResDto {
    /**
     * 敏感词ID自增长
     */
    private Long sensitiveId;

    /**
     * 敏感词
     */
    private String sensitiveName;

    /**
     * 敏感等级:警告级WARNING(后台记录，替换类型为空)、屏蔽级MASK(替换内容)、禁发级FORBBIDEN(不允许提交，替换类型强制为ALL)
     */
    private String sensitiveLevel;

    /**
     * 替换类型:  部分替换、全部替换
     */
    private String replaceType;

    /**
     * 替换用词
     */
    private String replaceWord;

    /**
     * 敏感词用途(发言、昵称、文本 多个用,号分隔)
     */
    private String sensitiveUse;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 姓名
     */
    private String createName;

}