package com.xk.interfaces.dto.rsp.object.goods;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsRankingObjectRspDto {

    /**
     * 榜单排名
     */
    private Integer rewardRank;

    /**
     * 奖励类型
     */
    private Integer rewardType;

    /**
     * 奖励物品
     */
    private String reward;
}
