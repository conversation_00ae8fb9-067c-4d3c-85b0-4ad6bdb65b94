package com.xk.interfaces.dto.rsp.object.goods;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoodsResObjectRspDto {
    /**
     * 资源id
     */
    private Integer resId;
    /**
     * 资源类型
     */
    private String resType;
    /**
     * 资源地址
     */
    private String resAddr;
    /**
     * 排序
     */
    private String sort;
}
