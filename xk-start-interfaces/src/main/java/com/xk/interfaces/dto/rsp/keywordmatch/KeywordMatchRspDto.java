package com.xk.interfaces.dto.rsp.keywordmatch;

import lombok.Data;

import java.util.Date;

/**
 * t_sensitive_config
 */
@Data
public class KeywordMatchRspDto {
    /**
     * 业务id
     */
    private String busiId;

    /**
     * 业务类型
     */
    private Integer busiType;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private Long updateId;

}