package com.xk.interfaces.dto.rsp.log;

import lombok.Data;

import java.util.Date;

/**
 * t_sensitive_config
 */
@Data

public class OperationLogRspDto {

    /**
     * 日志id
     */
    private Long logId;

    /**
     * 权限名称
     */
    private String itemName;

    /**
     * 操作描述
     */
    private String operationDesc;

    /**
     * 参数
     */
    private String params;

    /**
     * 地址
     */
    private String url;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 业务id
     */
    private Long busiId;

}