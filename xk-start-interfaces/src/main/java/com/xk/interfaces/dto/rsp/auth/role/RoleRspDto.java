package com.xk.interfaces.dto.rsp.auth.role;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * t_sensitive_config
 */
@Data
public class RoleRspDto {
    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色英文名称
     */
    private String roleNameEn;

    /**
     * 描述
     */
    private String roleDesc;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 排序
     */
    private Integer sort;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    private Long createId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    private Long updateId;

    /**
     * 1:正常；2：停用：3：删除
     */
    private Integer status;
    /**
     * 分组名
     */
    private String groupName;
}