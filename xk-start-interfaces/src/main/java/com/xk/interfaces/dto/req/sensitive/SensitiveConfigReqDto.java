package com.xk.interfaces.dto.req.sensitive;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * t_sensitive_config
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SensitiveConfigReqDto extends RequireSessionDtoPager {


    /**
     * 敏感词
     */
    private String sensitiveName;

    /**
     * 敏感等级:警告级WARNING(后台记录，替换类型为空)、屏蔽级MASK(替换内容)、禁发级FORBBIDEN(不允许提交，替换类型强制为ALL)
     */
    private String sensitiveLevel;

    /**
     * 替换类型:  部分替换、全部替换
     */
    private String replaceType;

    /**
     * 替换用词
     */
    private String replaceWord;

    /**
     * 敏感词用途(发言、昵称、文本 多个用,号分隔)
     */
    private String sensitiveUse;

    /**
     * 状态
     */
    private String status;

}