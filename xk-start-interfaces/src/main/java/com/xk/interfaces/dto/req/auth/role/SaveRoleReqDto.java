package com.xk.interfaces.dto.req.auth.role;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveRoleReqDto extends RequireSessionDto {

    /**
     * 角色名称
     */
    @Size(min = 1, max = 8)
    @NotBlank
    private String roleName;

    /**
     * 角色英文名称
     */
    @NotBlank
    private String roleNameEn;

    /**
     * 描述
     */
    private String roleDesc;

    /**
     * 业务类型
     */
    @NotNull
    private Integer businessType;

    /**
     * 平台类型
     */
    @NotNull
    private Integer platformType;

    /**
     * 1:正常；2：停用
     */
    @NotNull
    private Integer status;

    /**
     * 排序
     */
    @Min(0)
    @NotNull
    private Integer sort;
}