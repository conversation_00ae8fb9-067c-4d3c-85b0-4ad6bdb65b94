package com.xk.interfaces.dto.cate;

import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.*;

import java.util.Set;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateCateDto extends RequireSessionDto {

    private String cateName;

    private Integer sort;

    private Integer status;

    private Long parentId;

    private Set<CompositeIdentifier> compositeIdentifiers;

}
