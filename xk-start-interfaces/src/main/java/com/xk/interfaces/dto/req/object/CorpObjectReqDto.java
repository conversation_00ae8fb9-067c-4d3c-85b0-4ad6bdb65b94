package com.xk.interfaces.dto.req.object;

import com.myco.mydata.domain.model.action.session.AbstractSession;
import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CorpObjectReqDto extends AbstractSession {

    private Long corpId;

    @Builder
    public CorpObjectReqDto(String sessionId, Long corpId) {
        super(sessionId);
        this.corpId = corpId;
    }

}
