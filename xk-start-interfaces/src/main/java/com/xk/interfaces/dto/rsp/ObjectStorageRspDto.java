package com.xk.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ObjectStorageRspDto {
    /**
     * 上传id
     */
    private Long id;
    /**
     * 临时安全证书 Id
     */
    private String tmpSecretId;
    /**
     * 临时安全证书 Key
     */
    private String tmpSecretKey;
    /**
     * token 值
     */
    private String sessionToken;
    /**
     * 证书无效的时间
     */
    private long expiredTime;
    /**
     * 桶
     */
    private String bucket;
    /**
     * 区域
     */
    private String region;
    /**
     * 存储服务器地址
     */
    private String domain;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 云空间文件名
     */
    private String fileName;
    /**
     * token md5
     */
    private String md5;
}
