package com.xk.interfaces.dto.req.auth.menu;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MenuItemReqDto extends RequireSessionDto {

    /**
     * 菜单ID
     */
    @NotNull
    private Long menuId;

    /**
     * 权限id
     */
    @NotNull
    private String itemId;
}