package com.xk.interfaces.dto.rsp.object.goods;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoodsInfoObjectRspDto {
    /**
     * 商品id
     */
    private Long goodsId;
    /**
     * 所属业务板块
     */
    private String busiType;
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型
     */
    private String goodsType;

    /**
     * 价格id
     */
    private Long priceId;

    /**
     * 商品单价
     */
    private Long unitPrice;

    /**
     * 规格id
     */
    private Long specificationId;

    /**
     * 商品规格
     */
    private String specificationName;

    /**
     * 计划下架时间
     */
    private Date planDownTime;

    /**
     * 实际下架时间
     */
    private Date actualDownTime;

    /**
     * 描述
     */
    private String goodsDescribe;

    /**
     * 库存id
     */
    private Long stockId;

    /**
     * 商品库存数
     */
    private Long stock;

    /**
     * 剩余库存数
     */
    private Long availableStock;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 最高价
     */
    private Long highestPrice;

    /**
     * 最低价
     */
    private Long lowestPrice;

    /**
     * 商家商品
     */
    private MerchantProductObjectRspDto merchantProduct;
}
