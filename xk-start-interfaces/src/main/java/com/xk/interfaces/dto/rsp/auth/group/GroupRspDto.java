package com.xk.interfaces.dto.rsp.auth.group;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * t_sensitive_config
 */
@Data
public class GroupRspDto {
    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 分组英文名称
     */
    private String nameEn;

    /**
     * 显示状态；1显示 0不显示
     */
    private Integer isEnabled;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 1:正常；2：停用：3：删除
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 分组类型（1:角色，2：功能）
     */
    private Integer groupType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    /**
     * 修改人id
     */
    private Long updateId;

}