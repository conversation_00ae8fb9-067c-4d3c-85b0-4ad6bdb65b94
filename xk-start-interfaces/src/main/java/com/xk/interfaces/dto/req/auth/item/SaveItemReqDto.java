package com.xk.interfaces.dto.req.auth.item;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class SaveItemReqDto extends RequireSessionDto {

    /**
     * 名称
     */
    @NotBlank
    private String itemName;

    /**
     * 英文名称
     */
    private String itemNameEn;

    /**
     * 格式：urimodule/controll/action
     */
    @NotBlank
    private String uri;

    /**
     * 描述
     */
    private String itemDesc;

    /**
     * 接口所属服务
     */
    @NotNull
    private String serverName;

    /**
     * 业务类型
     */
    @NotNull
    private Integer businessType;

    /**
     * 平台类型
     */
    @NotNull
    private Integer platformType;

    /**
     * 是否显示uri(1 显示 0 不显示)
     */
    @NotNull
    private Integer showUri;

    /**
     * 在菜单里是否显示(1 显示 0 不显示）
     */
    @NotNull
    private Integer showInMenu;

    /**
     * 排序
     */
    @NotNull
    private Integer sort;

    /**
     * 位置
     */
    private String itemPosition;

    /**
     * 是否记录日志 0否 1是
     */
    @NotNull
    private Integer isLog;
    /**
     * 是否验证 0否 1是
     */
    @NotNull
    private Integer isAuth;

}