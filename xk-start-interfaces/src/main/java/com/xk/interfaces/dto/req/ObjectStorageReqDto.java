package com.xk.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class ObjectStorageReqDto extends RequireSessionDto {
    /**
     * 业务类型
     */
    @NotBlank
    private String objectStorageBusinessType;

    /**
     * 文件名
     */
    private String filename;

}
