package com.xk.interfaces.dto.rsp.sensitive;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * t_sensitive_config
 */
@Data

public class SensitiveRecordResDto {
    /**
     * 记录SN自增长
     */
    private Long sensitiveSn;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 来源业务流水号
     */
    private Long doneCode;

    /**
     * 来源业务代码
     */
    private Integer busiCode;

    /**
     * 来源用户
     */
    private Long userId;

    /**
     * 来源节目发言没来源业务流水号 要填来源节目
     */
    private Long programId;

    /**
     * 敏感内容
     */
    private String content;

    /**
     * 敏感等级取最大敏感等级
     */
    private String sensitiveLevel;

    /**
     * 状态未处理、已处理
     */
    private String status;

    /**
     * 敏感配置表id
     */
    private Long sensitiveId;

}