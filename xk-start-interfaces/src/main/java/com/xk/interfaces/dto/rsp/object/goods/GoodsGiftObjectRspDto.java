package com.xk.interfaces.dto.rsp.object.goods;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoodsGiftObjectRspDto {
    /**
     * 赠品ID
     */
    private String giftId;
    /**
     * 赠品图片地址
     */
    private String giftPic;

    /**
     * 赠品排序
     */
    private Integer giftSort;
    /**
     * 概率
     */
    private Integer probability;
}
