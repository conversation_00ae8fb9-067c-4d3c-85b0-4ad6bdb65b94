package com.xk.interfaces.dto.req.auth.group;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GroupQueryReqDto extends RequireSessionDto {


    /**
     * 显示状态；1显示 0不显示
     */
    private Integer isEnabled;


    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;



    /**
     * 分组类型（1:角色，2：功能）
     */
    private Integer groupType;
    /**
     * 状态
     */
    private Integer status;
}