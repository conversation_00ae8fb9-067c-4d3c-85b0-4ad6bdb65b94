package com.xk.interfaces.dto.req.auth.menu;

import java.util.Set;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MenuRoleReqDto extends RequireSessionDto {

    /**
     * 角色id
     */
    @NotNull(message = "角色id不能为空")
    private Long roleId;

    /**
     * 菜单ID
     */
    private Set<Long> menuIds;
}