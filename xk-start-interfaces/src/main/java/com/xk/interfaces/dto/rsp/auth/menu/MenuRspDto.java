package com.xk.interfaces.dto.rsp.auth.menu;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * t_sensitive_config
 */
@Data
public class MenuRspDto {
    /**
     * 菜单ID
     */
    private Long menuId;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 菜单名
     */
    private String menuName;

    /**
     * 菜单英文名
     */
    private String menuNameEn;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 菜单等级
     */
    private Integer menuLevel;

    /**
     * 菜单url
     */
    private String menuUrl;

    /**
     * 菜单约定显示位置
     */
    private String menuPosition;

    /**
     * 菜单图标名称（根据图标名称加载本地图片文件）
     */
    private String iconName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    /**
     * 修改人id
     */
    private Long updateId;

    /**
     * 菜单描述
     */
    private String menuDesc;

    private Integer status;


    private List<MenuRspDto> children;
}