package com.xk.interfaces.dto.req.auth.group;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RoleGroupReqDto extends RequireSessionDto {

    /**
     * 分组id
     */
    @NotNull
    private Long groupId;
    /**
     * 角色id
     */
    @NotNull
    private Long roleId;

}