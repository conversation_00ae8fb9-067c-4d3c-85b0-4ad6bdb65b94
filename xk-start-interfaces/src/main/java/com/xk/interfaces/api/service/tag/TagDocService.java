package com.xk.interfaces.api.service.tag;

import com.xk.interfaces.dto.tag.TagIdentifierDto;
import com.xk.interfaces.dto.tag.TagInfoDto;
import com.xk.interfaces.service.tag.TagService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

/**
 * 标签文档服务
 * <AUTHOR>
 */
@Controller
@RequestMapping("/tag")
public interface TagDocService extends TagService {

    /**
     * 保存标签信息
     *
     * @param dto 包含标签信息的 DTO，封装在一个 Mono 中。
     * @return 一个表示保存操作的 Mono<Void>。
     */
    @PostMapping("/save")
    @Override
    Mono<Void> save(@RequestBody Mono<TagInfoDto> dto);

    /**
     * 更新标签信息
     *
     * @param dto 包含标签信息的 DTO，封装在一个 Mono 中。
     * @return 一个表示更新操作的 Mono<Void>。
     */
    @PostMapping("/update")
    @Override
    Mono<Void> update(@RequestBody Mono<TagInfoDto> dto);

    /**
     * 删除标签信息
     *
     * @param dto 包含标签标识符的 DTO，封装在一个 Mono 中。
     * @return 一个表示删除操作的 Mono<Void>。
     */
    @PostMapping("/remove")
    @Override
    Mono<Void> remove(@RequestBody Mono<TagIdentifierDto> dto);
}

