package com.xk.interfaces.api.query.auth.role;

import java.util.List;

import com.xk.interfaces.dto.req.auth.item.ItemQueryReqDto;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.interfaces.dto.req.auth.role.RoleByGroupIdReqDto;
import com.xk.interfaces.dto.req.auth.role.RoleIdReqDto;
import com.xk.interfaces.dto.req.auth.role.RolePagerReqDto;
import com.xk.interfaces.dto.rsp.auth.item.ItemRspDto;
import com.xk.interfaces.dto.rsp.auth.role.RoleRspDto;
import com.xk.interfaces.dto.tree.TreeNodeRspDto;
import com.xk.interfaces.query.auth.role.RoleQueryService;

import reactor.core.publisher.Mono;

/**
 * 角色查询
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/auth/role/query")
public interface RoleQueryDocService extends RoleQueryService {

    /**
     * 查询角色分页列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/searchRolePager")
    Mono<Pagination> searchRolePager(@RequestBody Mono<RolePagerReqDto> dto);

    /**
     * 根据id查询角色
     *
     * @param dto
     * @return
     */
    @PostMapping("/getById")
    Mono<RoleRspDto> getById(@RequestBody Mono<RoleIdReqDto> dto);

    /**
     * 根据分组id查询角色列表(包括子分组的)
     *
     * @param dto
     * @return
     */
    @Override
    @PostMapping("/searchAllRoleByGroupId")
    Mono<List<RoleRspDto>> searchAllRoleByGroupId(@RequestBody Mono<RoleByGroupIdReqDto> dto);

    /**
     * 根据角色id查询权限列表
     *
     * @param dto
     * @return
     */
    @Override
    @PostMapping("/searchItemByRoleId")
    Mono<List<ItemRspDto>> searchItemByRoleId(@RequestBody Mono<ItemQueryReqDto> dto);

    /**
     * 根据id查询菜单树
     *
     * @param dto dto
     * @return 菜单树
     */
    @Override
    @PostMapping("/searchMenuTreeById")
    Mono<List<TreeNodeRspDto>> searchMenuTreeById(@RequestBody Mono<RoleIdReqDto> dto);
}
