package com.xk.interfaces.api.query.auth.group;

import com.xk.interfaces.dto.req.auth.group.GroupIdReqDto;
import com.xk.interfaces.dto.req.auth.group.GroupQueryReqDto;
import com.xk.interfaces.dto.rsp.auth.group.GroupRspDto;
import com.xk.interfaces.dto.tree.TreeNodeRspDto;
import com.xk.interfaces.query.auth.group.GroupQueryService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 分组查询
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/auth/group/query")
public interface GroupQueryDocService extends GroupQueryService {

    /**
     * 查询分组树
     *
     * @param dto
     * @return
     */
    @PostMapping("/searchGroupTree")
    Mono<List<TreeNodeRspDto>> searchGroupTree(@RequestBody Mono<GroupQueryReqDto> dto);

    /**
     * 查询分组列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/searchGroupList")
    Mono<List<GroupRspDto>> searchGroupList(@RequestBody Mono<GroupQueryReqDto> dto);

    /**
     * 根据id查询分组
     *
     * @param dto
     * @return
     */
    @PostMapping("/getById")
    Mono<GroupRspDto> getById(@RequestBody Mono<GroupIdReqDto> dto);
}
