package com.xk.interfaces.api;

import com.xk.interfaces.dto.req.ObjectStorageReqDto;
import com.xk.interfaces.dto.req.ObjectStorageUploadConfirmReqDto;
import com.xk.interfaces.dto.rsp.ObjectStorageRspDto;
import com.xk.interfaces.service.os.ObjectStorageService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

/**
 * 对象存储服务
 * @author: killer
 **/
@Controller
@RequestMapping("/os")
public interface ObjectStorageService2 extends ObjectStorageService {

    /**
     * 获取token
     * @param objectStorageRootMono objectStorageRootMono
     * @return Mono<ObjectStorageRspDto>
     */
    @PostMapping("/applyToken")
    @Override
    Mono<ObjectStorageRspDto> applyToken(@RequestBody Mono<ObjectStorageReqDto> objectStorageRootMono);

    /**
     * 上传确认
     * @param objectStorageUploadConfirmReqDtoMono objectStorageUploadConfirmReqDtoMono
     * @return Mono<Void>
     */
    @PostMapping("/uploadConfirm")
    @Override
    Mono<Void> updateUpload(@RequestBody Mono<ObjectStorageUploadConfirmReqDto> objectStorageUploadConfirmReqDtoMono);

}
