package com.xk.interfaces.api.query.auth.userrole;

import java.util.List;

import com.xk.interfaces.dto.req.auth.userrole.MenuBySessionReqDto;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.xk.interfaces.dto.req.auth.userrole.UserRoleReqDto;
import com.xk.interfaces.dto.rsp.auth.menu.MenuRspDto;
import com.xk.interfaces.dto.rsp.auth.userrole.UserRoleRspDto;
import com.xk.interfaces.query.auth.userrole.UserRoleQueryService;

import reactor.core.publisher.Mono;

/**
 * 用户角色查询
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/auth/user/role/query")
public interface UserRoleQueryDocService extends UserRoleQueryService {

    /**
     * 根据id查询角色
     *
     * @param dto
     * @return
     */
    @PostMapping("/getByUserId")
    Mono<UserRoleRspDto> getByUserId(@RequestBody Mono<UserRoleReqDto> dto);

    /**
     * 根据会话查询所有菜单
     *
     * @param dto
     * @return
     */
    @Override
    @PostMapping("/getMenusBySession")
    Mono<List<MenuRspDto>> getMenusBySession(@RequestBody Mono<MenuBySessionReqDto> dto);
}
