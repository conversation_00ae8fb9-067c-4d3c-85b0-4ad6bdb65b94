package com.xk.interfaces.api.query.auth.menu;

import com.xk.interfaces.dto.req.auth.menu.MenuIdReqDto;
import com.xk.interfaces.dto.req.auth.menu.MenuQueryReqDto;
import com.xk.interfaces.dto.rsp.auth.menu.MenuRspDto;
import com.xk.interfaces.dto.tree.TreeNodeRspDto;
import com.xk.interfaces.query.auth.menu.MenuQueryService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 菜单查询
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/auth/menu/query")
public interface MenuQueryDocService extends MenuQueryService {

    /**
     * 查询菜单树
     *
     * @param dto
     * @return
     */
    @PostMapping("/searchMenuTree")
    Mono<List<TreeNodeRspDto>> searchMenuTree(@RequestBody Mono<MenuQueryReqDto> dto);

    /**
     * 根据id查询菜单
     *
     * @param dto
     * @return
     */
    @PostMapping("/getById")
    Mono<MenuRspDto> getById(@RequestBody Mono<MenuIdReqDto> dto);
}
