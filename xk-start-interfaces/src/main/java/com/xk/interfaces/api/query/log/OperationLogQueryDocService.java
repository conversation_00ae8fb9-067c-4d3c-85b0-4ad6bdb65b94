package com.xk.interfaces.api.query.log;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.interfaces.dto.req.log.OperationLogPagerReqDto;
import com.xk.interfaces.query.log.OperationLogQueryService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

/**
 * 操作日志查询
 */
@Controller
@RequestMapping("/operation/log/query")
public interface OperationLogQueryDocService extends OperationLogQueryService {

    /**
     * 操作日志分页列表
     *
     * @param dtoMono
     * @return
     */
    @PostMapping("/searchPager")
    Mono<Pagination> searchPager(@RequestBody Mono<OperationLogPagerReqDto> dtoMono);
}
