package com.xk.interfaces.api.service.sensitive;

import com.xk.interfaces.dto.config.ImportReqDto;
import com.xk.interfaces.dto.req.sensitive.SensitiveConfigEditReqDto;
import com.xk.interfaces.dto.req.sensitive.SensitiveConfigIdsReqDto;
import com.xk.interfaces.service.sensitive.SensitiveService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

/**
 * 敏感词操作
 */
@Controller
@RequestMapping("/config/sensitive")
public interface SensitiveDocService extends SensitiveService {

    /**
     * 添加敏感词
     * @param dtoMono
     * @return
     */
    @Override
    @PostMapping("/saveSensitiveConfig")
    Mono<Void> saveSensitiveConfig(@RequestBody Mono<SensitiveConfigEditReqDto> dtoMono);

    /**
     * 修改敏感词
     * @param dtoMono
     * @return
     */
    @Override
    @PostMapping("/updateSensitiveConfig")
    Mono<Void> updateSensitiveConfig(@RequestBody Mono<SensitiveConfigEditReqDto> dtoMono);

    /**
     * 修改敏感词启用
     *
     * @param dtoMono
     * @return
     */
    @Override
    @PostMapping("/updateSensitiveConfigEnable")
    Mono<Void> updateSensitiveConfigEnable(@RequestBody Mono<SensitiveConfigIdsReqDto> dtoMono);

    /**
     * 修改敏感词禁用
     *
     * @param dtoMono
     * @return
     */
    @Override
    @PostMapping("/updateSensitiveConfigDisable")
    Mono<Void> updateSensitiveConfigDisable(@RequestBody Mono<SensitiveConfigIdsReqDto> dtoMono);

    /**
     * 删除敏感词
     * @param dtoMono
     * @return
     */
    @Override
    @PostMapping("/deleteSensitiveConfig")
    Mono<Void> deleteSensitiveConfig(@RequestBody Mono<SensitiveConfigIdsReqDto> dtoMono);

    /**
     * 上传文件添加
     *
     * @param dtoMono
     * @return
     */
    @Override
    @PostMapping("/uploadSensitiveConfig")
    Mono<Void> commitUploadSensitiveConfig(@RequestBody Mono<ImportReqDto> dtoMono);
}
