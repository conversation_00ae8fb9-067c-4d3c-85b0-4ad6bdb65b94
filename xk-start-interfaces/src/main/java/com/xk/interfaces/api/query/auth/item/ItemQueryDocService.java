package com.xk.interfaces.api.query.auth.item;

import java.util.List;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.interfaces.dto.req.auth.item.ItemByMenuIdReqDto;
import com.xk.interfaces.dto.req.auth.item.ItemIdReqDto;
import com.xk.interfaces.dto.req.auth.item.ItemQueryReqDto;
import com.xk.interfaces.dto.rsp.auth.item.ItemRspDto;
import com.xk.interfaces.query.auth.item.ItemQueryService;

import reactor.core.publisher.Mono;

/**
 * 权限查询
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/auth/item/query")
public interface ItemQueryDocService extends ItemQueryService {


    /**
     * 查询权限分页列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/searchItemPager")
    Mono<Pagination> searchItemPager(@RequestBody Mono<ItemQueryReqDto> dto);

    /**
     * 根据菜单id查询权限列表(包括子权限的)
     *
     * @param dto
     * @return
     */
    @PostMapping("/searchAllItemList")
    Mono<List<ItemRspDto>> searchAllItemList(@RequestBody Mono<ItemByMenuIdReqDto> dto);

    /**
     * 根据id查询权限
     *
     * @param dto
     * @return
     */
    @PostMapping("/getById")
    Mono<ItemRspDto> getById(@RequestBody Mono<ItemIdReqDto> dto);
}
