<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.corp</groupId>
        <artifactId>xk-corp</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-corp-server</artifactId>
    <name>xk-corp-server</name>
    <description>xk-corp-server</description>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.corp</groupId>
            <artifactId>xk-corp-application</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.xk.corp.server.XkCorpServer</mainClass>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
