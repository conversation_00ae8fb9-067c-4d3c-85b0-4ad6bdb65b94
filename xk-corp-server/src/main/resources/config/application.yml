debug: true
server:
  port: 11004
spring:
  application:
    name: xkCorp
  profiles:
    active: "@profileActive@"
    include: commons,data,jms,cache,http,schedule,proxy,os,server
  webflux:
    format:
      date-time: yyyy-MM-dd HH:mm:ss

application:
  validation:
    enable: false
  session:
    enable: true
  data:
    sqlmap:
      resources: classpath*:com/myco/mydata/infrastructure/data/sqlmap/**/*.xml,classpath*:com/myco/mydata/config/infrastructure/data/sqlmap/**/*.xml,classpath*:com/xk/corp/infrastructure/data/sqlmap/**/*.xml,classpath*:com/xk/infrastructure/data/sqlmap/**/*.xml
  ncs:
    zookeeper:
      enable: true
  jms:
    kafka-sender:
      enable: false
    rocketmq-sender:
      enable: true
      aclEnable: false
    rocketmq-producer:
      enable: true
      aclEnable: false
    rocketmq-jvm:
      enable: true
      aclEnable: false
    rocketmq-consumer:
      enable: true
      aclEnable: false
    zmq-producer:
      enable: true
  http:
    http:
      enable: true
  redis:
    zmqRedisClient:
      enable: true
    seqRedisClient:
      enable: true
    busiRedisClient:
      enable: true
  scheduling:
    quartz:
      enable: true
      loader: "ncs"
