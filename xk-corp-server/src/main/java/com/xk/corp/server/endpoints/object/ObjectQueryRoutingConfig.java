package com.xk.corp.server.endpoints.object;

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.corp.interfaces.query.object.CorpObjectQueryHandlerService;
import com.xk.interfaces.dto.req.object.CorpObjectReqDto;

@Configuration(proxyBeanMethods = false)
public class ObjectQueryRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> corpObjectServiceRouter(
            CorpObjectQueryHandlerService corpObjectQueryHandlerService) {
        return nest(RequestPredicates.path("/corp/object"), route()
                .POST("/getCorpObject", ACCEPT_JSON, request -> WebFluxHandler.handler(request,
                        CorpObjectReqDto.class, corpObjectQueryHandlerService::getCorpObject))
                .build());
    }
}


