package com.xk.corp.server.endpoints.apply;

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.corp.interfaces.dto.req.apply.CorpApplyPagerReqDto;
import com.xk.corp.interfaces.query.apply.CorpApplyQueryService;

@Configuration(proxyBeanMethods = false)
public class CorpApplyQueryRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> corpApplyQueryServiceRouter(
            CorpApplyQueryService service) {
        return nest(
                RequestPredicates.path("/corp/apply/query"), route()
                        .POST("/search/pager", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request,
                                        CorpApplyPagerReqDto.class, service::searchApplyPager))
                        .POST("/session", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request,
                                        RequireSessionDto.class, service::searchBySession))
                        .POST("/search/count", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request,
                                        RequireSessionDto.class, service::searchCount))
                        .build());
    }
}


