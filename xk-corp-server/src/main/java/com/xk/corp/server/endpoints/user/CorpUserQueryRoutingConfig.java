package com.xk.corp.server.endpoints.user;

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.corp.interfaces.dto.req.user.CorpUserPagerAdminReqDto;
import com.xk.corp.interfaces.dto.req.user.CorpUserPagerReqDto;
import com.xk.corp.interfaces.query.user.CorpUserQueryService;

@Configuration(proxyBeanMethods = false)
public class CorpUserQueryRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> corpUserQueryServiceRouter(CorpUserQueryService service) {
        return nest(RequestPredicates.path("/corp/user/query"), route()
                .POST("/search/pager/admin", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpUserPagerAdminReqDto.class,
                                service::searchCorpUserPagerAdmin))
                .POST("/search/show/pager", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpUserPagerReqDto.class,
                                service::searchShowPager))
                .POST("/search/pager", ACCEPT_JSON, request -> WebFluxHandler.handler(request,
                        CorpUserPagerReqDto.class, service::searchPager))
                .build());
    }

}


