package com.xk.corp.server.endpoints.user;

/**
 * @author: killer
 **/

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.corp.interfaces.dto.req.user.*;
import com.xk.corp.interfaces.service.user.CorpUserService;

@Configuration(proxyBeanMethods = false)
public class CorpUserServiceRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> corpUserServiceRouter(CorpUserService service) {
        return nest(RequestPredicates.path("/corp/user"), route()
                .POST("/create", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpUserCreateReqDto.class,
                                service::create))
                .POST("/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpUserUpdateReqDto.class,
                                service::update))
                .POST("/status/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpUserStatusReqDto.class,
                                service::updateStatus))
                .POST("/delete", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpUserRequireIdReqDto.class,
                                service::delete))
                .POST("/role/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpUserRoleUpdateReqDto.class,
                                service::updateRole))
                .POST("/admin/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpUserRequireIdReqDto.class,
                                service::updateAdmin))
                .POST("/corp/admin/validate", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpAdminValidateReqDto.class,
                                service::createAdminValidate))
                .POST("/corp/admin/update", ACCEPT_JSON, request -> WebFluxHandler.handler(request,
                        CorpUserUpdateAdminReqDto.class, service::updateAdminCorp))
                .build());
    }
}


