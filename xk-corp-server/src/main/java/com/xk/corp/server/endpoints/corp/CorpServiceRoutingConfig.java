package com.xk.corp.server.endpoints.corp;

/**
 * @author: killer
 **/

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.corp.interfaces.dto.req.corp.*;
import com.xk.corp.interfaces.service.corp.CorpService;

@Configuration(proxyBeanMethods = false)
public class CorpServiceRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> corpServiceRouter(CorpService service) {
        return nest(RequestPredicates.path("/corp"), route()
                .POST("/create", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpInfoCreateReqDto.class,
                                service::createCorpInfo))
                .POST("/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpInfoUpdateReqDto.class,
                                service::updateCorpInfo))
                .POST("/quota/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpInfoQuotaReqDto.class,
                                service::updateFreeQuota))
                .POST("/status/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpInfoStatusReqDto.class,
                                service::updateCorpInfoStatus))
                .POST("/delay/status/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpInfoDelayStatusReqDto.class,
                                service::updateCorpInfoDelayStatus))
                .POST("/launch/status/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpInfoLaunchStatusReqDto.class,
                                service::updateCorpInfoLaunchStatus))
                .POST("/block/user", ACCEPT_JSON, request -> WebFluxHandler.handler(request,
                        BlockUserReqDto.class, service::updateBlockUser))
                .build());
    }

}


