package com.xk.corp.server.endpoints.corp;

import com.myco.mydata.domain.model.action.session.AbstractSession;
import com.xk.corp.interfaces.dto.req.recommend.CorpRecommendPagerReqDto;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.corp.interfaces.dto.req.corp.CorpIdSearchReqDto;
import com.xk.corp.interfaces.dto.req.corp.CorpInfoDetailReqDto;
import com.xk.corp.interfaces.dto.req.corp.CorpInfoPagerReqDto;
import com.xk.corp.interfaces.dto.req.corp.CorpWonderfulMomentReqDto;
import com.xk.corp.interfaces.query.corp.CorpQueryService;
import com.xk.interfaces.dto.req.user.UserIdReqDto;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

@Configuration(proxyBeanMethods = false)
public class CorpQueryRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> corpQueryServiceRouter(CorpQueryService service) {
        return nest(RequestPredicates.path("/corp/query"), route()
                .POST("/search/pager", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpInfoPagerReqDto.class,
                                service::searchCorpPager))
                .POST("/search/pager/recommend", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpRecommendPagerReqDto.class,
                                service::searchCorpRecommendPager))
                .POST("/search", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, AbstractSession.class,
                                service::searchCorp))
                .POST("/detail", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpInfoDetailReqDto.class,
                                service::detail))
                .POST("/id", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpIdSearchReqDto.class,
                                service::getCorpById))
                .POST("/find/session", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, RequireSessionDto.class,
                                service::findBySession))
                .POST("/find/user", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, UserIdReqDto.class,
                                service::findByUserId))
                .POST("/moment/pager", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpWonderfulMomentReqDto.class,
                                service::searchWonderfulMoment))
                .POST("/live/pager", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpWonderfulMomentReqDto.class,
                                service::searchLiveReplay))
                .build());
    }

}


