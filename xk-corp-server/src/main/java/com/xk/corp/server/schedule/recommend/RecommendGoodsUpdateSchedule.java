package com.xk.corp.server.schedule.recommend;

import com.myco.framework.scheduling.quartz.support.Job2ExecutionContext;
import com.myco.mydata.infrastructure.schedule.job.DefaultJob;
import com.xk.corp.interfaces.task.CorpRecommendToCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RecommendGoodsUpdateSchedule extends DefaultJob {

    private final CorpRecommendToCacheService corpRecommendToCacheService;

    @Override
    public Mono<Void> handler(Job2ExecutionContext context) {
        return corpRecommendToCacheService.updateRecommendGoods();
    }

}
