package com.xk.corp.server.endpoints.recommend;

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import com.xk.corp.interfaces.dto.req.recommend.CorpRecommendGoodsReqDto;
import com.xk.corp.interfaces.dto.req.recommend.CorpRecommendPagerReqDto;
import com.xk.corp.interfaces.dto.req.recommend.CorpRecommendRandomQueryReqDto;
import com.xk.corp.interfaces.query.recommend.CorpRecommendQueryService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.myco.mydata.server.handler.WebFluxHandler;

@Configuration(proxyBeanMethods = false)
public class CorpRecommendQueryRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> corpRecommendQueryServiceRouter(CorpRecommendQueryService service) {
        return nest(RequestPredicates.path("/corp/recommend/query"), route()
                .POST("/search/pager", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpRecommendPagerReqDto.class,
                                service::searchCorpRecommendPager))
                .POST("/search/random", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpRecommendRandomQueryReqDto.class,
                                service::searchRandom))
                .POST("/search/goods", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpRecommendGoodsReqDto.class,
                                service::searchGoods))
                .build());
    }
}


