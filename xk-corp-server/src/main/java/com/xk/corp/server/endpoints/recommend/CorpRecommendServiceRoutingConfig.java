package com.xk.corp.server.endpoints.recommend;

/**
 * @author: killer
 **/

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.corp.interfaces.dto.req.recommend.*;
import com.xk.corp.interfaces.service.recommend.CorpRecommendService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

@Configuration(proxyBeanMethods = false)
public class CorpRecommendServiceRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> corpRecommendServiceRouter(CorpRecommendService service) {
        return nest(
                RequestPredicates.path("/corp/recommend"), route()
                        .POST("/create", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request, CorpRecommendCreateReqDto.class,
                                        service::createCorpRecommend))
                        .POST("/update", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request, CorpRecommendUpdateReqDto.class,
                                        service::updateCorpRecommend))
                        .POST("/updateRandom", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request, CorpRecommendRandomReqDto.class,
                                        service::updateRandom))
                        .POST("/removeRecommendGoods", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request, CorpRecommendGoodsReqDto.class,
                                        service::removeRecommendGoods))
                        .POST("/removeRecommendCorp", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request, CorpRecommendGoodsReqDto.class,
                                        service::removeRecommendCorp))
                        .POST("/updateRecommendGoods", ACCEPT_JSON,
                                request -> WebFluxHandler.handler(request, CorpRecommendGoodsUpdateReqDto.class,
                                        service::updateRecommendGoods))
                        .build());
    }

}


