package com.xk.corp.server.endpoints.apply;

/**
 * @author: killer
 **/

import static org.springframework.web.reactive.function.server.RequestPredicates.accept;
import static org.springframework.web.reactive.function.server.RouterFunctions.nest;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicate;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;

import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.corp.interfaces.dto.req.apply.CorpApplyAuditReqDto;
import com.xk.corp.interfaces.dto.req.apply.CorpApplyRemarkAddReqDto;
import com.xk.corp.interfaces.dto.req.apply.CorpApplyReqDto;
import com.xk.corp.interfaces.dto.req.apply.CorpApplyStatusUpdateReqDto;
import com.xk.corp.interfaces.service.apply.CorpApplyService;

@Configuration(proxyBeanMethods = false)
public class CorpApplyServiceRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON = accept(MediaType.APPLICATION_JSON);

    @Bean
    public RouterFunction<ServerResponse> corpApplyServiceRouter(CorpApplyService service) {
        return nest(RequestPredicates.path("/corp/apply"), route()
                .POST("/create", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpApplyReqDto.class,
                                service::createCorpApply))
                .POST("/audit", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpApplyAuditReqDto.class,
                                service::updateCorpApplyAudit))
                .POST("/status/update", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpApplyStatusUpdateReqDto.class,
                                service::updateCorpApplyStatus))
                .POST("/remark/add", ACCEPT_JSON,
                        request -> WebFluxHandler.handler(request, CorpApplyRemarkAddReqDto.class,
                                service::addCorpApplyRemark))
                .build());
    }

}


