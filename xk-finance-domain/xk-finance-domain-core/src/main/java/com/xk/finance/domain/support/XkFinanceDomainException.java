package com.xk.finance.domain.support;

import com.xk.finance.domain.commons.XkFinanceDomainErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.ExceptionRoot;
import com.myco.mydata.domain.model.exception.wrapper.DomainWrapperThrowable;

/**
 * 领域异常
 * @author: killer
 **/
public class XkFinanceDomainException extends DomainWrapperThrowable {

    public XkFinanceDomainException(XkFinanceDomainErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public XkFinanceDomainException(XkFinanceDomainErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

}
