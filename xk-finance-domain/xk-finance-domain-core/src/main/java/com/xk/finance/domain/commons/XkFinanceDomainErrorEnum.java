package com.xk.finance.domain.commons;


import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * domain相关错误码
 * 定义错误码区间 11000-11999
 * @author: killer
 **/
@Getter
@AllArgsConstructor
public enum XkFinanceDomainErrorEnum implements ExceptionIdentifier {
    DOMAIN_ERROR(11000, "domain错误"),

    ;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}
