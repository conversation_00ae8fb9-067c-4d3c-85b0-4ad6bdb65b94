package com.xk.order.domain.model.order.id;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

/**
 * 订单项标识符
 */

@Getter
@Builder
@RequiredArgsConstructor
@EqualsAndHashCode
public class OrderItemIdentifier implements Identifier<OrderItemIdentifier> {

    /**
     * 订单项ID
     */
    private final Long orderItemId;

    @Override
    public @NonNull OrderItemIdentifier getIdentifier() {
        return this;
    }
}
