package com.xk.order.domain.repository.logisticsSendGoods;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.order.domain.model.logisticsSendGoods.LogisticsSendGoodsOrderRoot;
import com.xk.order.domain.model.logisticsSendGoods.entity.LogisticsSendGoodsEntity;

import reactor.core.publisher.Flux;

public interface LogisticsSendGoodsRootQueryRepository extends IQueryRepository {
    Flux<LogisticsSendGoodsEntity> selectById(LogisticsSendGoodsOrderRoot root);
}
