package com.xk.order.domain.model.order.entity;

import com.myco.mydata.domain.model.Identifier;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.enums.order.OrderRefundStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@Builder
@AllArgsConstructor
public class OrderRefundEntity implements Identifier<OrderIdentifier> {

    /**
     * 订单ID（业务主键）
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 退款支付标识符
     */
    private String refundPaymentId;

    /**
     * 退款状态 1-无退款 2-退款中 3-已退款 4-退款失败
     */
    private OrderRefundStatusEnum refundStatus;

    @Override
    public @NonNull OrderIdentifier getIdentifier() {
        return OrderIdentifier.builder().orderNo(orderNo).build();
    }

    public void setDefault() {
        this.refundStatus = OrderRefundStatusEnum.NONE;
    }
}
