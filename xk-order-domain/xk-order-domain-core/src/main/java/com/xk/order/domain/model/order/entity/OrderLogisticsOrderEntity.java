package com.xk.order.domain.model.order.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.order.id.LogisticsOrderOrderIdentifier;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@Builder
@AllArgsConstructor
public class OrderLogisticsOrderEntity implements Entity<LogisticsOrderOrderIdentifier> {

    /**
     * 物流订单id
     */
    private Long logisticsOrderId;

    /**
     * 订单id
     */
    private String orderNo;

    /**
     * 物流订单类型 1-商城 2-物料 3-商品 4-赠品
     */
    private LogisticsOrderTypeEnum logisticsOrderType;

    /**
     * 物流订单状态 1、待发货2、待收货3、已完成
     */
    private LogisticsOrderStatusEnum logisticsOrderStatus;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流公司名
     */
    private String logisticsCorpName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @Override
    public @NonNull LogisticsOrderOrderIdentifier getIdentifier() {
        return LogisticsOrderOrderIdentifier.builder()
                .orderId(OrderIdentifier.builder().orderNo(orderNo).build()).build();
    }

    @Override
    public Validatable<LogisticsOrderOrderIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
