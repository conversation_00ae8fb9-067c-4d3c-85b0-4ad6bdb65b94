package com.xk.order.domain.service.order;

import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.enums.order.OrderTypeEnum;

import reactor.core.publisher.Mono;

public interface OrderRootService {

    Mono<Long> generateId();

    Mono<String> generateOrderNo(OrderTypeEnum orderType);

    Mono<String> generateOrderNo(OrderTypeEnum orderType, ProductTypeEnum productType);

    /**
     * 获取订单聚合根
     *
     * @param identifier identifier
     * @return Mono<OrderRoot>
     */
    Mono<OrderRoot> getRoot(OrderIdentifier identifier);

    /**
     * 获取订单聚合根,不查询物流信息
     * 
     * @param identifier identifier
     * @return Mono<OrderRoot>
     */
    Mono<OrderRoot> getRootNoLogistics(OrderIdentifier identifier);

    /**
     * 保存订单聚合根
     * 
     * @param root root
     * @return Mono<Void>
     */
    Mono<Void> updateRoot(Mono<OrderRoot> root);

    /**
     * 保存root
     * 
     * @param mono mono
     * @return Mono<Void>
     */
    Mono<Void> saveRoot(Mono<OrderRoot> mono);

    /**
     * 获取取消订单队列缓存
     * 
     * @param orderRoot orderRoot
     * @return Mono<OrderIdentifier>
     */
    Mono<OrderIdentifier> getOrderCancelCache(OrderRoot orderRoot);

    /**
     * 删除取消订单队列缓存
     * 
     * @param orderRoot orderRoot
     * @return Mono<OrderIdentifier>
     */
    Mono<Void> deleteCancelQueue(OrderRoot orderRoot);

    /**
     * 删除取消订单队列指定数据
     *
     * @param orderRoot orderRoot
     * @return Mono<Void>
     */
    Mono<Void> deleteCancelOrder(OrderRoot orderRoot);

    /**
     * 是否有订单取消缓存
     *
     * @param orderRoot orderRoot
     * @return Mono<Boolean>
     */
    Mono<Boolean> haveOrderCancelCache(OrderRoot orderRoot);
}
