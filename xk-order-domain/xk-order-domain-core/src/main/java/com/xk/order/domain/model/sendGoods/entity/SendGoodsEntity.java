package com.xk.order.domain.model.sendGoods.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.sendGoods.id.SendGoodsIdentifier;
import com.xk.order.domain.model.sendGoods.valobj.CorpOrderAddrValueObject;
import com.xk.order.domain.model.sendGoods.valobj.SendOrderAddrValueObject;
import com.xk.order.enums.sendGoods.LogisticsStatusEnum;

import lombok.*;

/**
 * 物流订单实体
 * <AUTHOR>
 * @Date 2024/8/6 14:29
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SendGoodsEntity implements Entity<SendGoodsIdentifier> {

    /**
     * 发货id
     */
    private Long sendGoodsId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商户ID
     */
    private Long corpId;

    /**
     * 物流公司id
     */
    private Long logisticsCorpId;

    /**
     * 物流公司名称
     */
    private String logisticsCorpName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 商家发货地址
     */
    private CorpOrderAddrValueObject corpOrderAddr;

    /**
     * 商家收货地址
     */
    private SendOrderAddrValueObject sendOrderAddr;

    /**
     * 物流状态
     */
    private LogisticsStatusEnum logisticsStatus;

    /**
     * 物流状态源
     */
    private String logisticsStatusSource;

    /**
     * 异常原因
     */
    private String errRemark;

    /**
     * 运费价格
     */
    private Long price;

    /**
     * 发货时间
     */
    private Date sendGoodsTime;

    /**
     * 应发货时间
     */
    private Date deliveryTime;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 取消截止时间
     */
    private Date cancelDeadlineTime;

    @Override
    public @NonNull SendGoodsIdentifier getIdentifier() {
        return SendGoodsIdentifier.builder().sendGoodsId(sendGoodsId).build();
    }

    /**
     * @throws ExceptionWrapperThrowable ExceptionWrapperThrowable
     */
    @Override
    public Validatable<SendGoodsIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
