package com.xk.order.domain.model.order.entity;

import com.myco.mydata.domain.model.Identifier;
import com.xk.order.domain.model.order.id.OrderIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@Builder
@AllArgsConstructor
public class OrderAddressEntity implements Identifier<OrderIdentifier> {

    /**
     * 订单ID（业务主键）
     */
    private Long orderId;

    /**
     * 订单ID（业务主键）
     */
    private String orderNo;

    /**
     * 用户地址ID
     */
    private Long userAddressId;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 省市区中文
     */
    private String addressSite;

    /**
     * 详细地址
     */
    private String addressDetail;

    @Override
    public @NonNull OrderIdentifier getIdentifier() {
        return OrderIdentifier.builder().orderNo(orderNo).build();
    }

    public void setDefault() {

    }
}
