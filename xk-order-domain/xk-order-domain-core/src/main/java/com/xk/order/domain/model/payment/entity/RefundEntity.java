package com.xk.order.domain.model.payment.entity;


import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.order.domain.model.payment.id.PaymentIdentifier;
import com.xk.order.enums.payment.PayStatusEnum;
import com.xk.order.enums.payment.PaymentPayTypeEnum;
import com.xk.order.enums.payment.RefundStatusEnum;
import lombok.*;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundEntity implements Entity<PaymentIdentifier> {

    /**
     * 收付单号
     */
    private Long paymentId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 退款状态
     */
    private RefundStatusEnum refundStatus;

    /**
     * 发起平台
     */
    private PlatformTypeEnum platformType;

    /**
     * 支付类型
     */
    private PaymentPayTypeEnum payType;

    /**
     * 收款完成时间
     */
    private Date payTime;

    /**
     * 收款金额
     */
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除：0-未删除 1-已删除
     */
    private CommonStatusEnum deleted;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @Override
    public @NonNull PaymentIdentifier getIdentifier() {
        return PaymentIdentifier.builder().paymentId(paymentId).build();
    }

    @Override
    public Validatable<PaymentIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
