package com.xk.order.domain.service.order.impl;

import java.util.function.Supplier;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.order.domain.event.order.OrderCreateEvent;
import com.xk.order.domain.event.order.OrderUpdateEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.repository.order.OrderItemRootQueryRepository;
import com.xk.order.domain.repository.order.OrderRootQueryRepository;
import com.xk.order.domain.repository.order.OrderRootRepository;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.domain.support.OrderSequenceEnum;
import com.xk.order.enums.order.OrderTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderRootServiceImpl implements OrderRootService {

    private final IdentifierGenerateService identifierGenerateService;
    private final OrderRootRepository orderRootRepository;
    private final OrderRootQueryRepository orderRootQueryRepository;
    private final OrderItemRootQueryRepository orderItemRootQueryRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Long> generateId() {
        return Mono.just((Long) identifierGenerateService
                .generateIdentifier(IdentifierRoot.builder().identifier(OrderSequenceEnum.O_ORDER)
                        .type(IdentifierGenerateEnum.CACHE).build()));
    }

    @Override
    public Mono<String> generateOrderNo(OrderTypeEnum orderType) {
        return generateOrderNo(orderType, null);
    }

    @Override
    public Mono<String> generateOrderNo(OrderTypeEnum orderType, ProductTypeEnum productType) {
        return Mono.justOrEmpty(orderType)
                .switchIfEmpty(
                        Mono.error(new SystemWrapperThrowable(SystemErrorEnum.VALIDATE_FAILURE)))
                .then(orderRootRepository.generateOrderNo(orderType, productType));
    }

    @Override
    public Mono<OrderRoot> getRoot(OrderIdentifier identifier) {
        return orderRootQueryRepository.getRoot(identifier);
    }

    @Override
    public Mono<OrderRoot> getRootNoLogistics(OrderIdentifier identifier) {
        return orderRootQueryRepository.getRootNoLogistics(identifier);
    }

    @Override
    public Mono<Void> updateRoot(Mono<OrderRoot> mono) {
        return mono.flatMap(root -> {
            Supplier<Mono<Void>> publishEvent = () -> {
                // 事件发布函数
                String orderNo = root.getIdentifier().getOrderNo();
                EventRoot eventRoot = EventRoot.builder().domainEvent(OrderUpdateEvent.builder()
                        .identifier(
                                EventRoot.getCommonsDomainEventIdentifier(OrderUpdateEvent.class))
                        .orderNo(orderNo)
                        .oldOrderStatus(root.getOrderEntity().getOrderOldStatus().getCode())
                        .orderStatus(root.getOrderEntity().getOrderStatus().getCode())
                        .orderType(root.getOrderEntity().getOrderType().getCode()).build())
                        .isQueue(true).build();

                return eventRootService.publisheByMono(eventRoot)
                        .doOnSuccess(v -> log.info("UpdateOrderEvent事件发布完成: {}", orderNo)).then();
            };
            return root.checkSave().then(orderRootRepository.update(root)).then(publishEvent.get());
        });
    }

    @Override
    public Mono<Void> saveRoot(Mono<OrderRoot> mono) {
        return mono.flatMap(root -> {
            Supplier<Mono<Void>> publishEvent = () -> {
                // 事件发布函数
                String orderNo = root.getIdentifier().getOrderNo();
                OrderTypeEnum orderType = root.getOrderEntity().getOrderType();
                Long createId = root.getOrderEntity().getCreateValObj().getCreateId();
                EventRoot eventRoot = EventRoot.builder().domainEvent(OrderCreateEvent.builder()
                        .identifier(
                                EventRoot.getCommonsDomainEventIdentifier(OrderCreateEvent.class))
                        .orderNo(orderNo).orderType(orderType).createId(createId).build())
                        .isQueue(true).build();

                return eventRootService.publisheByMono(eventRoot)
                        .doOnSuccess(v -> log.info("OrderCreateEvent事件发布完成: {}", orderNo)).then();
            };

            return root.initDefault().then(orderRootRepository.save(root)).then(publishEvent.get())
                    .then(orderRootRepository.addCancelQueue(root));
        });
    }

    @Override
    public Mono<OrderIdentifier> getOrderCancelCache(OrderRoot orderRoot) {
        return orderRootQueryRepository.queryOrderCache(orderRoot);
    }

    @Override
    public Mono<Void> deleteCancelQueue(OrderRoot orderRoot) {
        return orderRootRepository.deleteCancelQueue(orderRoot);
    }

    @Override
    public Mono<Void> deleteCancelOrder(OrderRoot orderRoot) {
        return orderRootRepository.deleteCancelOrder(orderRoot);
    }

    @Override
    public Mono<Boolean> haveOrderCancelCache(OrderRoot orderRoot) {
        return orderRootQueryRepository.haveOrderCancelCache(orderRoot);
    }
}
