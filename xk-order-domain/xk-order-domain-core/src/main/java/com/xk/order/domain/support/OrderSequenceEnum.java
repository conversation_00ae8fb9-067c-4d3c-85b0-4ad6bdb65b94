package com.xk.order.domain.support;

import com.myco.mydata.domain.model.identifier.SequenceIdentifier;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum OrderSequenceEnum implements SequenceIdentifier {

    O_ORDER("O_ORDER","order_id","OOrderMapper"),
    O_ORDER_ITEM("O_ORDER_ITEM","order_item_id","OOrderItemMapper"),
    O_ORDER_GIFT("O_ORDER_GIFT","order_gift_id","OOrderGiftMapper"),
    O_LOGISTICS_ORDER("O_LOGISTICS_ORDER","o_logistics_order","OLogisticsOrderMapper"),
    O_LOGISTICS_SEND_GOODS("O_LOGISTICS_SEND_GOODS","o_logistics_send_goods","OLogisticsSendGoodsMapper"),
    O_ORDER_LOG("O_ORDER_LOG","o_order_log","OOrderLogMapper"),
    O_SEND_GOODS("O_SEND_GOODS","o_send_goods","OSendGoodsMapper"),
    O_SEND_GOODS_DETAIL("O_SEND_GOODS_DETAIL","o_send_goods_detail","OSendGoodsDetailMapper"),
    O_PAYMENT("O_PAYMENT","o_payment","OPaymentMapper"),
    O_PAYMENT_DETAIL("O_PAYMENT_DETAIL","o_payment_detail","OPaymentDetailMapper"),
    O_REFUND("O_REFUND","o_refund","ORefundMapper"),
    ;

    private final String table;
    private final String pk;
    private final String className;

    @Override
    public @NonNull String getName() {
        return this.name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return this.name();
    }

    @Override
    public String getTable() {
        return this.table;
    }

    public String getPk() {
        return this.pk;
    }

    public String getClassName() {
        return this.className;
    }

}
