package com.xk.order.domain.service.logisticsSendGoods.impl;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.order.domain.service.logisticsSendGoods.LogisticsSendGoodsRootService;
import com.xk.order.domain.support.OrderSequenceEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsSendGoodsRootServiceImpl implements LogisticsSendGoodsRootService {

    private final IdentifierGenerateService identifierGenerateService;

    @Override
    public Mono<Long> generateId() {
        return Mono.just((Long) identifierGenerateService.generateIdentifier(
                IdentifierRoot.builder().identifier(OrderSequenceEnum.O_LOGISTICS_SEND_GOODS)
                        .type(IdentifierGenerateEnum.CACHE).build()));
    }
}
