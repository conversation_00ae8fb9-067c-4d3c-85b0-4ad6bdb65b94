package com.xk.order.domain.repository.payment;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import reactor.core.publisher.Mono;

public interface PaymentRootQueryRepository extends IQueryRepository {

    /**
     * 根据订单号查询支付单
     * @param orderNo orderNo
     * @return Mono<PaymentEntity>
     */
    Mono<PaymentEntity> findByOrderNo(String orderNo);

}
