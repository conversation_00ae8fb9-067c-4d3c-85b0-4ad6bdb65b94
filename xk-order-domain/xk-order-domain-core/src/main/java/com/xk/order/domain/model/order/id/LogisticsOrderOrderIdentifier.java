package com.xk.order.domain.model.order.id;

import com.myco.mydata.domain.model.Identifier;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class LogisticsOrderOrderIdentifier implements Identifier<LogisticsOrderOrderIdentifier> {

    private final OrderIdentifier orderId;

    private final LogisticsOrderOrderIdentifier logisticsOrderId;

    @Override
    public @NonNull LogisticsOrderOrderIdentifier getIdentifier() {
        return this;
    }
}
