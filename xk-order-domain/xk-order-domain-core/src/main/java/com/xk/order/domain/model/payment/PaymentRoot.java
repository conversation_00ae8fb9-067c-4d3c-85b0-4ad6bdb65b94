package com.xk.order.domain.model.payment;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.payment.entity.PaymentDetailEntity;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.model.payment.entity.RefundEntity;
import com.xk.order.domain.model.payment.id.PaymentIdentifier;
import com.xk.order.domain.model.payment.valobj.RiskControlValObj;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@Builder
public class PaymentRoot extends DomainRoot<PaymentIdentifier> {


    private PaymentEntity paymentEntity;

    private RefundEntity  refundEntity;

    private RiskControlValObj riskControlValObj;

    private PaymentDetailEntity  paymentDetailEntity;

    @Override
    public Validatable<PaymentIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
