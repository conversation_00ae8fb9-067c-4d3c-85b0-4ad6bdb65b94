package com.xk.order.domain.model.sendGoods.valobj;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CorpOrderAddrValueObject {

    /**
     * 商家发货地址ID
     */
    private Long addressId;

    /**
     * 商家发货手机号
     */
    private String mobile;

    /**
     * 发货省
     */
    private String province;

    /**
     * 发货市
     */
    private String city;

    /**
     * 发货区
     */
    private String district;

    /**
     * 详细发货地址
     */
    private String addressDetail;

    /**
     * 发货人姓名
     */
    private String name;

}
