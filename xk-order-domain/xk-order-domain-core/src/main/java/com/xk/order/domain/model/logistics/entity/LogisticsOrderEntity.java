package com.xk.order.domain.model.logistics.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.logistics.valobj.OrderValueObject;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;

import lombok.*;

/**
 * 物流订单实体
 * <AUTHOR>
 * @Date 2024/8/6 14:29
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogisticsOrderEntity implements Entity<LogisticsOrderIdentifier> {

    /**
     * 物流订单ID
     */
    private Long logisticsOrderId;

    /**
     * 订单值对象
     */
    private OrderValueObject orderValueObject;

    /**
     * 物流订单状态 1、待发货2、待收货3、已完成
     */
    private LogisticsOrderStatusEnum logisticsOrderStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 订单状态时间
     */
    private Date orderStatusTime;

    @Override
    public @NonNull LogisticsOrderIdentifier getIdentifier() {
        return LogisticsOrderIdentifier.builder().logisticsOrderId(logisticsOrderId).build();
    }

    /**
     * @throws ExceptionWrapperThrowable ExceptionWrapperThrowable
     */
    @Override
    public Validatable<LogisticsOrderIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
