package com.xk.order.domain.repository.order;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.enums.order.OrderTypeEnum;

import reactor.core.publisher.Mono;

public interface OrderRootRepository extends IRepository<OrderRoot> {

    /**
     * 生成订单标识符
     *
     * @param orderType orderType
     * @param productType productType
     * @return Mono<String>
     */
    Mono<String> generateOrderNo(OrderTypeEnum orderType, ProductTypeEnum productType);

    /**
     * 添加到取消队列
     * 
     * @param root root
     * @return Mono<Void>
     */
    Mono<Void> addCancelQueue(OrderRoot root);

    /**
     * 删除订单缓存
     * 
     * @param orderRoot orderRoot
     * @return Mono<Void>
     */
    Mono<Void> deleteCancelQueue(OrderRoot orderRoot);

    /**
     * 删除取消订单
     *
     * @param orderRoot orderRoot
     * @return Mono<Void>
     */
    Mono<Void> deleteCancelOrder(OrderRoot orderRoot);
}
