package com.xk.order.domain.model.order.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.order.domain.model.order.id.OrderIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@Builder
@AllArgsConstructor
public class OrderPriceEntity implements Entity<OrderIdentifier> {

    /**
     * 订单ID（业务主键）
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品总金额
     */
    private Long totalAmount;

    /**
     * 商家优惠金额
     */
    private Long corpDiscountAmount;

    /**
     * 商品应付金额
     */
    private Long needAmount;

    /**
     * 商品其他优惠金额
     */
    private Long otherDiscountAmount;

    /**
     * 实付金额
     */
    private Long payAmount;

    /**
     * 免费额度状态 0-未使用 1-已使用
     */
    private CommonStatusEnum freeQuotaDiscountStatus;

    /**
     * 免费额度减免金额
     */
    private Long freeQuotaDiscountAmount;

    /**
     * 满减状态 0-未使用 1-已使用
     */
    private CommonStatusEnum discountAmountStatus;

    /**
     * 满减金额
     */
    private Long discountAmount;

    /**
     * 首购优惠状态：0-未使用 1-已使用
     */
    private CommonStatusEnum firstBuyDiscountStatus;

    /**
     * 首购优惠减免金额
     */
    private Long firstBuyDiscountAmount;

    /**
     * 优惠券状态 0-未使用 1-已使用
     */
    private CommonStatusEnum couponStatus;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券减免金额
     */
    private Long couponAmount;

    /**
     * 运费
     */
    private Long shippingFee;

    @Override
    public @NonNull OrderIdentifier getIdentifier() {
        return OrderIdentifier.builder().orderNo(orderNo).build();
    }

    @Override
    public Validatable<OrderIdentifier> validate() {
        return this;
    }

    public void setDefault() {
        if (this.freeQuotaDiscountStatus == null) {
            this.freeQuotaDiscountStatus = CommonStatusEnum.DISABLE;
        }
        if (this.discountAmountStatus == null) {
            this.discountAmountStatus = CommonStatusEnum.DISABLE;
        }
        if (this.firstBuyDiscountStatus == null) {
            this.firstBuyDiscountStatus = CommonStatusEnum.DISABLE;
        }
        if (this.couponStatus == null) {
            this.couponStatus = CommonStatusEnum.DISABLE;
        }
        if (this.shippingFee == null) {
            this.shippingFee = 0L;
        }
        if (this.couponAmount == null) {
            this.couponAmount = 0L;
        }
        if (this.firstBuyDiscountAmount == null) {
            this.firstBuyDiscountAmount = 0L;
        }
        if (this.discountAmount == null) {
            this.discountAmount = 0L;
        }
        if (this.freeQuotaDiscountAmount == null) {
            this.freeQuotaDiscountAmount = 0L;
        }
        if (this.corpDiscountAmount == null) {
            this.corpDiscountAmount = 0L;
        }
        if (this.otherDiscountAmount == null) {
            this.otherDiscountAmount = totalAmount - payAmount;
        }
        if (this.needAmount == null) {
            this.needAmount = totalAmount;
        }
    }
}
