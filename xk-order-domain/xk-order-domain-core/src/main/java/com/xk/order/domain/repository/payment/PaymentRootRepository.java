package com.xk.order.domain.repository.payment;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.order.domain.model.payment.PaymentRoot;
import com.xk.order.domain.model.payment.entity.PaymentDetailEntity;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.model.payment.entity.RefundEntity;
import reactor.core.publisher.Mono;

public interface PaymentRootRepository extends IRepository<PaymentRoot> {

    /**
     * 根据订单号修改支付单
     * @param paymentEntity paymentEntity
     * @return Mono<Void>
     */
    Mono<Void> updateByOrderNo(PaymentEntity paymentEntity);

    /**
     * 添加支付单
     * @param paymentEntity paymentEntity
     * @return Mono<Void>
     */
    Mono<Void> insertPayment(PaymentEntity paymentEntity);

    /**
     * 添加支付单明细
     * @param paymentDetailEntity paymentDetailEntity
     * @return Mono<Void>
     */
    Mono<Void> insertPaymentDetail(PaymentDetailEntity paymentDetailEntity);

    /**
     * 添加退款单
     * @param refundEntity refundEntity
     * @return Mono<Void>
     */
    Mono<Void> insertRefund(RefundEntity refundEntity);
}
