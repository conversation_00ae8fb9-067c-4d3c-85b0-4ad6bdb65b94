package com.xk.order.domain.repository.logistics;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface LogisticsOrderRootQueryRepository extends IQueryRepository {

    Mono<LogisticsOrderEntity> selectById(LogisticsOrderRoot root);

    Flux<LogisticsOrderEntity> selectList(LogisticsOrderRoot root);
}
