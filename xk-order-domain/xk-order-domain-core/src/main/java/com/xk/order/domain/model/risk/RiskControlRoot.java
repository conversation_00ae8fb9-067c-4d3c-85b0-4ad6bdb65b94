package com.xk.order.domain.model.risk;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.risk.entity.RefundRiskControlEntity;
import com.xk.order.domain.model.risk.id.RiskIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@Builder
public class RiskControlRoot extends DomainRoot<RiskIdentifier> {

    private RefundRiskControlEntity refundRiskControlEntity;

    @Override
    public Validatable<RiskIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
