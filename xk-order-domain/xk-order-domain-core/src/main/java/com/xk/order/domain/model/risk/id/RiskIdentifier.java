package com.xk.order.domain.model.risk.id;

import com.myco.mydata.domain.model.Identifier;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class RiskIdentifier implements Identifier<RiskIdentifier> {

    private final Long riskId;

    @Override
    public @NonNull RiskIdentifier getIdentifier() {
        return this;
    }
}
