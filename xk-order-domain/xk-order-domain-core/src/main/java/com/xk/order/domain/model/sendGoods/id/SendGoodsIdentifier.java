package com.xk.order.domain.model.sendGoods.id;

import com.myco.mydata.domain.model.Identifier;
import lombok.*;

/**
 * @Author: liu<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-22
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SendGoodsIdentifier implements Identifier<SendGoodsIdentifier> {

    /**
     * 发货id
     */
    private Long sendGoodsId;

    @Override
    public @NonNull SendGoodsIdentifier getIdentifier() {
        return SendGoodsIdentifier.builder().sendGoodsId(sendGoodsId).build();
    }
}
