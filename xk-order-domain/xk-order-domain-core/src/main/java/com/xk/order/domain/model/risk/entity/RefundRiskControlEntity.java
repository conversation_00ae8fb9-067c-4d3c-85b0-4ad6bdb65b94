package com.xk.order.domain.model.risk.entity;


import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.risk.id.RiskIdentifier;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundRiskControlEntity implements Entity<RiskIdentifier> {

    private Long riskId;

    private Long amount;

    private Integer refundCount;

    private Long userId;

    @Override
    public @NonNull RiskIdentifier getIdentifier() {
        return RiskIdentifier.builder().riskId(riskId).build();
    }

    @Override
    public Validatable<RiskIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
