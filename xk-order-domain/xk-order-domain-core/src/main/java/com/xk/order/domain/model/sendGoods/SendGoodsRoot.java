package com.xk.order.domain.model.sendGoods;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;
import com.xk.order.domain.model.sendGoods.id.SendGoodsIdentifier;
import com.xk.order.domain.model.sendGoods.valobj.LogisticsValueObject;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class SendGoodsRoot extends DomainRoot<SendGoodsIdentifier> {

    private final List<LogisticsOrderIdentifier> logisticsOrderIdentifierList;

    private final SendGoodsEntity sendGoodsEntity;

    private final List<LogisticsValueObject> logisticsValueObjectList;

    @Builder
    public SendGoodsRoot(@NonNull SendGoodsIdentifier identifier, List<LogisticsOrderIdentifier> logisticsOrderIdentifierList, SendGoodsEntity sendGoodsEntity, List<LogisticsValueObject> logisticsValueObjectList) {
        super(identifier);
        this.logisticsOrderIdentifierList = logisticsOrderIdentifierList;
        this.sendGoodsEntity = sendGoodsEntity;
        this.logisticsValueObjectList = logisticsValueObjectList;
    }

    @Override
    public Validatable<SendGoodsIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
