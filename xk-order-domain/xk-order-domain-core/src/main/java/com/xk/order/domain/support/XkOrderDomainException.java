package com.xk.order.domain.support;

import com.myco.mydata.domain.model.exception.wrapper.DomainWrapperThrowable;
import com.xk.order.domain.commons.XkOrderDomainErrorEnum;

/**
 * 领域异常
 * 
 * @author: killer
 **/
public class XkOrderDomainException extends DomainWrapperThrowable {

    public XkOrderDomainException(XkOrderDomainErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public XkOrderDomainException(XkOrderDomainErrorEnum exceptionIdentifier, String msg) {
        super(exceptionIdentifier, msg);
    }

    public XkOrderDomainException(XkOrderDomainErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

}
