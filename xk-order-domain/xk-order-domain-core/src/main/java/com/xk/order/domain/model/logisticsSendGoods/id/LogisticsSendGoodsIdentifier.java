package com.xk.order.domain.model.logisticsSendGoods.id;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-22
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogisticsSendGoodsIdentifier implements Identifier<LogisticsSendGoodsIdentifier> {

    /**
     * 发货物流关联id
     */
    private Long id;

    @Override
    public @NonNull LogisticsSendGoodsIdentifier getIdentifier() {
        return LogisticsSendGoodsIdentifier.builder().id(id).build();
    }
}
