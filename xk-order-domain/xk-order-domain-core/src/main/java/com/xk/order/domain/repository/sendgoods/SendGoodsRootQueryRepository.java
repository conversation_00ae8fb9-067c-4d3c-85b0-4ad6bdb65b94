package com.xk.order.domain.repository.sendgoods;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.order.domain.model.sendGoods.SendGoodsRoot;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;

import reactor.core.publisher.Mono;

public interface SendGoodsRootQueryRepository extends IQueryRepository {

    Mono<SendGoodsEntity> selectById(SendGoodsRoot build);

}
