package com.xk.order.domain.commons;


import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * domain相关错误码
 * 定义错误码区间 11000-11999
 * @author: killer
 **/
@Getter
@AllArgsConstructor
public enum XkOrderDomainErrorEnum implements ExceptionIdentifier {
    DOMAIN_ERROR(11000, "domain错误"),

    SPECIFICATION_HAS_LOCKED(11001,"您选中的惊喜福盒已被他人抢走，请重新选择"),
    ORDER_ADDR_CANT_CHANGE(11002,"订单已发货，无法修改地址"),
    ORDER_REMIND_SHIPPING_CANT_CHANGE(11003,"订单已发货，无法修改催发货状态"),
    ORDER_REMIND_SHIPPING_NOT_ALLOW(11004,"订单尚未到可催发货时间,请耐心等待"),
    ORDER_PAY_CANT_CHANGE(11005,"订单已支付,请勿重复操作"),
    SPECIFICATION_HAS_EXPIRE(11006,"您选中的惊喜福盒已过期，请重新选择"),
;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}
