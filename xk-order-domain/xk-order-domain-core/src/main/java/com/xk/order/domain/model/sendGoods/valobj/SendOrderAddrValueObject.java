package com.xk.order.domain.model.sendGoods.valobj;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendOrderAddrValueObject {

    /**
     * 用户收货地址ID
     */
    private Long addressId;

    /**
     * 用户收货手机号
     */
    private String mobile;

    /**
     * 收货省
     */
    private String province;

    /**
     * 收货市
     */
    private String city;

    /**
     * 收货区
     */
    private String district;

    /**
     * 收货省市区中文
     */
    private String addressSite;

    /**
     * 详细收货地址
     */
    private String addressDetail;

    /**
     * 收货人姓名
     */
    private String name;

}
