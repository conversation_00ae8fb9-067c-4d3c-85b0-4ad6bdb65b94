package com.xk.order.domain.model.order.id;

import com.myco.mydata.domain.model.Identifier;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class OrderIdentifier implements Identifier<OrderIdentifier> {

    private final String orderNo;

    @Override
    public @NonNull OrderIdentifier getIdentifier() {
        return this;
    }
}
