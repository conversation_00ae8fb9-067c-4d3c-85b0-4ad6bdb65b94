package com.xk.order.domain.model.order;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.id.OrderItemIdentifier;
import com.xk.order.domain.model.order.valobj.OrderItemLockValObj;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import reactor.core.publisher.Mono;

@Getter
public class OrderItemRoot extends DomainRoot<OrderItemIdentifier> {

    private final OrderItemEntity orderItemEntity;

    private final OrderItemLockValObj orderItemLockValObj;

    private final List<OrderGiftEntity> orderGiftEntityList;

    @Builder
    public OrderItemRoot(@NonNull OrderItemIdentifier identifier, OrderItemEntity orderItemEntity,
            OrderItemLockValObj orderItemLockValObj, List<OrderGiftEntity> orderGiftEntityList) {
        super(identifier);
        this.orderItemEntity = orderItemEntity;
        this.orderItemLockValObj = orderItemLockValObj;
        this.orderGiftEntityList = orderGiftEntityList;
    }

    @Override
    public Validatable<OrderItemIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

    public Mono<Void> initDefault() {
        this.orderItemEntity.setDefault();
        if (CollectionUtils.isNotEmpty(orderGiftEntityList)) {
            orderGiftEntityList.forEach(OrderGiftEntity::setDefault);
        }
        return checkSave();
    }

    public Mono<Void> checkSave() {
        return Mono.empty();
    }
}
