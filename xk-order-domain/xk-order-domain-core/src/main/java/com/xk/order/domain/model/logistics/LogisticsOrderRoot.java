package com.xk.order.domain.model.logistics;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;

import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class LogisticsOrderRoot extends DomainRoot<LogisticsOrderIdentifier> {

    private final LogisticsOrderEntity logisticsOrderEntity;

    @Builder
    public LogisticsOrderRoot(@NonNull LogisticsOrderIdentifier identifier, LogisticsOrderEntity logisticsOrderEntity) {
        super(identifier);
        this.logisticsOrderEntity = logisticsOrderEntity;
    }

    @Override
    public Validatable<LogisticsOrderIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
