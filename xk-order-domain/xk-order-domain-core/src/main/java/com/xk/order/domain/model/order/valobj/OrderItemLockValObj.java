package com.xk.order.domain.model.order.valobj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderItemLockValObj {
    private Long userId;

    /**
     * 位置
     */
    private Integer position;

    private Long goodsId;

    /**
     * true校验是否本人持有锁 false只校验是否被他人持有锁
     */
    private Boolean holdLock;
}
