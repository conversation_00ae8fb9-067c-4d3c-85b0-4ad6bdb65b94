package com.xk.order.domain.model.payment.entity;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.order.domain.model.payment.id.PaymentIdentifier;
import com.xk.order.enums.payment.PayDetailStatusEnum;
import com.xk.order.enums.payment.PayDirectionEnum;
import lombok.*;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentDetailEntity implements Entity<PaymentIdentifier> {

    /**
     * 收付单号
     */
    private Long paymentId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 收付方向
     */
    private PayDirectionEnum payDirection;

    /**
     * 流水支付状态
     */
    private PayDetailStatusEnum status;

    /**
     * 收付金额
     */
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除：0-未删除 1-已删除
     */
    private CommonStatusEnum deleted;

    /**
     * 创建人
     */
    private Long createId;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


    @Override
    public @NonNull PaymentIdentifier getIdentifier() {
        return PaymentIdentifier.builder().paymentId(paymentId).build();
    }

    @Override
    public Validatable<PaymentIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
