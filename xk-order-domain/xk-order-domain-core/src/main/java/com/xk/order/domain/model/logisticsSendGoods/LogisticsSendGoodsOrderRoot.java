package com.xk.order.domain.model.logisticsSendGoods;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.logisticsSendGoods.entity.LogisticsSendGoodsEntity;
import com.xk.order.domain.model.logisticsSendGoods.id.LogisticsSendGoodsIdentifier;

import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class LogisticsSendGoodsOrderRoot extends DomainRoot<LogisticsSendGoodsIdentifier> {

    private final LogisticsSendGoodsEntity logisticsSendGoodsEntity;

    @Builder
    public LogisticsSendGoodsOrderRoot(@NonNull LogisticsSendGoodsIdentifier identifier,
            LogisticsSendGoodsEntity logisticsSendGoodsEntity) {
        super(identifier);
        this.logisticsSendGoodsEntity = logisticsSendGoodsEntity;
    }

    @Override
    public Validatable<LogisticsSendGoodsIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
