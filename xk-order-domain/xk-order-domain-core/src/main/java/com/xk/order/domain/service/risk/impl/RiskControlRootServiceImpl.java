package com.xk.order.domain.service.risk.impl;

import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.repository.config.BusinessConfigRootQueryRepository;
import com.xk.order.domain.model.risk.RiskControlRoot;
import com.xk.order.domain.repository.risk.RiskControlRootRepository;
import com.xk.order.domain.service.risk.RiskControlRootService;
import com.xk.order.enums.risk.RiskKeysEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class RiskControlRootServiceImpl implements RiskControlRootService {

    private final BusinessConfigRootQueryRepository businessConfigRootQueryRepository;
    private final RiskControlRootRepository riskControlRootRepository;


    private Mono<String> getRiskLimit(RiskKeysEnum riskKeysEnum) {
        return businessConfigRootQueryRepository
                .findList(BusinessConfigEntity.builder().businessType(1).groupType("basic")
                        .groupId("0").keys(List.of(riskKeysEnum.getKey())).build())
                .collectList().flatMap(businessConfigEntities -> {
                    for (BusinessConfigEntity businessConfigEntity : businessConfigEntities) {
                        if (riskKeysEnum.getKey().equals(businessConfigEntity.getKey())) {
                            return Mono.just(businessConfigEntity.getVal());
                        }
                    }
                    return Mono.empty();
                });
    }

    @Override
    public Mono<Boolean> checkPayAmountLimit(Mono<RiskControlRoot> riskControlRootMono) {
        return riskControlRootMono.flatMap(riskControlRoot -> {
            return getRiskLimit(RiskKeysEnum.PAY_AMOUNT_LIMIT).filter(val -> {
                log.info("RiskControlRootService checkPayAmountLimit value:{},{},{}", val,
                        riskControlRoot.getRefundRiskControlEntity().getAmount(), riskControlRoot
                                .getRefundRiskControlEntity().getAmount() <= Long.parseLong(val));
                return riskControlRoot.getRefundRiskControlEntity().getAmount() <= Long
                        .parseLong(val);
            }).hasElement();
        });
    }

    @Override
    public Mono<Boolean> checkPayRefundLimit(Mono<RiskControlRoot> riskControlRootMono) {
        return riskControlRootMono.flatMap(riskControlRoot -> {
            return getRiskLimit(RiskKeysEnum.PAY_REFUND_LIMIT).flatMap(val -> {
                return riskControlRootRepository
                        .getRiskLimitByUserId(
                                Mono.just(riskControlRoot.getRefundRiskControlEntity().getUserId()))
                        .filter(l -> {
                            log.info("RiskControlRootService checkPayRefundLimit value:{},{},{}", l,
                                    val, l <= Long.parseLong(val));
                            return l <= Long.parseLong(val);
                        }).hasElement();
            });
        });
    }
}
