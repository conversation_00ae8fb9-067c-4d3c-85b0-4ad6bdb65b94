package com.xk.order.domain.service.payment;

import com.xk.order.domain.model.payment.PaymentRoot;
import com.xk.order.domain.model.payment.valobj.RiskControlValObj;
import com.xk.order.domain.support.OrderSequenceEnum;
import reactor.core.publisher.Mono;

public interface PaymentRootService {
    /**
     * 生成id
     * @param orderSequence orderSequence
     * @return Mono<Long>
     */
    Mono<Long> generateId(OrderSequenceEnum orderSequence);


    /**
     * 退款
     * @param paymentRootMono paymentRootMono
     * @return Mono<Void>
     */
    Mono<Void> saveRefund(Mono<PaymentRoot> paymentRootMono);

    /**
     * 添加流水
     * @param paymentRootMono paymentRootMono
     * @return Mono<Void>
     */
    Mono<Void> savePaymentDetail(Mono<PaymentRoot> paymentRootMono);

    /**
     * 退款
     * @param paymentRootMono paymentRootMono
     * @return Mono<Void>
     */
    Mono<Boolean> refund(Mono<PaymentRoot> paymentRootMono);

}
