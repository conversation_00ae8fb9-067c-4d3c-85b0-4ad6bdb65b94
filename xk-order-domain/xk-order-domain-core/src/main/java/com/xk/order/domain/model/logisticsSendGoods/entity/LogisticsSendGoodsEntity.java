package com.xk.order.domain.model.logisticsSendGoods.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.order.domain.model.logisticsSendGoods.id.LogisticsSendGoodsIdentifier;

import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/8/7 14:11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LogisticsSendGoodsEntity implements Entity<LogisticsSendGoodsIdentifier> {

    /**
     * id
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long logisticsOrderId;

    /**
     * 发货ID
     */
    private Long sendGoodsId;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    @Override
    public @NonNull LogisticsSendGoodsIdentifier getIdentifier() {
        return LogisticsSendGoodsIdentifier.builder().id(id).build();
    }

    @Override
    public Validatable<LogisticsSendGoodsIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
