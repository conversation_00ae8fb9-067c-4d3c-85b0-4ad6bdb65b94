package com.xk.order.domain.model.sendGoods.valobj;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LogisticsValueObject {

    /**
     * 物流事件时间
     */
    private Date trackTime;

    /**
     * 物流描述
     */
    private String trackDescribe;

    /**
     * 事件类型
     */
    private String trackType;

}
