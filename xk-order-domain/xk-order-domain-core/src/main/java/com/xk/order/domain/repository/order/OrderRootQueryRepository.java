package com.xk.order.domain.repository.order;

import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;

import reactor.core.publisher.Mono;

public interface OrderRootQueryRepository extends IQueryRepository {

    /**
     * 获取订单聚合根
     * 
     * @param identifier identifier
     * @return Mono<OrderRoot>
     */
    Mono<OrderRoot> getRoot(OrderIdentifier identifier);

    /**
     * 查询订单缓存
     * 
     * @param orderRoot orderRoot
     * @return Mono<OrderIdentifier>
     */
    Mono<OrderIdentifier> queryOrderCache(OrderRoot orderRoot);

    /**
     * 获取订单聚合根,不包含物流信息
     * 
     * @param identifier identifier
     * @return Mono<OrderRoot>
     */
    Mono<OrderRoot> getRootNoLogistics(OrderIdentifier identifier);

    /**
     * 是否有订单取消缓存
     *
     * @param orderRoot orderRoot
     * @return Mono<Boolean>
     */
    Mono<Boolean> haveOrderCancelCache(OrderRoot orderRoot);
}
