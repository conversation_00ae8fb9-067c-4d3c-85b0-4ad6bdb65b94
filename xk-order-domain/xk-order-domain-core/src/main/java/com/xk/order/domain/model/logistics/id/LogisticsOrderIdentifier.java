package com.xk.order.domain.model.logistics.id;

import com.myco.mydata.domain.model.Identifier;
import lombok.*;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-22
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogisticsOrderIdentifier implements Identifier<LogisticsOrderIdentifier> {

    /**
     * 物流订单ID
     */
    private Long logisticsOrderId;

    @Override
    public @NonNull LogisticsOrderIdentifier getIdentifier() {
        return LogisticsOrderIdentifier.builder().logisticsOrderId(logisticsOrderId).build();
    }
}
