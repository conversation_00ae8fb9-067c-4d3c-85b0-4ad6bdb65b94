package com.xk.order.domain.model.order.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Identifier;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.enums.payment.PayStatusEnum;
import com.xk.order.enums.payment.PaymentPayTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@Builder
@AllArgsConstructor
public class OrderPayEntity implements Identifier<OrderIdentifier> {

    /**
     * 订单ID（业务主键）
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 收款支付标识符
     */
    private String payPaymentId;

    /**
     * 支付方式：1-银行卡 2-支付宝 3-微信支付
     */
    private PaymentPayTypeEnum payType;

    /**
     * 支付状态：1-待付款 2-已支付 3-已取消 4-支付失败
     */
    private PayStatusEnum payStatus;

    /**
     * 支付金额
     */
    private Long paymentAmount;

    /**
     * 支付创建时间
     */
    private Date payCreateTime;

    /**
     * 支付成功时间
     */
    private Date payTime;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 退款支付标识符
     */
    private String refundPaymentId;

    /**
     * 退款审核状态：0-未审核 1-审核通过 2-审核拒绝
     */
    private Integer refundAuditStatus;

    /**
     * 退款审核人ID
     */
    private Long refundAuditUserId;

    /**
     * 退款审核时间
     */
    private Date refundAuditTime;


    @Override
    public @NonNull OrderIdentifier getIdentifier() {
        return OrderIdentifier.builder().orderNo(orderNo).build();
    }

    public void setDefault(Long payAmount) {
        this.payStatus = PayStatusEnum.PENDING;
        this.paymentAmount = payAmount;
        this.payCreateTime = new Date();
    }
}
