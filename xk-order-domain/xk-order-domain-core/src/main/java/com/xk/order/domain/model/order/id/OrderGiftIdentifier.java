package com.xk.order.domain.model.order.id;

import com.myco.mydata.domain.model.Identifier;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class OrderGiftIdentifier implements Identifier<OrderGiftIdentifier> {

    private final Long orderGiftId;

    @Override
    public @NonNull OrderGiftIdentifier getIdentifier() {
        return this;
    }
}
