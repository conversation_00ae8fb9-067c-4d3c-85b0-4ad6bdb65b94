package com.xk.ewd.application.convertor.logistics;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.dto.logistics.GiftCorpDto;
import com.xk.ewd.interfaces.dto.rsp.logistics.OrderSearchByGiftCorpRspDto;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class GiftCorpDtoToOrderSearchByGiftCorpRspDtoMapper
        implements BaseMapper<GiftCorpDto, OrderSearchByGiftCorpRspDto> {

    @Override
    public OrderSearchByGiftCorpRspDto convert(GiftCorpDto source) {
        return convert(source, OrderSearchByGiftCorpRspDto.builder().build());
    }

    @Override
    public OrderSearchByGiftCorpRspDto convert(GiftCorpDto source,
            OrderSearchByGiftCorpRspDto target) {
        if (source == null) {
            return null;
        }

        return OrderSearchByGiftCorpRspDto.builder().corpId(source.getCorpId())
                .corpName(source.getCorpName()).corpLogo(source.getCorpLogo()).build();
    }
}
