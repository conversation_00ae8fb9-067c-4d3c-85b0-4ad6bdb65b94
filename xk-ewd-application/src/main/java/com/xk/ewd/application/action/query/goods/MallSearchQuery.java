package com.xk.ewd.application.action.query.goods;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.goods.SearchGoodsMallReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers({@AutoMapper(target = SearchGoodsMallReqDto.class, convertGenerate = false)})
public class MallSearchQuery extends PagerQuery implements IActionQuery {

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 状态
     */
    private Integer listingStatus;

    /**
     * 分类id
     */
    private Long nodeId;

    private Integer showStatus;

    /**
     * 商品类型
     */
    private Integer goodsType;
}
