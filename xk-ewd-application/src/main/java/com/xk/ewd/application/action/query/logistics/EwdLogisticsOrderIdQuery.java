package com.xk.ewd.application.action.query.logistics;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.logistics.LogisticsDetailReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = LogisticsDetailReqDto.class)})
public class EwdLogisticsOrderIdQuery implements IActionQuery {

    /**
     * 物流订单ID
     */
    private Long logisticsOrderId;

}
