package com.xk.ewd.application.action.query.logistics;

import java.util.List;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.ewd.interfaces.dto.req.logistics.AppSendGoodsReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers(@AutoMapper(target = AppSendGoodsReqDto.class, convertGenerate = false))
public class AppSendGoodsQuery implements IActionQueryMany {

    /**
     * 物流订单ID
     */
    private List<Long> logisticsOrderIdList;

    private Long corpId;

}
