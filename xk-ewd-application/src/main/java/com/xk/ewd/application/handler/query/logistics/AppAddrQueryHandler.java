package com.xk.ewd.application.handler.query.logistics;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.ewd.application.action.query.logistics.AppAddrQuery;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.CorpEntity;
import com.xk.ewd.domain.model.logistics.entity.UserEntity;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.repository.logistics.LogisticsOrderRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.logistics.AppAddrResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 根据用户分组查询
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AppAddrQueryHandler implements IActionQueryManyHandler<AppAddrQuery, AppAddrResDto> {

    private final LogisticsOrderRootQueryRepository logisticsOrderRootQueryRepository;

    private final Converter converter;

    @Override
    public Flux<AppAddrResDto> execute(Mono<AppAddrQuery> query) {
        return query.flatMapMany(orderQuery -> ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObjectRoot -> {
                    UserDataObjectEntity userDataObjectEntity =
                            userObjectRoot.getUserDataObjectEntity();
                    if (UserTypeEnum.MERCHANT_KAS.equals(userDataObjectEntity.getUserType())) {
                        orderQuery.setCorpId(userDataObjectEntity.getCorpId());
                    }
                    LogisticsOrderRoot root = LogisticsOrderRoot.builder()
                            .identifier(LogisticsOrderIdentifier.builder().logisticsOrderId(-1L)
                                    .build())
                            .userEntity(UserEntity.builder().userId(orderQuery.getUserId()).build())
                            .corpEntity(CorpEntity.builder().corpId(orderQuery.getCorpId()).build())
                            .build();
                    return Mono.just(root);
                })
                .flatMapMany(addrQuery -> logisticsOrderRootQueryRepository.addr(addrQuery)
                        .flatMap(appAddrDto -> Mono
                                .just(converter.convert(appAddrDto, AppAddrResDto.class)))));
    }

}
