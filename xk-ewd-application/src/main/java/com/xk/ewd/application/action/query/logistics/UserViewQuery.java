package com.xk.ewd.application.action.query.logistics;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.logistics.LogisticsUserViewReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = LogisticsUserViewReqDto.class)})
public class UserViewQuery extends PagerQuery implements IActionQuery {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 注册手机
     */
    private String mobile;

    /**
     * 收货手机号
     */
    private String receivingMobile;

    private Long corpId;
}
