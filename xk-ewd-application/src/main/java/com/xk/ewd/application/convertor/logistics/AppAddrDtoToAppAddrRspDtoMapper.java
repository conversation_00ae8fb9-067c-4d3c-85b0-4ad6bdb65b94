package com.xk.ewd.application.convertor.logistics;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.dto.logistics.AppAddrDto;
import com.xk.ewd.interfaces.dto.rsp.logistics.AppAddrResDto;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class AppAddrDtoToAppAddrRspDtoMapper implements BaseMapper<AppAddrDto, AppAddrResDto> {

    @Override
    public AppAddrResDto convert(AppAddrDto source) {
        return convert(source, AppAddrResDto.builder().build());
    }

    @Override
    public AppAddrResDto convert(AppAddrDto source, AppAddrResDto target) {
        if (source == null) {
            return null;
        }

        return AppAddrResDto.builder().userAddressId(source.getUserAddressId())
                .consigneeName(source.getConsigneeName())
                .receivingMobile(source.getReceivingMobile()).addressSite(source.getAddressSite())
                .addressDetail(source.getAddressDetail()).build();
    }
}
