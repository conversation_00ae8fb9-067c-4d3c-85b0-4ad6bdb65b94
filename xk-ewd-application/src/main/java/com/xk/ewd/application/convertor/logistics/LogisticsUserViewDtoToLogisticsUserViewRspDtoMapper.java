package com.xk.ewd.application.convertor.logistics;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.dto.logistics.LogisticsUserViewDto;
import com.xk.ewd.interfaces.dto.rsp.logistics.LogisticsUserViewRspDto;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class LogisticsUserViewDtoToLogisticsUserViewRspDtoMapper
        implements BaseMapper<LogisticsUserViewDto, LogisticsUserViewRspDto> {

    @Override
    public LogisticsUserViewRspDto convert(LogisticsUserViewDto source) {
        return convert(source, LogisticsUserViewRspDto.builder().build());
    }

    @Override
    public LogisticsUserViewRspDto convert(LogisticsUserViewDto source,
            LogisticsUserViewRspDto target) {
        if (source == null) {
            return null;
        }

        return LogisticsUserViewRspDto.builder().userId(source.getUserId())
                .userNick(source.getUserNick()).mobile(source.getMobile())
                .consigneeName(source.getConsigneeName())
                .receivingMobile(source.getReceivingMobile())
                .userAddressId(source.getUserAddressId()).addressSite(source.getAddressSite())
                .addressDetail(source.getAddressDetail()).waitShipNum(source.getWaitShipNum())
                .build();
    }
}
