package com.xk.ewd.application.handler.query.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.ewd.application.action.query.order.OrderStatisticsQuery;
import com.xk.ewd.domain.model.order.OrderRoot;
import com.xk.ewd.domain.model.order.entity.CorpEntity;
import com.xk.ewd.domain.model.order.entity.OrderEntity;
import com.xk.ewd.domain.model.order.id.OrderIdentifier;
import com.xk.ewd.domain.repository.order.OrderRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.order.OrderStatisticsRsp;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class OrderStatisticsQueryHandler
        implements IActionQueryHandler<OrderStatisticsQuery, OrderStatisticsRsp> {

    private final OrderRootQueryRepository orderRootQueryRepository;

    private final Converter converter;

    @Override
    public Mono<OrderStatisticsRsp> execute(Mono<OrderStatisticsQuery> query) {
        return query.flatMap(orderQuery -> ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObjectRoot -> {
                    UserDataObjectEntity userDataObjectEntity =
                            userObjectRoot.getUserDataObjectEntity();
                    if (UserTypeEnum.MERCHANT_KAS.equals(userDataObjectEntity.getUserType())) {
                        orderQuery.setCorpId(userDataObjectEntity.getCorpId());
                    }
                    OrderRoot root = OrderRoot.builder()
                            .identifier(OrderIdentifier.builder().orderNo("-1").build())
                            .corpEntity(CorpEntity.builder().corpId(orderQuery.getCorpId()).build())
                            .orderEntity(OrderEntity.builder().orderType(orderQuery.getOrderType())
                                    .startTime(orderQuery.getStartTime())
                                    .endTime(orderQuery.getEndTime()).build())
                            .build();
                    return Mono.just(root);
                })
                .flatMap(orderRootQueryRepository::searchStatistics))
                .flatMap(x -> Mono.just(converter.convert(x, OrderStatisticsRsp.class)));
    }

}
