package com.xk.ewd.application.handler.query.payment;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.ewd.application.action.query.payment.EwdRefundQuery;
import com.xk.ewd.domain.repository.payment.PaymentRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.payment.RefundRsp;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Objects;

@Component
@RequiredArgsConstructor
public class EwdRefundQueryHandler implements IActionQueryHandler<EwdRefundQuery, Pagination> {

    private final PaymentRootQueryRepository paymentRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<EwdRefundQuery> query) {
        return query.flatMap(ewdRefundQuery -> {
            //创建分页
            Pagination pagination = new Pagination();
            pagination.setLimit(ewdRefundQuery.getLimit());
            pagination.setOffset(ewdRefundQuery.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(ewdRefundQuery));
            return paymentRootQueryRepository.searchRefundByPage(pagination).map(entity ->{
                return RefundRsp.builder()
                        .paymentId(entity.getPaymentId())
                        .orderNo(entity.getOrderNo())
                        .payNo(entity.getPayNo())
                        .userId(entity.getUserId())
                        .username(entity.getUsername())
                        .refundType(Objects.nonNull(entity.getRefundType())?entity.getRefundType().getCode():null)
                        .refundStatus(entity.getRefundStatus().getCode())
                        .platformType(entity.getPlatformType().getValue())
                        .payType(entity.getPayType().getCode())
                        .payTime(entity.getPayTime())
                        .refundTime(entity.getRefundTime())
                        .goodsId(entity.getGoodsId())
                        .goodsName(entity.getGoodsName())
                        .corpId(entity.getCorpId())
                        .corpName(entity.getCorpName())
                        .amount(entity.getAmount())
                        .remark(entity.getRemark())
                        .createTime(entity.getCreateTime())
                        .updateTime(entity.getUpdateTime())
                        .build();
            }).collectList().map(list->{
                pagination.setRecords(list);
                return pagination;
            });
        });
    }
}
