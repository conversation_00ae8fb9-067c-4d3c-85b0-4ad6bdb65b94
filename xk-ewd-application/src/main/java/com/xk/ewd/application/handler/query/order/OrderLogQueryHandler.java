package com.xk.ewd.application.handler.query.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.xk.ewd.application.action.query.order.OrderLogQuery;
import com.xk.ewd.domain.model.order.id.OrderIdentifier;
import com.xk.ewd.domain.repository.order.OrderLogRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.order.OrderLogRsp;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class OrderLogQueryHandler implements IActionQueryManyHandler<OrderLogQuery, OrderLogRsp> {

    private final OrderLogRootQueryRepository orderLogRootQueryRepository;

    @Override
    public Flux<OrderLogRsp> execute(Mono<OrderLogQuery> mono) {
        return mono
                .flatMapMany(v -> orderLogRootQueryRepository
                        .searchByOrderNo(OrderIdentifier.builder().orderNo(v.getOrderNo()).build()))
                .map(o -> OrderLogRsp.builder().orderNo(o.getOrderNo()).orderMsg(o.getOrderMsg())
                        .createTime(o.getCreateTime()).build());
    }
}
