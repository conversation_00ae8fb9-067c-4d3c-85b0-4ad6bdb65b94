package com.xk.ewd.application.handler.query.logistics;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.ewd.application.action.query.logistics.EwdLogisticsOrderIdQuery;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.repository.logistics.LogisticsOrderRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.logistics.EwdLogisticsOrderRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class EwdLogisticsIdQueryHandler
        implements IActionQueryHandler<EwdLogisticsOrderIdQuery, EwdLogisticsOrderRspDto> {

    private final LogisticsOrderRootQueryRepository logisticsOrderRootQueryRepository;

    private final Converter converter;

    @Override
    public Mono<EwdLogisticsOrderRspDto> execute(Mono<EwdLogisticsOrderIdQuery> query) {
        return query
                .flatMap(
                        logisticsDetailQuery -> logisticsOrderRootQueryRepository
                                .selectById(
                                        LogisticsOrderRoot.builder()
                                                .identifier(LogisticsOrderIdentifier.builder()
                                                        .logisticsOrderId(logisticsDetailQuery
                                                                .getLogisticsOrderId())
                                                        .build())
                                                .build())
                                .flatMap(dto -> Mono.just(
                                        converter.convert(dto, EwdLogisticsOrderRspDto.class))));
    }

}
