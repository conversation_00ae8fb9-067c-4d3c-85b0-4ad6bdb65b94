package com.xk.ewd.application.convertor.logistics;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.dto.logistics.LogisticsDetailDto;
import com.xk.ewd.interfaces.dto.rsp.logistics.LogisticsDetailRspDto;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class LogisticsDetailDtoToLogisticsDetailRspDtoMapper
        implements BaseMapper<LogisticsDetailDto, LogisticsDetailRspDto> {

    @Override
    public LogisticsDetailRspDto convert(LogisticsDetailDto source) {
        return convert(source, LogisticsDetailRspDto.builder().build());
    }

    @Override
    public LogisticsDetailRspDto convert(LogisticsDetailDto source, LogisticsDetailRspDto target) {
        if (source == null) {
            return null;
        }

        return LogisticsDetailRspDto.builder().trackTime(source.getTrackTime())
                .trackDescribe(source.getTrackDescribe()).build();
    }
}
