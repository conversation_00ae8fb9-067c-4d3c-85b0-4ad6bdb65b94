package com.xk.ewd.application.query;

import com.myco.framework.common.util.JsonUtil;
import com.myco.mydata.config.infrastructure.data.persistence.cfg.TSysCfgMapper;
import com.myco.mydata.config.infrastructure.data.po.cfg.TSysCfg;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.ewd.infrastructure.data.persistence.ExampleTbl2Mapper;
import com.xk.ewd.infrastructure.data.po.ExampleTbl2;
import com.xk.ewd.interfaces.query.TestQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class TestQueryServiceImpl implements TestQueryService {

    private final ExampleTbl2Mapper exampleTbl2Mapper;

    private final TSysCfgMapper testCfgMapper;

    @BusiCode
    @Override
    public Mono<Void> query(Mono<RequireSessionDto> requireSessionMono) {
        return Mono.fromCallable(() -> {
            // ExampleTbl2 test = new ExampleTbl2();
            // test.setCommodityId("1364");
            // ExampleTbl2 db = exampleTbl2Mapper.selectByCommodityId(test);
            TSysCfg test = new TSysCfg();
            test.setParamId("test");
            test.setParamName("test");
            test.setParamValue("test");
            test.setUnitType("test");
            TSysCfg t = testCfgMapper.selectByPrimaryKey(test);
            log.error("db:{}", JsonUtil.seriazileAsString(t));
            return null;
        });
    }
}
