package com.xk.ewd.application.action.query.logistics;

import java.util.Date;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.logistics.EwdLogisticsOrderReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = EwdLogisticsOrderReqDto.class)})
public class EwdLogisticsOrderQuery extends PagerQuery implements IActionQuery {

    /**
     * 订单状态 1 待发货 2 待收货 3 已完成
     */
    private Integer logisticsOrderStatus;

    /**
     * 开始发货时间
     */
    private Date startTime;

    /**
     * 结束发货时间
     */
    private Date endTime;

    /**
     * 物流订单或销售订单
     */
    private String searchOrderNo;

    /**
     * 商户ID
     */
    private Long corpId;

    /**
     * 商户名称
     */
    private String corpName;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 物流订单类型 1-商城 2-物料 3-商品 4-赠品
     */
    private Integer logisticsOrderType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String userNick;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 物流公司id
     */
    private Long logisticsCorpId;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 全部-1 待付款-2 待发货-3 待收货-4 已完成-5 已取消-6
     */
    private Integer orderListStatus;

    /**
     * 催发货状态 ：0-未催发 1-已催发
     */
    private Integer remindShippingStatus;

    private Integer orderStatus;

    private Integer refundStatus;

    /**
     * 商品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;

    /**
     * 收货地址ID
     */
    private String userAddressId;

    /**
     * 收货地址
     */
    private String address;
}
