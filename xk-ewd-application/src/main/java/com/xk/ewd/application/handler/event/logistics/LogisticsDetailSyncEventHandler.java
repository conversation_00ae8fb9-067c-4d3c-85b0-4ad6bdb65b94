package com.xk.ewd.application.handler.event.logistics;


import java.text.ParseException;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.domain.event.logistics.LogisticsDetailSyncEvent;
import com.xk.ewd.domain.event.logistics.LogisticsDetailUpdateEvent;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.model.logistics.valobj.TrajectoryValueObject;
import com.xk.ewd.domain.repository.logistics.LogisticsDetailRootQueryRepository;
import com.xk.ewd.domain.repository.logistics.LogisticsDetailRootRepository;
import com.xk.tp.interfaces.dto.req.logistics.LogisticsReqDto;
import com.xk.tp.interfaces.dto.res.logistics.LogisticsDetailRspDto;
import com.xk.tp.interfaces.query.logistics.LogisticsQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsDetailSyncEventHandler
        extends AbstractEventVerticle<LogisticsDetailSyncEvent> {

    private final LogisticsDetailRootRepository logisticsDetailRootRepository;

    private final LogisticsDetailRootQueryRepository logisticsDetailRootQueryRepository;

    private final LogisticsQueryService logisticsQueryService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<LogisticsDetailSyncEvent> event) {
        return event
                .flatMap(
                        logisticsDetailSyncEvent -> logisticsQueryService
                                .detail(Mono.just(LogisticsReqDto.builder()
                                        .logisticsNo(logisticsDetailSyncEvent.getLogisticsNo())
                                        .logisticsCorpName(
                                                logisticsDetailSyncEvent.getLogisticsCorpName())
                                        .receivingMobile(
                                                logisticsDetailSyncEvent.getReceivingMobile())
                                        .build()))
                                .flatMap(logisticsDetailRspDtos -> logisticsDetailRootQueryRepository.selectMaxTimeById(LogisticsOrderRoot.builder()
                                                .identifier(LogisticsOrderIdentifier.builder()
                                                        .logisticsOrderId(logisticsDetailSyncEvent
                                                                .getLogisticsOrderId())
                                                        .build())
                                                .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                                        .logisticsOrderId(logisticsDetailSyncEvent
                                                                .getLogisticsOrderId())
                                                        .build())
                                        .build()).flatMap(traceTime -> {
                                    List<LogisticsDetailRspDto> list = logisticsDetailRspDtos.stream().filter(logisticsDetailRspDto -> {
                                        String trackTime = logisticsDetailRspDto.getTrackTime();
                                        try {
                                            Date date = DateUtils.parseDate(trackTime, "yyyy-MM-dd HH:mm:ss");
                                            return date != null && !date.after(traceTime);
                                        } catch (ParseException e) {
                                            throw new RuntimeException(e);
                                        }
                                    }).toList();
                                    if (list.isEmpty()) {
                                        return Mono.just(logisticsDetailRspDtos);
                                    }
                                return logisticsDetailRootRepository
                                        .save(LogisticsOrderRoot.builder()
                                                .identifier(LogisticsOrderIdentifier.builder()
                                                        .logisticsOrderId(logisticsDetailSyncEvent
                                                                .getLogisticsOrderId())
                                                        .build())
                                                .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                                        .logisticsOrderId(logisticsDetailSyncEvent
                                                                .getLogisticsOrderId())
                                                        .build())
                                                .trajectoryValueObjectList(list
                                                        .stream().map(logisticsDetailRspDto -> {
                                                            try {
                                                                return TrajectoryValueObject
                                                                        .builder()
                                                                        .trackDescribe(
                                                                                logisticsDetailRspDto
                                                                                        .getTrackDescribe())
                                                                        .trackTime(
                                                                                DateUtils.parseDate(
                                                                                        logisticsDetailRspDto
                                                                                                .getTrackTime(),
                                                                                        "yyyy-MM-dd HH:mm:ss"))
                                                                        .trackType(
                                                                                logisticsDetailRspDto
                                                                                        .getTrackType())
                                                                        .build();
                                                            } catch (ParseException e) {
                                                                throw new RuntimeException(e);
                                                            }
                                                        }).toList())
                                                .build());
                                        }).thenReturn(logisticsDetailRspDtos))
                                .flatMap(logisticsDetailRspDtos -> {
                                    // 发送物流轨迹 更新事件
                                    EventRoot eventRoot = EventRoot.builder().domainEvent(
                                            LogisticsDetailUpdateEvent.builder().identifier(
                                                    EventRoot.getCommonsDomainEventIdentifier(
                                                            LogisticsDetailUpdateEvent.class))
                                                    .logisticsOrderId(logisticsDetailSyncEvent
                                                            .getLogisticsOrderId())
                                                    .logisticsCorpName(logisticsDetailSyncEvent
                                                            .getLogisticsCorpName())
                                                    .logisticsNo(logisticsDetailSyncEvent
                                                            .getLogisticsNo())
                                                    .logisticsOrderType(logisticsDetailSyncEvent
                                                            .getLogisticsOrderType())
                                                    .receivingMobile(logisticsDetailSyncEvent
                                                            .getReceivingMobile())
                                                    .trajectoryValueObject(JSONObject
                                                            .toJSONString(logisticsDetailRspDtos))
                                                    .build())
                                            .isQueue(false).build();
                                    return eventRootService.publisheByMono(eventRoot).then();

                                }));
    }
}
