package com.xk.ewd.application.handler.query.logistics;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.ewd.application.action.query.logistics.AppSendGoodsQuery;
import com.xk.ewd.domain.dto.logistics.SendGoodsDto;
import com.xk.ewd.domain.repository.logistics.LogisticsGoodsRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.logistics.AppSendGoodsRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 根据用户分组查询
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AppSendGoodsQueryHandler
        implements IActionQueryManyHandler<AppSendGoodsQuery, AppSendGoodsRspDto> {

    private final LogisticsGoodsRootQueryRepository logisticsGoodsRootQueryRepository;

    private final Converter converter;

    @Override
    public Flux<AppSendGoodsRspDto> execute(Mono<AppSendGoodsQuery> query) {
        return query.flatMapMany(orderQuery -> ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObjectRoot -> {
                    UserDataObjectEntity userDataObjectEntity =
                            userObjectRoot.getUserDataObjectEntity();
                    if (UserTypeEnum.MERCHANT_KAS.equals(userDataObjectEntity.getUserType())) {
                        orderQuery.setCorpId(userDataObjectEntity.getCorpId());
                    }
                    return Mono.just(SendGoodsDto.builder().corpId(orderQuery.getCorpId())
                            .logisticsOrderIdList(orderQuery.getLogisticsOrderIdList()).build());
                }).flatMapMany(
                        sendGoodsDto -> logisticsGoodsRootQueryRepository
                                .sendGoodsQuery(sendGoodsDto)
                                .flatMap(appSendGoodsDto -> Mono.just(AppSendGoodsRspDto.builder()
                                        .goodsId(appSendGoodsDto.getGoodsId())
                                        .goodsName(appSendGoodsDto.getGoodsName())
                                        .createTime(appSendGoodsDto.getCreateTime())
                                        .goodsImages(appSendGoodsDto.getGoodsImages())
                                        .countNum(appSendGoodsDto.getCountNum()).build()))));
    }

}
