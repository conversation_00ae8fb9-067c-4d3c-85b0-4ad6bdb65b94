package com.xk.ewd.application.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author: killer
 **/
@Configuration
@ComponentScan({"com.xk.ewd.application.convertor"
        , "com.xk.ewd.application.query"
        , "com.xk.ewd.application.service"
        , "com.xk.ewd.application.handler"
        , "com.xk.ewd.application.task"})
public class XkEwdApplicationConfig {
}
