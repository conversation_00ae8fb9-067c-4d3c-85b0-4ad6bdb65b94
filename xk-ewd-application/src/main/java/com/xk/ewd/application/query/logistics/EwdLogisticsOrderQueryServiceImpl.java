package com.xk.ewd.application.query.logistics;

import java.util.List;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.ewd.application.action.query.logistics.*;
import com.xk.ewd.interfaces.dto.req.logistics.*;
import com.xk.ewd.interfaces.dto.rsp.logistics.AppAddrResDto;
import com.xk.ewd.interfaces.dto.rsp.logistics.AppSendGoodsRspDto;
import com.xk.ewd.interfaces.dto.rsp.logistics.EwdLogisticsOrderRspDto;
import com.xk.ewd.interfaces.dto.rsp.logistics.LogisticsDetailRspDto;
import com.xk.ewd.interfaces.query.logistics.EwdLogisticsOrderQueryService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class EwdLogisticsOrderQueryServiceImpl implements EwdLogisticsOrderQueryService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;

    private final Converter converter;

    @BusiCode
    @Override
    public Mono<Pagination> selectByLogisticsOrder(Mono<EwdLogisticsOrderReqDto> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, EwdLogisticsOrderQuery.class,
                Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> ptsGroup(Mono<AppPtsGroupReqDto> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, PtsGroupQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<List<AppAddrResDto>> addr(Mono<AppAddrReqDto> dtoMono) {
        return queryManyDispatcher.executeQuery(dtoMono, AppAddrQuery.class, AppAddrResDto.class)
                .collectList();
    }

    @BusiCode
    @Override
    public Mono<Pagination> giftCorpsOrder(Mono<OrderSearchByGiftReqDto> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, GiftCorpsQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> importDetail(Mono<ImportDetailReqDto> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, ImportDetailQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> userViewCorp(Mono<LogisticsUserViewReqDto> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, UserViewQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<List<LogisticsDetailRspDto>> detail(Mono<LogisticsDetailReqDto> mono) {
        return queryManyDispatcher
                .executeQuery(mono, LogisticsDetailQuery.class, LogisticsDetailRspDto.class)
                .collectList();
    }

    @BusiCode
    @Override
    public Mono<EwdLogisticsOrderRspDto> selectById(Mono<LogisticsDetailReqDto> mono) {
        return queryDispatcher.executeQuery(mono, EwdLogisticsOrderIdQuery.class,
                EwdLogisticsOrderRspDto.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> ptsGoodsGroup(Mono<AppPtsGoodsGroupReqDto> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, PtsGoodsGroupQuery.class, Pagination.class);

    }

    @BusiCode
    @Override
    public Mono<List<AppSendGoodsRspDto>> sendGoods(Mono<AppSendGoodsReqDto> dtoMono) {
        return queryManyDispatcher
                .executeQuery(dtoMono, AppSendGoodsQuery.class, AppSendGoodsRspDto.class)
                .collectList();
    }
}
