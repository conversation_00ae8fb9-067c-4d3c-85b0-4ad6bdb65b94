package com.xk.ewd.application.handler.query.order;

import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.domain.commons.user.UserConfigTypeEnum;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.ewd.application.action.query.order.EwdOrderQuery;
import com.xk.ewd.domain.repository.order.OrderRootQueryRepository;
import com.xk.ewd.enums.order.OrderListStatusEnum;
import com.xk.ewd.interfaces.dto.rsp.order.EwdOrderRspDto;
import com.xk.infrastructure.cache.dao.corp.CorpBlockDao;
import com.xk.infrastructure.cache.key.corp.CorpBlockKey;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class EwdQueryHandler implements IActionQueryHandler<EwdOrderQuery, Pagination> {

    private final OrderRootQueryRepository orderRootQueryRepository;
    private final CorpBlockDao corpBlockDao;
    private final SelectorRootService selectorRootService;
    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<EwdOrderQuery> query) {
        Function<EwdOrderQuery, Mono<Pagination>> getReq = q -> {
            if (q.getOrderListStatus() != null) {
                OrderListStatusEnum orderListStatusEnum =
                        OrderListStatusEnum.getByCode(q.getOrderListStatus());
                q.setOrderStatus(orderListStatusEnum.getOrderStatus());
                q.setLogisticsOrderStatus(orderListStatusEnum.getLogisticsOrderStatus());
                q.setRefundStatus(orderListStatusEnum.getRefundStatus());
            }
            Pagination pagination = new Pagination();
            pagination.setLimit(q.getLimit());
            pagination.setOffset(q.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(q));
            return Mono.just(pagination);
        };

        return query.flatMap(orderQuery -> ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObjectRoot -> {
                    UserDataObjectEntity userDataObjectEntity =
                            userObjectRoot.getUserDataObjectEntity();
                    if (UserTypeEnum.MERCHANT_KAS.equals(userDataObjectEntity.getUserType())) {
                        orderQuery.setCorpId(userDataObjectEntity.getCorpId());
                    }
                    return Mono.just(orderQuery).flatMap(getReq)
                            .flatMap(pagination -> orderRootQueryRepository.selectList(pagination)
                                    .flatMap(orderRoot -> Mono.just(
                                            converter.convert(orderRoot, EwdOrderRspDto.class)))
                                    .flatMap(ewdOrderRspDto -> {
                                        if (UserTypeEnum.MERCHANT_KAS
                                                .equals(userDataObjectEntity.getUserType())) {
                                            Double value = corpBlockDao.getValue(
                                                    CorpBlockKey.builder()
                                                            .corpId(userDataObjectEntity
                                                                    .getCorpId())
                                                            .build(),
                                                    ewdOrderRspDto.getUserId());
                                            ewdOrderRspDto.setBlockStatus(value != null
                                                    ? CommonStatusEnum.ENABLE.getCode()
                                                    : CommonStatusEnum.DISABLE.getCode());
                                        } else {
                                            return selectorRootService
                                                    .getUserObject(ewdOrderRspDto.getUserId())
                                                    .flatMap(user -> {
                                                        String forbidDeal = user.getUserConfig(
                                                                UserConfigTypeEnum.IS_FORBID_DEAL);
                                                        ewdOrderRspDto.setBlockStatus((StringUtils
                                                                .isNotEmpty(forbidDeal)
                                                                && CommonStatusEnum.ENABLE.getCode()
                                                                        .toString()
                                                                        .equals(forbidDeal))
                                                                                ? CommonStatusEnum.ENABLE
                                                                                        .getCode()
                                                                                : CommonStatusEnum.DISABLE
                                                                                        .getCode());
                                                        return Mono.just(ewdOrderRspDto);
                                                    });
                                        }
                                        return Mono.just(ewdOrderRspDto);
                                    }).collectList().flatMap(list -> {
                                        pagination.setRecords(list);
                                        return Mono.just(pagination);
                                    }));
                }));
    }

}
