package com.xk.ewd.application.handler.event.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.domain.service.order.OrderAdapterService;
import com.xk.order.domain.event.order.OrderUpdateEvent;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderUpdateEventHandler extends AbstractEventVerticle<OrderUpdateEvent> {

    private final Converter converter;

    private final OrderAdapterService orderAdapterService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderUpdateEvent> event) {
        return event.flatMap(orderUpdateEvent -> orderAdapterService.update(
                Mono.just(StringIdentifier.builder().id(orderUpdateEvent.getOrderNo()).build())));

    }
}
