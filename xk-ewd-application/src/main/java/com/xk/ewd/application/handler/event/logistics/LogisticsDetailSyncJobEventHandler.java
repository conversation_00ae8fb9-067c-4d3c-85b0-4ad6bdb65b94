package com.xk.ewd.application.handler.event.logistics;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.domain.event.logistics.LogisticsDetailSyncEvent;
import com.xk.ewd.domain.event.logistics.LogisticsDetailSyncJobEvent;
import com.xk.ewd.domain.repository.logistics.LogisticsOrderRootQueryRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsDetailSyncJobEventHandler
        extends AbstractEventVerticle<LogisticsDetailSyncJobEvent> {

    private final LogisticsOrderRootQueryRepository logisticsOrderRootQueryRepository;

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    private final Integer PAGE_SIZE = 1000;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<LogisticsDetailSyncJobEvent> event) {
        return logisticsOrderRootQueryRepository.getWeekTotalRecords().flatMap(total -> {
            int totalPages = (int) Math.ceil((double) total / PAGE_SIZE);
            return Flux.range(1, totalPages).flatMap(page -> {
                int finalPage = page;
                Pagination pagination = new Pagination();
                pagination.setLimit(PAGE_SIZE);
                pagination.setOffset((finalPage - 1) * PAGE_SIZE);
                return logisticsOrderRootQueryRepository.getWeekRecords(pagination)
                        .flatMap(logisticsOrderRoot -> {
                            // 发送物流轨迹 更新事件
                            EventRoot eventRoot = EventRoot.builder()
                                    .domainEvent(LogisticsDetailSyncEvent.builder()
                                            .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                    LogisticsDetailSyncEvent.class))
                                            .logisticsOrderId(
                                                    logisticsOrderRoot.getLogisticsOrderEntity()
                                                            .getLogisticsOrderId())
                                            .logisticsCorpName(
                                                    logisticsOrderRoot.getLogisticsOrderEntity()
                                                            .getLogisticsCorpName())
                                            .logisticsOrderType(logisticsOrderRoot.getLogisticsOrderEntity().getLogisticsOrderType())
                                            .logisticsNo(logisticsOrderRoot
                                                    .getLogisticsOrderEntity().getLogisticsNo())
                                            .receivingMobile(logisticsOrderRoot.getOrderAddrEntity()
                                                    .getReceivingMobile())
                                            .build())
                                    .isQueue(false).build();
                            return eventRootService.publisheByMono(eventRoot).then();
                        });
            }).then();
        });
    }
}
