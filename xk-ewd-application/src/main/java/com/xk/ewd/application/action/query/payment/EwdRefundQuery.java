package com.xk.ewd.application.action.query.payment;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.payment.EwdRefundReq;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = EwdRefundReq.class)})
public class EwdRefundQuery extends PagerQuery implements IActionQuery {
    /**
     * 支付开始时间
     */
    private Date startPayTime;

    /**
     * 支付结束时间
     */
    private Date endPayTime;

    /**
     * 退款开始时间
     */
    private Date startRefundTime;

    /**
     * 退款结束时间
     */
    private Date endRefundTime;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 商品编号
     */
    private String goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商户id
     */
    private Long corpId;

    /**
     * 商户名
     */
    private String corpName;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 支付渠道
     */
    private Integer payType;

    /**
     * 退款状态
     */
    private Integer refundStatus;

    /**
     * 失败类型
     */
    private Integer refundType;
}
