package com.xk.ewd.application.action.query.goods;

import java.util.Date;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.goods.SearchGoodsMerchantReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers({@AutoMapper(target = SearchGoodsMerchantReqDto.class, convertGenerate = false)})
public class MerchantSearchQuery extends PagerQuery implements IActionQuery {

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 上下架状态
     */
    private Integer listingStatus;

    /**
     * 商品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;

    /**
     * 审核状态 1-审核中 2-已审核 3-未通过
     */
    private Integer auditStatus;

    /**
     * 完成状态:1-已完成,0-未完成
     */
    private Integer finishStatus;

    /**
     * 售罄状态
     */
    private Integer soldOutStatus;

    /**
     * 未售罄或已退款
     */
    private Integer notSoldOutOrRefund;

    /**
     * 退款状态
     */
    private Integer refundStatus;

    /**
     * 回收状态
     */
    private Integer recycleStatus;

    private Integer showStatus;

    private Integer publicityStatus;

    private Integer reportStatus;

    private Integer soldStatus;

    /**
     * 收藏卡名称
     */
    private String collectibleCardName;

    private Long collectibleCardId;

    private Date actualUpStartTime;

    private Date actualUpEndTime;

    private Integer blockType;

    /**
     * 商品类型
     */
    private Integer goodsType;
}
