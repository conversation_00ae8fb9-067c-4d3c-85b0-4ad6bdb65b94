package com.xk.ewd.application.handler.query.logistics;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.xk.ewd.application.action.query.logistics.LogisticsDetailQuery;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.repository.logistics.LogisticsDetailRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.logistics.LogisticsDetailRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 根据用户分组查询
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsDetailQueryHandler
        implements IActionQueryManyHandler<LogisticsDetailQuery, LogisticsDetailRspDto> {

    private final LogisticsDetailRootQueryRepository logisticsDetailRootQueryRepository;

    private final Converter converter;

    @Override
    public Flux<LogisticsDetailRspDto> execute(Mono<LogisticsDetailQuery> query) {
        return query
                .flatMapMany(
                        logisticsDetailQuery -> logisticsDetailRootQueryRepository
                                .selectById(
                                        LogisticsOrderRoot.builder()
                                                .identifier(LogisticsOrderIdentifier.builder()
                                                        .logisticsOrderId(logisticsDetailQuery
                                                                .getLogisticsOrderId())
                                                        .build())
                                                .build())
                                .flatMap(dto -> Mono.just(
                                        converter.convert(dto, LogisticsDetailRspDto.class))));

    }

}
