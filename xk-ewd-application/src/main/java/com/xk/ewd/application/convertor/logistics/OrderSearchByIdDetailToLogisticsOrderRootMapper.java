package com.xk.ewd.application.convertor.logistics;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.alibaba.fastjson2.JSONObject;
import com.xk.acct.enums.user.DeleteStatusEnum;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.CorpEntity;
import com.xk.ewd.domain.model.logistics.entity.OrderAddrEntity;
import com.xk.ewd.domain.model.logistics.entity.OrderEntity;
import com.xk.ewd.domain.model.logistics.entity.UserEntity;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.model.logistics.valobj.GoodsValueObject;
import com.xk.order.interfaces.dto.rsp.order.OrderGoodsInfoRsp;
import com.xk.order.interfaces.dto.rsp.order.OrderSearchByIdDetailRsp;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class OrderSearchByIdDetailToLogisticsOrderRootMapper
        implements BaseMapper<OrderSearchByIdDetailRsp, LogisticsOrderRoot> {


    @Override
    public LogisticsOrderRoot convert(OrderSearchByIdDetailRsp source) {
        return convert(source,
                LogisticsOrderRoot.builder()
                        .identifier(
                                LogisticsOrderIdentifier.builder().logisticsOrderId(-1L).build())
                        .build());
    }

    @Override
    public LogisticsOrderRoot convert(OrderSearchByIdDetailRsp source, LogisticsOrderRoot target) {
        if (source == null) {
            return null;
        }

        return LogisticsOrderRoot.builder()
                .identifier(LogisticsOrderIdentifier.builder().logisticsOrderId(-1L).build())
                .corpEntity(CorpEntity.builder().corpId(source.getCorpId())
                        .corpName(source.getCorpName()).corpLogo(source.getCorpLogo()).build())
                .orderAddrEntity(OrderAddrEntity.builder().userAddressId(source.getUserAddressId())
                        .addressDetail(source.getAddressDetail())
                        .addressSite(source.getAddressSite())
                        .consigneeName(source.getConsigneeName())
                        .receivingMobile(source.getReceivingMobile()).build())
                .orderEntity(OrderEntity.builder().orderNo(source.getOrderNo())
                        .corpDicountAmount(source.getCorpDiscountAmount())
                        .couponAmount(source.getCouponAmount()).orderStatus(source.getOrderStatus())
                        .discountAmount(source.getDiscountAmount()).orderType(source.getOrderType())
                        .payNo(source.getPayNo())
                        .freeQuotaDiscountAmount(source.getFreeQuotaDiscountAmount())
                        .createTime(source.getCreateTime())
                        .orderTotalBuyCount(source.getOrderTotalBuyCount())
                        .payType(source.getPayType()).payTime(source.getPayTime())
                        .totalAmount(source.getTotalAmount()).shippingFee(source.getShippingFee())
                        .otherDicountAmount(source.getOtherDiscountAmount())
                        .remindShippingStatus(source.getRemindShippingStatus())
                        .deleted(DeleteStatusEnum.NOT_DELETE.getCode())
                        .refundStatus(source.getRefundStatus())
                        .payAmount(source.getPayAmount()).refundStatus(source.getRefundStatus())
                        .shipTotalCount(source.getShipTotalCount())
                        .remindShippingStatus(source.getRemindShippingStatus())
                        .refundStatus(source.getRefundStatus()).build())
                .userEntity(UserEntity.builder().userId(source.getUserId())
                        .mobile(source.getMobile()).userNick(source.getUserNick())
                        .userLogo(source.getPicId()).build())
                .goodsValueObject(GoodsValueObject.builder()
                        .goodsInfo(JSONObject.toJSONString(source.getGoodsInfo()))
                        .giftInfo(JSONObject.toJSONString(source.getGiftInfo()))
                        .goodsInfo(JSONObject.toJSONString(source.getGoodsInfo()))
                        .giftInfo(JSONObject.toJSONString(source.getGiftInfo()))
                        .goodsName(getName(source.getGoodsInfo()))
                        .goodsId(getGoodsId(source.getGoodsInfo()))
                        .productType(getProductType(source.getGoodsInfo()))
                        .build())
                .build();
    }

    private String getName(List<OrderGoodsInfoRsp> goodsInfoRsps) {
        if (CollectionUtils.isEmpty(goodsInfoRsps)) {
            return null;
        }

        return JSONObject
                .toJSONString(goodsInfoRsps.stream().map(OrderGoodsInfoRsp::getGoodsName).toList());
    }

    private String getGoodsId(List<OrderGoodsInfoRsp> goodsInfoRsps) {
        if (CollectionUtils.isEmpty(goodsInfoRsps)) {
            return null;
        }

        return JSONObject
                .toJSONString(goodsInfoRsps.stream().map(OrderGoodsInfoRsp::getGoodsId).toList());
    }

    private String getProductType(List<OrderGoodsInfoRsp> goodsInfoRsps) {
        if (CollectionUtils.isEmpty(goodsInfoRsps)) {
            return null;
        }

        return JSONObject.toJSONString(
                goodsInfoRsps.stream().map(OrderGoodsInfoRsp::getProductType).toList());
    }
}
