package com.xk.ewd.application.handler.command.log;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.ewd.application.action.command.log.CreateOrderLogCommand;
import com.xk.ewd.domain.repository.order.OrderLogRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class CreateOrderLogCommandHandler
        implements IActionCommandHandler<CreateOrderLogCommand, Void> {

    private final Converter converter;
    private final OrderLogRootRepository orderLogRootRepository;

    @Override
    public Mono<Void> execute(Mono<CreateOrderLogCommand> mono) {
        return mono.flatMap(
                command -> command.buildRoot(converter).flatMap(orderLogRootRepository::save));
    }
}
