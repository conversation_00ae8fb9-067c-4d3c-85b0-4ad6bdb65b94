package com.xk.ewd.application.handler.event.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.application.action.command.log.CreateOrderLogCommand;
import com.xk.ewd.enums.order.OrderLogMsgEnum;
import com.xk.order.domain.event.order.OrderPaidEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPaidEventHandler extends AbstractEventVerticle<OrderPaidEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderPaidEvent> mono) {
        return mono.flatMap(event -> commandDispatcher.executeCommand(
                Mono.just(new CreateOrderLogCommand()), CreateOrderLogCommand.class, command -> {
                    command.setOrderNo(event.getOrderNo());
                    command.setOrderLogMsgEnum(OrderLogMsgEnum.PAID);
                    command.setReason("订单支付事件");
                    command.setOrderType(event.getOrderType().getCode());
                    command.buildCreate(event.getUserId());
                    return command;
                })).onErrorResume(throwable -> {
                    log.error("处理订单支付事件异常", throwable);
                    return Mono.error(throwable);
                });
    }
}
