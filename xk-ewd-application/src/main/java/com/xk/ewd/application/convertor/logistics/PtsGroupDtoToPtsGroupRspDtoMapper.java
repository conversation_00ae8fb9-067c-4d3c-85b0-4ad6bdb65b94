package com.xk.ewd.application.convertor.logistics;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.dto.logistics.PtsGroupDto;
import com.xk.ewd.interfaces.dto.rsp.logistics.PtsGroupRspDto;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class PtsGroupDtoToPtsGroupRspDtoMapper implements BaseMapper<PtsGroupDto, PtsGroupRspDto> {

    @Override
    public PtsGroupRspDto convert(PtsGroupDto source) {
        return convert(source, PtsGroupRspDto.builder().build());
    }

    @Override
    public PtsGroupRspDto convert(PtsGroupDto source, PtsGroupRspDto target) {
        if (source == null) {
            return null;
        }

        return PtsGroupRspDto.builder().userId(source.getUserId()).userNick(source.getUserNick())
                .userLogo(source.getUserLogo()).countNum(source.getCountNum()).build();
    }
}
