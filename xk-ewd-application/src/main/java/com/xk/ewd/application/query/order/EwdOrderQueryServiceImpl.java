package com.xk.ewd.application.query.order;

import java.util.List;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.ewd.application.action.query.order.EwdOrderQuery;
import com.xk.ewd.application.action.query.order.OrderLogQuery;
import com.xk.ewd.application.action.query.order.OrderStatisticsQuery;
import com.xk.ewd.interfaces.dto.req.order.EwdOrderReq;
import com.xk.ewd.interfaces.dto.req.order.OrderNoRequireReq;
import com.xk.ewd.interfaces.dto.req.order.OrderStatisticsReq;
import com.xk.ewd.interfaces.dto.rsp.order.OrderLogRsp;
import com.xk.ewd.interfaces.dto.rsp.order.OrderStatisticsRsp;
import com.xk.ewd.interfaces.query.order.EwdOrderQueryService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class EwdOrderQueryServiceImpl implements EwdOrderQueryService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;

    private final Converter converter;

    @BusiCode
    @Override
    public Mono<Pagination> selectByOrder(Mono<EwdOrderReq> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, EwdOrderQuery.class, Pagination.class);

    }

    @BusiCode
    @Override
    public Mono<OrderStatisticsRsp> searchStatistics(Mono<OrderStatisticsReq> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, OrderStatisticsQuery.class,
                OrderStatisticsRsp.class);
    }

    @BusiCode
    @Override
    public Mono<List<OrderLogRsp>> searchOrderLog(Mono<OrderNoRequireReq> mono) {
        return queryManyDispatcher.executeQuery(mono, OrderLogQuery.class, OrderLogRsp.class)
                .collectList();
    }
}
