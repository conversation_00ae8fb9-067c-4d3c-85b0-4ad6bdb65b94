package com.xk.ewd.application.action.query.logistics;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.logistics.OrderSearchByGiftReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = OrderSearchByGiftReqDto.class)})
public class GiftCorpsQuery extends PagerQuery implements IActionQuery {

    /**
     * 订单状态：1-待发货 2-待收货 3-已完成
     */
    private Integer logisticsOrderStatus;

    /**
     * 物流订单类型 1-商城 2-物料 3-商品 4-赠品
     */
    private Integer logisticsOrderType;

    /**
     * 搜索名
     */
    private String searchName;

    /**
     * 卡设名称
     */
    private String corpName;

    private Long userId;
}
