package com.xk.ewd.application.handler.event.logistics;


import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.application.action.command.log.CreateOrderLogCommand;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.ewd.domain.model.logistics.entity.OrderEntity;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.service.logistics.LogisticsOrderAdapterService;
import com.xk.ewd.enums.order.OrderLogMsgEnum;
import com.xk.order.domain.event.logistics.LogisticsOrderUpdateEvent;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsOrderUpdateEventHandler
        extends AbstractEventVerticle<LogisticsOrderUpdateEvent> {

    private final LogisticsOrderAdapterService logisticsOrderAdapterService;

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public Mono<Void> handle(Mono<LogisticsOrderUpdateEvent> event) {
        return event
                .flatMap(createEvent -> logisticsOrderAdapterService
                        .create(Mono.just(LogisticsOrderRoot.builder()
                                .identifier(LogisticsOrderIdentifier
                                        .builder()
                                        .logisticsOrderId(createEvent.getLogisticsOrderId())
                                        .build())
                                .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                        .logisticsOrderId(createEvent.getLogisticsOrderId())
                                        .logisticsOrderStatus(
                                                LogisticsOrderStatusEnum.TO_BE_SHIPPED.getCode())
                                        .logisticsOrderType(
                                                createEvent.getLogisticsOrderType().getCode())
                                        .build())
                                .orderEntity(OrderEntity.builder().orderNo(createEvent.getOrderNo())
                                        .giftAddr(createEvent.getGiftAddr()).build())
                                .build()))
                        .thenReturn(createEvent))
                .flatMap(createEvent -> commandDispatcher
                        .executeCommand(Mono.just(new CreateOrderLogCommand()),
                                CreateOrderLogCommand.class, command -> {
                                    command.setOrderNo(createEvent.getOrderNo());
                                    command.setOrderLogMsgEnum(OrderLogMsgEnum.CREATE_LOGISTICS);
                                    command.setReason("物流订单更新事件");
                                    command.buildCreate(createEvent.getUserId());
                                    return command;
                                })
                        .onErrorResume(throwable -> {
                            log.error("处理物流订单更新事件异常", throwable);
                            return Mono.error(throwable);
                        }));
    }
}
