package com.xk.ewd.application.convertor.logistics;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.interfaces.dto.rsp.logistics.ImportDetailRspDto;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class LogisticsOrderRootToImportDetailRspDtoMapper
        implements BaseMapper<LogisticsOrderRoot, ImportDetailRspDto> {

    @Override
    public ImportDetailRspDto convert(LogisticsOrderRoot source) {
        return convert(source, ImportDetailRspDto.builder().build());
    }

    @Override
    public ImportDetailRspDto convert(LogisticsOrderRoot source, ImportDetailRspDto target) {
        if (source == null) {
            return null;
        }

        return ImportDetailRspDto.builder().orderNo(source.getOrderEntity().getOrderNo())
                .logisticsCorpName(source.getLogisticsOrderEntity().getLogisticsCorpName())
                .logisticsNo(source.getLogisticsOrderEntity().getLogisticsNo())
                .logisticsOrderStatus(source.getLogisticsOrderEntity().getLogisticsOrderStatus())
                .errRemark(source.getLogisticsOrderEntity().getErrRemark()).build();
    }
}
