package com.xk.ewd.application.action.query.logistics;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.ewd.interfaces.dto.req.logistics.LogisticsDetailReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = LogisticsDetailReqDto.class)})
public class LogisticsDetailQuery implements IActionQueryMany {

    /**
     * 物流订单ID
     */
    private Long logisticsOrderId;

}
