package com.xk.ewd.application.action.query.logistics;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.logistics.AppPtsGoodsGroupReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = AppPtsGoodsGroupReqDto.class)})
public class PtsGoodsGroupQuery extends PagerQuery implements IActionQuery {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String userNick;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 收货手机号
     */
    private String mobile;

    /**
     * 物流订单类型 1-商城 2-物料 3-商品 4-赠品
     */
    private Integer logisticsOrderType;

    /**
     * 收获地址ID
     */
    private Long addressId;

    /**
     * 催发货状态：0-未催发 1-已催发
     */
    private Integer remindShippingStatus;

    /**
     * 订单状态 1、待发货2、待收货3、已收货
     */
    private Integer logisticsOrderStatus;

    /**
     * 商品名称
     */
    private String goodsName;

    private Long corpId;

}
