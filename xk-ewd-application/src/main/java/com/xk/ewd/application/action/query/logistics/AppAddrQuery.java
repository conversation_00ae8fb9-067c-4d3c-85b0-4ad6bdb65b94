package com.xk.ewd.application.action.query.logistics;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.ewd.interfaces.dto.req.logistics.AppAddrReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers(@AutoMapper(target = AppAddrReqDto.class, convertGenerate = false))
public class AppAddrQuery implements IActionQueryMany {

    /**
     * 用户ID
     */
    private Long userId;

    private Long corpId;

}
