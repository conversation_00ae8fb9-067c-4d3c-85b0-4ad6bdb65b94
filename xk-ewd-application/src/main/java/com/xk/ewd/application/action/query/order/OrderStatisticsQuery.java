package com.xk.ewd.application.action.query.order;

import java.util.Date;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.order.OrderStatisticsReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = OrderStatisticsReq.class)})
public class OrderStatisticsQuery implements IActionQuery {

    /**
     * 订单类型 1-商城 2-物料 3-商品
     */
    private Integer orderType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    private Long corpId;
}
