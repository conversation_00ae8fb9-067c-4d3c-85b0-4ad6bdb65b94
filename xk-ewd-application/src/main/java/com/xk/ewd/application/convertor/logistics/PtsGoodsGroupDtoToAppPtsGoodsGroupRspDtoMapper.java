package com.xk.ewd.application.convertor.logistics;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.dto.logistics.PtsGoodsGroupDto;
import com.xk.ewd.interfaces.dto.rsp.logistics.AppPtsGoodsGroupRspDto;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class PtsGoodsGroupDtoToAppPtsGoodsGroupRspDtoMapper
        implements BaseMapper<PtsGoodsGroupDto, AppPtsGoodsGroupRspDto> {

    @Override
    public AppPtsGoodsGroupRspDto convert(PtsGoodsGroupDto source) {
        return convert(source, AppPtsGoodsGroupRspDto.builder().build());
    }

    @Override
    public AppPtsGoodsGroupRspDto convert(PtsGoodsGroupDto source, AppPtsGoodsGroupRspDto target) {
        if (source == null) {
            return null;
        }

        return AppPtsGoodsGroupRspDto.builder().goodsId(source.getGoodsId())
                .goodsName(source.getGoodsName()).createTime(source.getCreateTime()).build();
    }
}
