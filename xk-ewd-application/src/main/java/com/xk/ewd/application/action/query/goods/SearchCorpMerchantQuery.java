package com.xk.ewd.application.action.query.goods;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.ewd.interfaces.dto.req.goods.SearchCorpMerchantReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers({@AutoMapper(target = SearchCorpMerchantReqDto.class, convertGenerate = false)})
public class SearchCorpMerchantQuery extends PagerQuery implements IActionQuery {

    private String searchName;

    private Integer goodsType;

    private Long corpInfoId;

    private Integer soldOutStatus;

    private Integer soldStatus;

    private Integer finishStatus;

    private Integer listingStatus;

    private Integer showStatus;

}
