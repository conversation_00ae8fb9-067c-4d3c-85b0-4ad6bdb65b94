package com.xk.ewd.application.handler.query.logistics;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.ewd.application.action.query.logistics.ImportDetailQuery;
import com.xk.ewd.domain.repository.logistics.LogisticsImportDetailRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.logistics.ImportDetailRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 根据用户分组查询
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ImportDetailQueryHandler
        implements IActionQueryHandler<ImportDetailQuery, Pagination> {

    private final LogisticsImportDetailRootQueryRepository logisticsImportDetailRootQueryRepository;

    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<ImportDetailQuery> query) {

        Function<ImportDetailQuery, Mono<Pagination>> getReq = q -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(q.getLimit());
            pagination.setOffset(q.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(q));
            return Mono.just(pagination);
        };

        return query.flatMap(getReq).flatMap(pagination -> logisticsImportDetailRootQueryRepository
                .importDetail(pagination)
                .flatMap(logisticsOrderRoot -> Mono
                        .just(converter.convert(logisticsOrderRoot, ImportDetailRspDto.class)))
                .collectList().flatMap(list -> {
                    pagination.setRecords(list);
                    return Mono.just(pagination);
                }));

    }

}
