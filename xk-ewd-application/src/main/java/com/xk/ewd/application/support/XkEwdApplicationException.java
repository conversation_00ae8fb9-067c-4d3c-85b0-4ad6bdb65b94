package com.xk.ewd.application.support;

import com.xk.ewd.application.commons.XkEwdApplicationErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.wrapper.ApplicationWrapperThrowable;

/**
 * @author: killer
 **/
public class XkEwdApplicationException extends ApplicationWrapperThrowable {

    public XkEwdApplicationException(XkEwdApplicationErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public XkEwdApplicationException(XkEwdApplicationErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

}
