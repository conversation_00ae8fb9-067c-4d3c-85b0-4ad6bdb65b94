package com.xk.ewd.application.convertor.order;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.dto.order.OrderStatisticsDto;
import com.xk.ewd.interfaces.dto.rsp.order.OrderStatisticsRsp;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class OrderStatisticsDtoToOrderStatisticsRspMapper
        implements BaseMapper<OrderStatisticsDto, OrderStatisticsRsp> {


    @Override
    public OrderStatisticsRsp convert(OrderStatisticsDto source) {
        return convert(source, OrderStatisticsRsp.builder().build());
    }

    @Override
    public OrderStatisticsRsp convert(OrderStatisticsDto source, OrderStatisticsRsp target) {
        if (source == null) {
            return null;
        }

        return OrderStatisticsRsp.builder().payCount(source.getPayCount())
                .totalCount(source.getTotalCount()).refundCount(source.getRefundCount())
                .waitPayCount(source.getWaitPayCount()).build();
    }
}
