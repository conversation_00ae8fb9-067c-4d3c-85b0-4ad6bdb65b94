package com.xk.ewd.application.action.query.order;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.ewd.interfaces.dto.req.order.OrderNoRequireReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = OrderNoRequireReq.class)})
public class OrderLogQuery implements IActionQueryMany {

    /**
     * 订单编号
     */
    private String orderNo;
}
