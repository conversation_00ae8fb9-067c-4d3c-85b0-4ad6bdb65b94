package com.xk.ewd.application.handler.event.logistics;


import java.util.Date;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.enums.filetask.FileTaskBizStatusEnum;
import com.xk.enums.filetask.FileTaskStatusEnum;
import com.xk.ewd.domain.dto.logistics.FileTaskStatusUpdateDto;
import com.xk.ewd.domain.dto.logistics.FileTaskStatusUpdateRspDto;
import com.xk.ewd.domain.event.filetask.TaskOrderUpdateEvent;
import com.xk.ewd.domain.repository.logistics.LogisticsImportDetailRootQueryRepository;
import com.xk.order.domain.event.task.OrderImportStatusUpdateEvent;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderImportStatusUpdateHandler
        extends AbstractEventVerticle<OrderImportStatusUpdateEvent> {

    private final LogisticsImportDetailRootQueryRepository logisticsImportDetailRootQueryRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<OrderImportStatusUpdateEvent> event) {
        Function<Date, Boolean> getTimeout = createTime -> {
            long diffInMillis = new Date().getTime() - createTime.getTime();
            long diffInMinutes = diffInMillis / (60 * 1000);
            return diffInMinutes > 5;
        };

        // 扫描数据库中的总数和实际数量，如果大于0，小于总数 则更新为处理中，如果等于总数
        return event.flatMap(order -> logisticsImportDetailRootQueryRepository
                .selectCount(
                        FileTaskStatusUpdateDto.builder().taskId(order.getFileTaskId()).build())
                .flatMap(count -> {
                    Map<Integer, Integer> collect = count.stream()
                            .collect(Collectors.toMap(
                                    FileTaskStatusUpdateRspDto::getLogisticsOrderStatus,
                                    FileTaskStatusUpdateRspDto::getOrderCount));
                    Integer toBeShippedCount = collect
                            .getOrDefault(LogisticsOrderStatusEnum.TO_BE_SHIPPED.getCode(), 0);
                    Integer toBeReceivedCount = collect
                            .getOrDefault(LogisticsOrderStatusEnum.TO_BE_RECEIVED.getCode(), 0);
                    Integer receivedCount =
                            collect.getOrDefault(LogisticsOrderStatusEnum.RECEIVED.getCode(), 0);
                    Integer successCount = toBeReceivedCount + receivedCount;

                    FileTaskStatusEnum fileTaskStatusEnum = FileTaskStatusEnum.TO_BE_PROCESSED;
                    FileTaskBizStatusEnum fileTaskBizStatusEnum = FileTaskBizStatusEnum.COMMIT;
                    if (successCount > 0) {
                        fileTaskStatusEnum = FileTaskStatusEnum.IN_PROCESSED;
                        fileTaskBizStatusEnum = FileTaskBizStatusEnum.PROCESSED;
                    }
                    if (successCount.equals(order.getTotalCount())) {
                        fileTaskStatusEnum = FileTaskStatusEnum.SUCCESS_PROCESSED;
                        fileTaskBizStatusEnum = FileTaskBizStatusEnum.SUCCESS;
                    }
                    if (toBeShippedCount > 0) {
                        if (order.getTotalCount().equals(successCount + toBeShippedCount)) {
                            fileTaskStatusEnum = FileTaskStatusEnum.PARTIALLY_PROCESSED;
                            fileTaskBizStatusEnum = FileTaskBizStatusEnum.SUCCESS;
                        } else {
                            fileTaskStatusEnum = FileTaskStatusEnum.IN_PROCESSED;
                            fileTaskBizStatusEnum = FileTaskBizStatusEnum.PROCESSED;
                        }
                    }
                    if (getTimeout.apply(order.getCreateTime())) {
                        // 处理超时
                        fileTaskStatusEnum = FileTaskStatusEnum.FAIL_PROCESSED;
                        fileTaskBizStatusEnum = FileTaskBizStatusEnum.SUCCESS;
                    }

                    EventRoot eventRoot = EventRoot.builder().domainEvent(TaskOrderUpdateEvent
                            .builder()
                            .identifier(EventRoot
                                    .getCommonsDomainEventIdentifier(TaskOrderUpdateEvent.class))
                            .fileTaskId(order.getFileTaskId()).totalCount(order.getTotalCount())
                            .successCount(successCount).fileTaskStatus(fileTaskStatusEnum)
                            .fileTaskBizStatusEnum(fileTaskBizStatusEnum)
                            .successTime(fileTaskBizStatusEnum.equals(FileTaskBizStatusEnum.SUCCESS)
                                    ? new Date()
                                    : null)
                            .build()).isQueue(false).build();
                    return eventRootService.publisheByMono(eventRoot);
                })).then();

    }
}
