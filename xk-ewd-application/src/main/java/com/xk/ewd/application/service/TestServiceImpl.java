package com.xk.ewd.application.service;

import com.myco.mydata.config.infrastructure.data.persistence.cfg.TSysCfgMapper;
import com.myco.mydata.config.infrastructure.data.po.cfg.TSysCfg;
import com.xk.ewd.interfaces.service.TestService;
import org.springframework.stereotype.Service;

import com.myco.framework.common.util.JsonUtil;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.ewd.infrastructure.data.persistence.ExampleTbl2Mapper;
import com.xk.ewd.infrastructure.data.po.ExampleTbl2;
import com.xk.ewd.interfaces.query.TestQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.util.Random;

/**
 * @author: killer
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class TestServiceImpl implements TestService {

    private final ExampleTbl2Mapper exampleTbl2Mapper;

    private final TSysCfgMapper testCfgMapper;

    @BusiCode
    @Override
    public Mono<Void> save(Mono<RequireSessionDto> requireSessionMono) {

        return Mono.fromCallable(() -> {
            // ExampleTbl2 test = new ExampleTbl2();
            // test.setCommodityId( "123456:");
            // test.setCountry("中国");
            // test.setCustomerName("测试");
            // test.setPayTime(System.currentTimeMillis());
            // test.setPrice(100.0);
            // test.setPayDt(new java.util.Date());
            //
            // exampleTbl2Mapper.insert(test);
            TSysCfg test = new TSysCfg();
            test.setParamId("test");
            test.setParamName("test");
            test.setParamValue("test");
            test.setUnitType("test");
            testCfgMapper.insert(test);

            log.error("db:{}", JsonUtil.seriazileAsString(test));
            // Random random = new Random();
            // for (int i = 0; i < 1000; i++) {
            // ExampleTbl2 test = new ExampleTbl2();
            // test.setCommodityId( "2kafka:"+ i + "" + random.nextInt(100));
            // test.setCountry("中国");
            // test.setCustomerName("测试");
            // test.setPayTime(System.currentTimeMillis());
            // test.setPrice(random.nextDouble() * 100);
            // test.setPayDt(new java.util.Date());
            //
            // exampleTbl2Mapper.insert(test);
            // log.error("db:{}", JsonUtil.seriazileAsString(test));
            // }
            // throw new RuntimeException("测试异常");
            return null;
        });
    }
}
