package com.xk.ewd.application.handler.event.logistics;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.enums.filetask.FileTaskBizStatusEnum;
import com.xk.enums.filetask.FileTaskStatusEnum;
import com.xk.ewd.domain.event.filetask.TaskOrderUpdateEvent;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.ewd.domain.model.logistics.entity.OrderEntity;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.repository.logistics.LogisticsImportDetailRootRepository;
import com.xk.order.domain.event.logistics.LogisticsOrderGenerateEvent;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsOrderGenerateEventHandler
        extends AbstractEventVerticle<LogisticsOrderGenerateEvent> {

    private final Converter converter;

    private final LogisticsImportDetailRootRepository logisticsImportDetailRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<LogisticsOrderGenerateEvent> event) {
        return event.flatMap(exportEvent -> {
            LogisticsOrderRoot root = LogisticsOrderRoot.builder()
                    .identifier(LogisticsOrderIdentifier.builder().logisticsOrderId(-1L).build())
                    .logisticsOrderEntity(LogisticsOrderEntity.builder()
                            .taskId(exportEvent.getTaskId())
                            // 只导出待发货的数据
                            .logisticsOrderStatus(LogisticsOrderStatusEnum.TO_BE_SHIPPED.getCode())
                            .build())
                    .orderEntity(OrderEntity.builder()
                            .filePath(String.format("%s/nas%s/%s", "file://",
                                    exportEvent.getFilePath(), exportEvent.getFileName()))
                            .build())
                    .build();
            return logisticsImportDetailRootRepository.generate(root).then(Mono.defer(() -> {
                // 发送fileTask 更新事件
                EventRoot eventRoot = EventRoot.builder().domainEvent(TaskOrderUpdateEvent.builder()
                        .identifier(EventRoot
                                .getCommonsDomainEventIdentifier(TaskOrderUpdateEvent.class))
                        .successTime(new Date()).totalCount(0).successCount(0)
                        .fileTaskId(exportEvent.getTaskId())
                        .fileTaskStatus(FileTaskStatusEnum.SUCCESS_PROCESSED)
                        .fileTaskBizStatusEnum(FileTaskBizStatusEnum.GENERATE_FILE).build())
                        .isQueue(true).build();
                return eventRootService.publisheByMono(eventRoot).then();
            }));
        });
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }
}
