package com.xk.ewd.application.action.command.log;

import java.util.Collections;
import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.ewd.domain.model.order.OrderLogRoot;
import com.xk.ewd.domain.model.order.entity.OrderLogEntity;
import com.xk.ewd.enums.order.OrderLogMsgEnum;

import io.github.linpeilie.Converter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import reactor.core.publisher.Mono;

@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers({@AutoMapper(target = OrderLogEntity.class, reverseConvertGenerate = false)})
public class CreateOrderLogCommand extends AbstractActionCommand {

    /**
     * 订单号
     */
    @NotNull
    private String orderNo;

    /**
     * 订单消息
     */
    @NotNull
    private OrderLogMsgEnum orderLogMsgEnum;

    /**
     * 事件原因
     */
    private String reason;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    public void buildCreate(Long createId) {
        this.createId = createId;
        this.createTime = new Date();
    }

    public Mono<OrderLogRoot> buildRoot(Converter converter) {
        OrderLogEntity convert = converter.convert(this, OrderLogEntity.class);
        convert.setOrderMsg(orderLogMsgEnum.getMsg());
        return Mono.just(OrderLogRoot.builder().identifier(convert.getIdentifier())
                .orderLogEntityList(Collections.singletonList(convert)).build());
    }
}
