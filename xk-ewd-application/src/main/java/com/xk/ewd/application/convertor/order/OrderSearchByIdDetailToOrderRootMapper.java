package com.xk.ewd.application.convertor.order;

import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.alibaba.fastjson2.JSONObject;
import com.xk.acct.enums.user.DeleteStatusEnum;
import com.xk.ewd.domain.model.order.OrderRoot;
import com.xk.ewd.domain.model.order.entity.*;
import com.xk.ewd.domain.model.order.id.OrderIdentifier;
import com.xk.ewd.domain.model.order.valobj.GoodsValueObject;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import com.xk.order.interfaces.dto.rsp.order.LogisticsInfoRsp;
import com.xk.order.interfaces.dto.rsp.order.OrderGoodsInfoRsp;
import com.xk.order.interfaces.dto.rsp.order.OrderSearchByIdDetailRsp;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class OrderSearchByIdDetailToOrderRootMapper
        implements BaseMapper<OrderSearchByIdDetailRsp, OrderRoot> {


    @Override
    public OrderRoot convert(OrderSearchByIdDetailRsp source) {
        return convert(source,
                OrderRoot.builder()
                        .identifier(OrderIdentifier.builder().orderNo(source.getOrderNo()).build())
                        .build());
    }

    @Override
    public OrderRoot convert(OrderSearchByIdDetailRsp source, OrderRoot target) {
        if (source == null) {
            return null;
        }

        LogisticsInfoRsp first = new LogisticsInfoRsp();
        if (CollectionUtils.isNotEmpty(source.getLogisticsInfoRspList())) {
            first = source.getLogisticsInfoRspList().stream()
                    .filter(logisticsInfoRsp -> !logisticsInfoRsp.getLogisticsOrderType()
                            .equals(LogisticsOrderTypeEnum.GIFT.getCode()))
                    .findFirst().orElse(new LogisticsInfoRsp());
        }

        return OrderRoot.builder()
                .identifier(OrderIdentifier.builder().orderNo(source.getOrderNo()).build())
                .corpEntity(CorpEntity.builder().corpId(source.getCorpId())
                        .corpName(source.getCorpName()).build())
                .logisticsOrderEntity(
                        LogisticsOrderEntity.builder().logisticsOrderId(first.getLogisticsOrderId())
                                .logisticsCorpName(first.getLogisticsCorpName())
                                .logisticsNo(first.getLogisticsNo())
                                .logisticsOrderStatus(first.getLogisticsOrderStatus())
                                .logisticsOrderType(first.getLogisticsOrderType()).build())
                .orderAddrEntity(OrderAddrEntity.builder().userAddressId(source.getUserAddressId())
                        .addressDetail(source.getAddressDetail())
                        .addressSite(source.getAddressSite())
                        .consigneeName(source.getConsigneeName())
                        .receivingMobile(source.getReceivingMobile()).build())
                .orderEntity(OrderEntity.builder().orderId(source.getOrderId())
                        .orderNo(source.getOrderNo())
                        .corpDicountAmount(source.getCorpDiscountAmount())
                        .couponAmount(source.getCouponAmount()).orderStatus(source.getOrderStatus())
                        .discountAmount(source.getDiscountAmount()).orderType(source.getOrderType())
                        .payNo(source.getPayNo())
                        .freeQuotaDiscountAmount(source.getFreeQuotaDiscountAmount())
                        .createTime(source.getCreateTime())
                        .orderTotalBuyCount(source.getOrderTotalBuyCount())
                        .payType(source.getPayType()).payTime(source.getPayTime())
                        .totalAmount(source.getTotalAmount()).shippingFee(source.getShippingFee())
                        .otherDicountAmount(source.getOtherDiscountAmount())
                        .deleted(DeleteStatusEnum.NOT_DELETE.getCode())
                        .refundStatus(source.getRefundStatus())
                        .shipTotalCount(source.getShipTotalCount())
                        .remindShippingStatus(source.getRemindShippingStatus())
                        .refundStatus(source.getRefundStatus()).payAmount(source.getPayAmount())
                        .payStatus(source.getPayStatus()).platformType(source.getPlatformType())
                        .build())
                .userEntity(UserEntity.builder().userId(source.getUserId())
                        .mobile(source.getMobile()).userNick(source.getUserNick()).build())
                .goodsValueObject(GoodsValueObject.builder()
                        .goodsInfo(JSONObject.toJSONString(source.getGoodsInfo()))
                        .giftInfo(JSONObject.toJSONString(source.getGiftInfo()))
                        .goodsName(JSONObject.toJSONString(source.getGoodsInfo().stream()
                                .map(OrderGoodsInfoRsp::getGoodsName).toList()))
                        .goodsId(JSONObject.toJSONString(source.getGoodsInfo().stream()
                                .map(OrderGoodsInfoRsp::getGoodsId).toList()))
                        .productType(JSONObject.toJSONString(source.getGoodsInfo().stream()
                                .map(OrderGoodsInfoRsp::getProductType).toList()))
                        .build())
                .build();
    }
}
