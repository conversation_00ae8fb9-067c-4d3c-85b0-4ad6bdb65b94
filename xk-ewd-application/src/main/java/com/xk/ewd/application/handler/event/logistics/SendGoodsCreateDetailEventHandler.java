package com.xk.ewd.application.handler.event.logistics;


import org.springframework.stereotype.Component;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.ewd.domain.model.logistics.entity.OrderEntity;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.model.logistics.valobj.GoodsValueObject;
import com.xk.ewd.domain.repository.logistics.LogisticsImportDetailRootRepository;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateDetailEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class SendGoodsCreateDetailEventHandler
        extends AbstractEventVerticle<SendGoodsCreateDetailEvent> {

    private final LogisticsImportDetailRootRepository logisticsImportDetailRootRepository;

    @Override
    public Mono<Void> handle(Mono<SendGoodsCreateDetailEvent> event) {
        return event.flatMap(
                createEvent -> logisticsImportDetailRootRepository.save(LogisticsOrderRoot.builder()
                        .identifier(LogisticsOrderIdentifier.builder()
                                .logisticsOrderId(createEvent.getLogisticsOrderId()).build())
                        .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                .logisticsOrderId(createEvent.getLogisticsOrderId())
                                .logisticsOrderStatus(
                                        createEvent.getLogisticsOrderStatus().getCode())
                                .logisticsCorpName(createEvent.getLogisticsCorpName())
                                .logisticsNo(createEvent.getLogisticsNo())
                                .logisticsOrderType(createEvent.getLogisticsOrderType().getCode())
                                .taskId(createEvent.getFileTaskId())
                                .errRemark(createEvent.getErrRemark()).build())
                        .orderEntity(
                                OrderEntity.builder().orderNo(createEvent.getOrderNo()).build())
                        .goodsValueObject(
                                GoodsValueObject.builder().goodsName(createEvent.getGoodsName())
                                        .goodsCount(createEvent.getGoodsCount()).build())
                        .build()));
    }
}
