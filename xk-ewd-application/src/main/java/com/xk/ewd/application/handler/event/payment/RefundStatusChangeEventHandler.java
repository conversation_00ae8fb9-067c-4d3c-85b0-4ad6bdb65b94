package com.xk.ewd.application.handler.event.payment;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.domain.model.payment.PaymentRoot;
import com.xk.ewd.domain.model.payment.entity.RefundEntity;
import com.xk.ewd.domain.service.payment.PaymentRootService;
import com.xk.order.domain.event.payment.RefundStatusChangeEvent;
import com.xk.order.enums.payment.RefundStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
public class RefundStatusChangeEventHandler extends AbstractEventVerticle<RefundStatusChangeEvent> {

    private final PaymentRootService paymentRootService;

    @Override
    public Mono<Void> handle(Mono<RefundStatusChangeEvent> event) {

        return event.flatMap(refundStatusChangeEvent -> {
            log.info("接收退货单状态变更事件：{}",refundStatusChangeEvent.getPaymentId());
            return paymentRootService.updateRefund(PaymentRoot.builder()
                            .refundEntity(RefundEntity.builder()
                                    .paymentId(refundStatusChangeEvent.getPaymentId())
                                    .refundStatus(RefundStatusEnum.getByCode(refundStatusChangeEvent.getRefundStatus()))
                                    .refundTime(refundStatusChangeEvent.getRefundTime())
                                    .updateTime(new Date())
                                    .build())
                    .build());
        });
    }
    @Override
    public boolean isBlockExecute() {
        return true;
    }
}
