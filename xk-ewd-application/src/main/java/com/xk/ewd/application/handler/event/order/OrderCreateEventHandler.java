package com.xk.ewd.application.handler.event.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.ewd.application.action.command.log.CreateOrderLogCommand;
import com.xk.ewd.domain.service.order.OrderAdapterService;
import com.xk.ewd.enums.order.OrderLogMsgEnum;
import com.xk.order.domain.event.order.OrderCreateEvent;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCreateEventHandler extends AbstractEventVerticle<OrderCreateEvent> {

    private final Converter converter;

    private final OrderAdapterService orderAdapterService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderCreateEvent> mono) {
        return mono.flatMap(event -> orderAdapterService
                .create(Mono.just(StringIdentifier.builder().id(event.getOrderNo()).build()))
                .then(commandDispatcher.executeCommand(Mono.just(new CreateOrderLogCommand()),
                        CreateOrderLogCommand.class, command -> {
                            command.setOrderNo(event.getOrderNo());
                            command.setOrderLogMsgEnum(OrderLogMsgEnum.CREATE);
                            command.setReason("订单创建事件");
                            command.setOrderType(event.getOrderType().getCode());
                            command.buildCreate(event.getCreateId());
                            return command;
                        })))
                .onErrorResume(throwable -> {
                    log.error("处理订单创建事件异常", throwable);
                    return Mono.error(throwable);
                });
    }
}
