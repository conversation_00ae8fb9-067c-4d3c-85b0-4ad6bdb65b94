package com.xk.ewd.application.handler.query.logistics;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.ewd.application.action.query.logistics.PtsGoodsGroupQuery;
import com.xk.ewd.domain.repository.logistics.LogisticsGoodsRootQueryRepository;
import com.xk.ewd.interfaces.dto.rsp.logistics.AppPtsGoodsGroupRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 根据用户分组查询
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PtsGoodsGroupQueryHandler
        implements IActionQueryHandler<PtsGoodsGroupQuery, Pagination> {

    private final LogisticsGoodsRootQueryRepository logisticsGoodsRootQueryRepository;

    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<PtsGoodsGroupQuery> query) {

        Function<PtsGoodsGroupQuery, Mono<Pagination>> getReq = q -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(q.getLimit());
            pagination.setOffset(q.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(q));
            return Mono.just(pagination);
        };

        return query.flatMap(orderQuery -> ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObjectRoot -> {
                    UserDataObjectEntity userDataObjectEntity =
                            userObjectRoot.getUserDataObjectEntity();
                    if (UserTypeEnum.MERCHANT_KAS.equals(userDataObjectEntity.getUserType())) {
                        orderQuery.setCorpId(userDataObjectEntity.getCorpId());
                    }
                    return Mono.just(orderQuery);
                }).flatMap(getReq)
                .flatMap(pagination -> logisticsGoodsRootQueryRepository.ptsGoodsGroup(pagination)
                        .flatMap(logisticsOrderRoot -> Mono.just(converter
                                .convert(logisticsOrderRoot, AppPtsGoodsGroupRspDto.class)))
                        .collectList().flatMap(list -> {
                            pagination.setRecords(list);
                            return Mono.just(pagination);
                        })));

    }

}
