package com.xk.goods.application.handler.command.business;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.business.DeleteBusinessResCommand;
import com.xk.goods.domain.model.business.BusinessResRoot;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.repository.business.BusinessResRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class DeleteBusinessResHandler
        implements IActionCommandHandler<DeleteBusinessResCommand, Void> {

    private final Converter converter;
    private final BusinessResRootRepository businessResRootRepository;

    @Override
    public Mono<Void> execute(Mono<DeleteBusinessResCommand> mono) {
        return execute(mono, command -> {
            BusinessResEntity entity = converter.convert(command, BusinessResEntity.class);
            return BusinessResRoot.builder().identifier(entity.getIdentifier())
                    .businessResEntity(entity).build();
        }, businessResRootRepository::remove);
    }
}
