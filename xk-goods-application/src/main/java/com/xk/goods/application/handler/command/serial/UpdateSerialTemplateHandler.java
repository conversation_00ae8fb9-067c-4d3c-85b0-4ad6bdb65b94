package com.xk.goods.application.handler.command.serial;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.serial.UpdateSerialTemplateCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateSerialTemplateHandler implements IActionCommandHandler<UpdateSerialTemplateCommand, Void> {

    @Override
    public Mono<Void> execute(Mono<UpdateSerialTemplateCommand> mono) {
        return null;
    }
}
