package com.xk.goods.application.action.query.score;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.interfaces.dto.req.score.ScorePagerReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMappers({@AutoMapper(target = ScorePagerReqDto.class, convertGenerate = false)})
public class ScoreSearchPagerQuery extends PagerQuery implements IActionQuery {

    private Long goodsId;
}
