package com.xk.goods.application.handler.command.serial;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.goods.application.action.command.serial.SaveSerialGroupCommand;
import com.xk.goods.domain.model.serial.SerialGroupRoot;
import com.xk.goods.domain.model.serial.entity.SerialGroupEntity;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.repository.serial.SerialGroupCategoryRootQueryRepository;
import com.xk.goods.domain.repository.serial.SerialGroupRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaveSerialGroupHandler implements IActionCommandHandler<SaveSerialGroupCommand, Void> {

    private final SerialGroupRootRepository serialGroupRootRepository;

    private final SerialGroupCategoryRootQueryRepository serialGroupCategoryRootQueryRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<SaveSerialGroupCommand> mono) {
        return mono.flatMap(command ->
                serialGroupCategoryRootQueryRepository.searchSerialGroupCategoryById(LongIdentifier.builder().id(command.getSerialGroupCategoryId()).build())
                        .flatMap(serialGroupCategoryEntity -> {
                            SerialGroupEntity entity = new SerialGroupEntity();
                            converter.convert(command, entity);
                            entity.setUpdateId(command.getUserId());
                            entity.setCreateId(command.getUserId());
                            entity.setCategoryGroupName(serialGroupCategoryEntity.getName() + ";" + command.getName());
                            SerialGroupIdentifier identifier = SerialGroupIdentifier.builder().serialGroupId(command.getSerialGroupId()).build();
                            return serialGroupRootRepository.save(SerialGroupRoot.builder()
                                    .identifier(identifier).serialGroupEntity(entity).build());
                        })
        );
    }
}
