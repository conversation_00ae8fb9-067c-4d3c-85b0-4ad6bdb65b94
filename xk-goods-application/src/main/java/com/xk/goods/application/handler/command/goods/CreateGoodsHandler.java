package com.xk.goods.application.handler.command.goods;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.goods.CreateGoodsCommand;
import com.xk.goods.domain.service.goods.GoodsRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateGoodsHandler implements IActionCommandHandler<CreateGoodsCommand, Void> {

    private final GoodsRootService goodsRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateGoodsCommand> mono) {
        return mono.flatMap(command -> goodsRootService
                .saveRoot(Mono.just(command.getRoot(command, converter))));
    }
}
