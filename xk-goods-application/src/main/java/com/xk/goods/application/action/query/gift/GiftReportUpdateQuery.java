package com.xk.goods.application.action.query.gift;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdRequireReqDto;
import com.xk.goods.interfaces.dto.req.goods.ReportPagerReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = GoodsIdRequireReqDto.class, convertGenerate = false),
        @AutoMapper(target = ReportPagerReqDto.class, convertGenerate = false)})
public class GiftReportUpdateQuery implements IActionQueryMany {

    /**
     * 主键
     */
    @NotNull
    private Long goodsId;

    /**
     * 商家ID
     */
    private Long corpId;
}
