package com.xk.goods.application.handler.query.serial;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.goods.application.action.query.serial.SerialItemOriginalQuery;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.domain.repository.serial.SerialItemRootQueryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SerialItemOriginalQueryHandler implements IActionQueryHandler<SerialItemOriginalQuery, Pagination> {

    private final SerialItemRootQueryRepository serialItemRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<SerialItemOriginalQuery> queryMono) {
        return execute(queryMono, query -> {
            Pagination pagination = new Pagination();
            pagination.setOffset(query.getOffset());
            pagination.setLimit(query.getLimit());
            pagination.setCriteria(CollectionHelper.converBeanToMap(query));
            return pagination;
        }, serialItemRootQueryRepository::searchOriginalItemByKeywords, SerialOriginalItemEntity.class);
    }
}
