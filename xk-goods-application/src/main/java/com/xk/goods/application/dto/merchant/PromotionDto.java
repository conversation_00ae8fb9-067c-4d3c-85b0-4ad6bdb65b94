package com.xk.goods.application.dto.merchant;

import java.io.Serializable;

import com.xk.goods.domain.model.merchant.valobj.PromotionValObj;
import com.xk.goods.interfaces.dto.req.goods.merchant.PromotionReqDto;
import com.xk.goods.interfaces.dto.res.goods.merchant.PromotionResDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = PromotionReqDto.class, convertGenerate = false),
        @AutoMapper(target = PromotionResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = PromotionValObj.class),})
public class PromotionDto implements Serializable {
    /**
     * 活动序号
     */
    private Long promotionSequence;

    /**
     * 活动标题
     */
    private String promotionTitle;

    /**
     * 活动内容
     */
    private String promotionContent;
}
