
package com.xk.goods.application.query.goods;

import java.util.Comparator;
import java.util.List;
import java.util.function.Function;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.action.session.AbstractSession;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.application.action.query.cate.CateListByGroupQuery;
import com.xk.application.action.query.tree.TreeNodeQueryByComposite;
import com.xk.domain.model.category.Category;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.domain.model.tree.TreeNode;
import com.xk.enums.tree.NodeStatusEnum;
import com.xk.goods.domain.model.category.entity.GoodsCategoryTreeNode;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.interfaces.dto.res.goods.GoodsCategoryTreeNodeAppDto;
import com.xk.goods.interfaces.query.goods.GoodsCategoryAppQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsCategoryAppQueryServiceImpl implements GoodsCategoryAppQueryService {


    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;

    @BusiCode
    @Override
    public Mono<List<GoodsCategoryTreeNodeAppDto>> searchParent(Mono<AbstractSession> mono) {
        return mono.flatMap(req -> {
            // 1. 构建分类查询对象
            Function<AbstractSession, CateListByGroupQuery> buildCateQuery =
                    request -> CateListByGroupQuery.builder()
                            .compositeIdentifier(CompositeIdentifier.builder()
                                    .groupId(Long.valueOf(GoodsTypeEnum.MALL_PRODUCT.getCode()))
                                    .type(GroupBusinessTypeEnum.GOODS.getValue()).build())
                            .build();

            // 2. 分类处理流水线
            Function<Category, Mono<GoodsCategoryTreeNode>> processCategory = category -> {
                // 2.1 构建树节点查询
                Function<Category, TreeNodeQueryByComposite> buildNodeQuery =
                        cat -> TreeNodeQueryByComposite.builder()
                                .compositeIdentifier(CompositeIdentifier.builder().groupId(0L)
                                        .type(GroupBusinessTypeEnum.CATE.getValue())
                                        .id(cat.getCateId()).build())
                                .build();

                // 2.2 树节点转换器
                Function<TreeNode, GoodsCategoryTreeNode> nodeConverter = treeNode -> {
                    GoodsCategoryTreeNode node = new GoodsCategoryTreeNode();
                    node.setGoodsCategoryId((Long) category.getCompositeIdentifiers().stream()
                            .findFirst().map(CompositeIdentifier::id).orElse(null));
                    node.setNodeId(treeNode.getNodeId());
                    node.setParentId(treeNode.getParentId());
                    node.setStatus(treeNode.getStatus());
                    node.setSort(treeNode.getSort());
                    node.setName(treeNode.getName());
                    return node;
                };

                return this.actionQueryDispatcher.process(Mono.just(buildNodeQuery.apply(category)),
                        TreeNodeQueryByComposite.class, TreeNode.class).map(nodeConverter);
            };

            // 组合函数式处理链
            return this.actionQueryManyDispatcher
                    .executeQuery(Mono.just(buildCateQuery.apply(req)), CateListByGroupQuery.class,
                            Category.class)
                    .flatMap(processCategory)
                    .filter(cate -> NodeStatusEnum.ENABLED.equals(cate.getStatus())
                            && (cate.getParentId() == null || cate.getParentId() == 0L))
                    .sort(Comparator.comparing(GoodsCategoryTreeNode::getSort))
                    .map(v -> GoodsCategoryTreeNodeAppDto.builder().goodsCategoryId(v.getNodeId())
                            .name(v.getName()).build())
                    .collectList();
        });
    }
}
