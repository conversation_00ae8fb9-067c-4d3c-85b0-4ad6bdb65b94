package com.xk.goods.application.action.query.serial;

import com.myco.framework.cache.annotations.KeyProperty;
import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.domain.model.serial.entity.SerialGroupEntity;
import com.xk.goods.domain.model.serial.entity.SerialGroupTeamEntity;
import com.xk.goods.interfaces.dto.req.serial.AppSerialGroupTeamReqDto;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupQuickReqDto;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMappers({@AutoMapper(target = AppSerialGroupTeamReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialGroupTeamEntity.class, reverseConvertGenerate = false)})
public class SerialGroupTeamQuery extends PagerQuery implements IActionQuery {
    /**
     * 卡密组id
     */
    @KeyProperty
    private Long serialGroupId;

    /**
     * 卡密组类目id
     */
    private Long serialGroupCategoryId;

    /**
     * 卡密组名称
     */
    private String name;

    /**
     * 一级类目名称；二级类目名称；卡密组名称；分号拼接
     */
    private String categoryGroupName;

    /**
     * 卡密组类型：1球队卡密组；2自定义卡密组
     */
    private Integer groupType;

    /**
     * 板块类型：1球队；2动漫
     */
    private Integer blockType;

    /**
     * 是否显示（0：不显示，1：显示）
     */
    private Integer isShow;

    /**
     * 状态（0：正常，1：禁用）
     */
    private Integer status;

    /**
     * 是否删除（0：不删除，1：已删除）
     */
    private Integer deleted;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
