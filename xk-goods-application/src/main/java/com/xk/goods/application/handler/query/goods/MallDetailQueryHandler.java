package com.xk.goods.application.handler.query.goods;

import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.domain.service.cate.CateRootService;
import com.xk.domain.service.tree.TreeRootService;
import com.xk.goods.application.action.query.business.BusinessResIdentifierQuery;
import com.xk.goods.application.action.query.goods.MallDetailQuery;
import com.xk.goods.domain.model.category.GoodsCategoryRoot;
import com.xk.goods.domain.model.category.entity.GoodsCategoryEntity;
import com.xk.goods.domain.model.goods.GoodsRoot;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.price.id.PriceIdentifier;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.service.category.GoodsCategoryRootService;
import com.xk.goods.domain.service.goods.GoodsRootService;
import com.xk.goods.domain.service.price.PriceRootService;
import com.xk.goods.domain.service.specification.SpecificationRootService;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.price.PriceTypeEnum;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;
import com.xk.goods.interfaces.dto.res.goods.MallDetailResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class MallDetailQueryHandler
        implements IActionQueryHandler<MallDetailQuery, MallDetailResDto> {

    private final CateRootService cateRootService;
    private final TreeRootService treeRootService;
    private final GoodsCategoryRootService goodsCategoryRootService;
    private final GoodsRootService goodsRootService;
    private final SpecificationRootService specificationRootService;
    private final PriceRootService priceRootService;
    private final Converter converter;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public Mono<MallDetailResDto> execute(Mono<MallDetailQuery> query) {
        return query.flatMap(this::processMallDetailQuery);
    }

    private Mono<MallDetailResDto> processMallDetailQuery(MallDetailQuery dto) {
        // 1. 转换商品ID
        GoodsIdentifier goodsId = GoodsIdentifierConvertor.map(dto.getGoodsId());

        // 2. 并行获取商品Root和规格列表
        Mono<GoodsRoot> goodsRootMono = goodsRootService.getRoot(goodsId);
        Mono<List<SpecificationRoot>> specsMono = specificationRootService.searchByGoodsId(goodsId);

        return Mono.zip(goodsRootMono, specsMono).flatMap(tuple -> {
            GoodsRoot goodsRoot = tuple.getT1();
            List<SpecificationRoot> specRoots = tuple.getT2();

            // 3. 转换基础DTO
            MallDetailResDto result = converter.convert(goodsRoot, MallDetailResDto.class);

            // 并行执行所有设置操作
            return setProductPictures(result, goodsRoot)
                    .then(setUnitTypeAndPriceInfo(result, specRoots))
                    .then(setCategoryInfo(result, goodsRoot)).thenReturn(result);
        });
    }

    private Mono<Void> setProductPictures(MallDetailResDto result, GoodsRoot goodsRoot) {
        return Flux.fromIterable(goodsRoot.getResIdentifierList())
                .flatMap(res -> this.queryDispatcher.executeQuery(Mono.just(res),
                        BusinessResIdentifierQuery.class, BusinessResDto.class))
                .collectList().doOnNext(result::setProductPicList).then();
    }

    private Mono<Void> setUnitTypeAndPriceInfo(MallDetailResDto result,
            List<SpecificationRoot> specRoots) {
        if (specRoots == null || specRoots.isEmpty()) {
            return Mono.empty();
        }
        SpecificationRoot first = specRoots.getFirst();
        result.setUnitType(first.getSpecificationEntity().getUnitType());

        PriceIdentifier priceId =
                specRoots.getFirst().getSpecificationEntity().getPriceIdentifier();
        return priceRootService.getRoot(priceId).doOnNext(priceRoot -> {
            priceRoot.getPriceItemEntityList().forEach(item -> {
                if (PriceTypeEnum.SALE_PRICE.equals(item.getPriceType())) {
                    result.setAmount(item.getAmount());
                    result.setCurrencyType(item.getCurrencyType().getCode());
                } else if (PriceTypeEnum.COST_PRICE.equals(item.getPriceType())) {
                    result.setCostAmount(item.getAmount());
                    result.setCurrencyType(item.getCurrencyType().getCode());
                }
            });
        }).then();
    }

    private Mono<Void> setCategoryInfo(MallDetailResDto result, GoodsRoot goodsRoot) {
        if (CollectionUtils.isEmpty(goodsRoot.getGoodsCategoryIdentifierList())) {
            return Mono.empty();
        }

        return Flux.fromIterable(goodsRoot.getGoodsCategoryIdentifierList())
                .flatMap(goodsCategoryRootService::getRoot).flatMap(this::buildGoodsCategoryDto)
                .collectList().doOnNext(result::setGoodsCategoryDtoList).then();
    }

    private Mono<MallDetailResDto.GoodsCategoryDto> buildGoodsCategoryDto(
            GoodsCategoryRoot category) {
        GoodsCategoryEntity entity = category.getGoodsCategoryEntity();
        CompositeIdentifier identifier = CompositeIdentifier.builder()
                .id(entity.getGoodsCategoryId()).type(GroupBusinessTypeEnum.GOODS.getValue())
                .groupId(GoodsTypeEnum.MALL_PRODUCT.getCode()).build();

        return cateRootService.findCateByBusinessId(identifier).flatMap(cate -> {
            CompositeIdentifier cateIdentifier = CompositeIdentifier.builder().id(cate.getCateId())
                    .type(GroupBusinessTypeEnum.CATE.getValue()).groupId(0L).build();

            return treeRootService.findByBusinessId(cateIdentifier)
                    .map(treeNode -> MallDetailResDto.GoodsCategoryDto.builder()
                            .categoryName(treeNode.getName()).nodeId(treeNode.getNodeId())
                            .isRoot(treeNode.getParentId() == null || treeNode.getParentId() == 0)
                            .goodsCategoryId(category.getIdentifier().getGoodsCategoryId())
                            .build());
        });
    }
}
