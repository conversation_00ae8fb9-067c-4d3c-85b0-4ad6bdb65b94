package com.xk.goods.application.action.command.goods;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.category.entity.GoodsCategoryEntity;
import com.xk.goods.infrastructure.convertor.goods.GoodsTypeEnumConvertor;
import com.xk.goods.interfaces.dto.req.goods.GoodsCategoryReqDto;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = GoodsCategoryReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsCategoryEntity.class,
                uses = {GoodsTypeEnumConvertor.class, CommonStatusEnumConvertor.class},
                reverseConvertGenerate = false)})
public class CreateGoodsCategoryCommand extends AbstractActionCommand {

    private Long goodsCategoryId;

    /**
     * 商品类型: 1-商城商品, 2-物料商品, 3-收藏卡
     */
    private Integer goodsType;

    /**
     * 父id,不支持更新
     */
    private Long parentId;

    /**
     * 类别名称。 用于表示当前类别的名称。
     */
    private String categoryName;

    /**
     * 排序字段，用于类别的排序。
     */
    private Integer sort;

    /**
     * 类别状态，通常用整数值表示状态码。
     */
    private Integer status;

    @AutoMapping(targetClass = GoodsCategoryEntity.class, target = "createValObj.createId")
    private Long createId;

    @AutoMapping(targetClass = GoodsCategoryEntity.class, target = "createValObj.createTime")
    private Date createTime;
}
