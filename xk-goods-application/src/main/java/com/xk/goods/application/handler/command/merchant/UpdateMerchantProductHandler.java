package com.xk.goods.application.handler.command.merchant;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.xk.goods.application.action.command.merchant.UpdateMerchantProductCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.merchant.id.MerchantProductIdentifier;
import com.xk.goods.domain.service.merchant.MerchantProductRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateMerchantProductHandler
        implements IActionCommandHandler<UpdateMerchantProductCommand, Void> {

    private final MerchantProductRootService merchantProductRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdateMerchantProductCommand> mono) {
        return mono
                .flatMap(command -> merchantProductRootService
                        .getRoot(MerchantProductIdentifier.builder()
                                .goodsIdentifier(GoodsIdentifier.builder()
                                        .goodsId(command.getGoodsId()).build())
                                .build())
                        .switchIfEmpty(Mono.error(
                                new XkGoodsApplicationException(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                        .map(root -> command.getRoot(root, converter))
                        .flatMap(root -> merchantProductRootService.updateRoot(Mono.just(root))));
    }
}
