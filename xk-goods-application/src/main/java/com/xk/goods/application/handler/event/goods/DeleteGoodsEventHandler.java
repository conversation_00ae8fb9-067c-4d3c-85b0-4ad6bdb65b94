package com.xk.goods.application.handler.event.goods;

import static com.xk.goods.application.commons.XkGoodsApplicationErrorEnum.APPLICATION_ERROR;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.application.action.command.business.DeleteBusinessResCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.domain.event.goods.DeleteGoodsEvent;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.service.business.BusinessResRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteGoodsEventHandler extends AbstractEventVerticle<DeleteGoodsEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final BusinessResRootService businessResRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<DeleteGoodsEvent> mono) {
        return mono.flatMap(event -> {
            BaseBusinessRes firstRes = event.getBaseBusinessResList().getFirst();
            return Mono.justOrEmpty(firstRes)
                    .flatMap(first -> businessResRootService
                            .searchByBusinessGroup(BusinessResEntity.builder()
                                    .businessGroupType(first.getBusinessGroupType())
                                    .businessId(first.getBusinessId()).build())
                            .flatMap(res -> commandDispatcher.executeCommand(Mono.just(res),
                                    DeleteBusinessResCommand.class, Void.class))
                            .then())
                    .onErrorResume(e -> {
                        log.error("DeleteGoodsEvent事件失败: {}", event, e);
                        return Mono.error(new XkGoodsApplicationException(APPLICATION_ERROR));
                    }).then();
        });
    }
}
