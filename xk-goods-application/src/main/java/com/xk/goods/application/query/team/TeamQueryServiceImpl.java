package com.xk.goods.application.query.team;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.application.action.query.team.TeamMemberSearchQuery;
import com.xk.goods.interfaces.dto.req.team.TeamMemberSearchPagerReqDto;
import com.xk.goods.interfaces.query.team.TeamQueryService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class TeamQueryServiceImpl implements TeamQueryService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public Mono<Pagination> searchPager(Mono<TeamMemberSearchPagerReqDto> mono) {
        return queryDispatcher.executeQuery(mono, TeamMemberSearchQuery.class, Pagination.class);
    }
}
