package com.xk.goods.application.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author: killer
 **/
@Configuration
@ComponentScan({"com.xk.goods.application.convertor"
        , "com.xk.goods.application.query"
        , "com.xk.goods.application.service"
        , "com.xk.goods.application.handler"
        , "com.xk.goods.application.support.object"
        , "com.xk.goods.application.task"})
public class XkGoodsApplicationConfig {
}
