package com.xk.goods.application.handler.event.team;

import static com.xk.goods.application.commons.XkGoodsApplicationErrorEnum.APPLICATION_ERROR;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.application.action.command.business.DeleteBusinessResCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.team.DeleteTeamMemberEvent;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.service.business.BusinessResRootService;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteTeamMemberEventHandler extends AbstractEventVerticle<DeleteTeamMemberEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final BusinessResRootService businessResRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<DeleteTeamMemberEvent> mono) {
        return mono.flatMap(event -> businessResRootService
                .searchByBusinessGroup(
                        BusinessResEntity.builder().businessGroupType(BusinessGroupTypeEnum.MEMBER)
                                .businessId(event.getTeamMemberId()).build())
                .flatMap(res -> commandDispatcher.executeCommand(Mono.just(res),
                        DeleteBusinessResCommand.class, Void.class))
                .onErrorResume(e -> {
                    log.error("处理DeleteTeamMemberEvent事件失败: {}", event, e);
                    return Mono.error(new XkGoodsApplicationException(APPLICATION_ERROR));
                }).then());
    }
}
