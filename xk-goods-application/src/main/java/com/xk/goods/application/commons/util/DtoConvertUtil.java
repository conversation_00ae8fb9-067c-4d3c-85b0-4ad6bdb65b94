package com.xk.goods.application.commons.util;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import com.xk.goods.domain.model.business.id.BusinessResIdentifier;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.interfaces.dto.req.business.BusinessResReqDto;

public class DtoConvertUtil {

    public static List<BusinessResIdentifier> getSeriesId(Long identifier,
            List<BusinessResReqDto> seriesPicSet) {
        List<BusinessResIdentifier> identifierSet = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(seriesPicSet)) {
            identifierSet.addAll(seriesPicSet.stream()
                    .map(res -> BusinessResIdentifier.builder().resId(res.getResId())
                            .businessResType(BusinessResTypeEnum.SERIES_PICTURE)
                            .businessGroupType(BusinessGroupTypeEnum.SERIES).businessId(identifier)
                            .build())
                    .collect(Collectors.toSet()));
        }
        return identifierSet;
    }

    public static List<BusinessResIdentifier> getSeriesId(Long identifier,
            List<BusinessResReqDto> seriesPicSet, BusinessResTypeEnum businessResType) {
        List<BusinessResIdentifier> identifierSet = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(seriesPicSet)) {
            identifierSet.addAll(seriesPicSet.stream()
                    .map(res -> BusinessResIdentifier.builder().resId(res.getResId())
                            .businessResType(businessResType)
                            .businessGroupType(BusinessGroupTypeEnum.SERIES).businessId(identifier)
                            .build())
                    .collect(Collectors.toSet()));
        }
        return identifierSet;
    }

    public static List<BusinessResIdentifier> getTeamResId(Long identifier,
            List<BusinessResReqDto> memberPicSet, List<BusinessResReqDto> memberAvatorSet) {
        List<BusinessResIdentifier> identifierSet = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(memberPicSet)) {
            identifierSet.addAll(memberPicSet.stream()
                    .map(res -> BusinessResIdentifier.builder().resId(res.getResId())
                            .businessResType(BusinessResTypeEnum.MEMBER_PICTURE)
                            .businessGroupType(BusinessGroupTypeEnum.MEMBER).businessId(identifier)
                            .build())
                    .collect(Collectors.toSet()));
        }
        if (CollectionUtils.isNotEmpty(memberAvatorSet)) {
            identifierSet.addAll(memberAvatorSet.stream()
                    .map(res -> BusinessResIdentifier.builder().resId(res.getResId())
                            .businessResType(BusinessResTypeEnum.MEMBER_AVATAR)
                            .businessGroupType(BusinessGroupTypeEnum.MEMBER).businessId(identifier)
                            .build())
                    .collect(Collectors.toSet()));
        }
        return identifierSet;
    }
}
