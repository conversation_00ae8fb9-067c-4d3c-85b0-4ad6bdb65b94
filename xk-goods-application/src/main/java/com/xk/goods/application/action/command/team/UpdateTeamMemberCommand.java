package com.xk.goods.application.action.command.team;

import java.util.Date;
import java.util.List;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.business.id.BusinessResIdentifier;
import com.xk.goods.domain.model.team.entity.TeamMemberEntity;
import com.xk.goods.interfaces.dto.req.team.TeamMemberReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = TeamMemberReqDto.class),
        @AutoMapper(target = TeamMemberEntity.class),})
public class UpdateTeamMemberCommand extends AbstractActionCommand {
    private Long teamMemberId;

    /**
     * 队员类型
     */
    private Integer memberType;

    /**
     * 队员中文名
     */
    private String memberCnName;

    /**
     * 队员英文名
     */
    private String memberEnName;

    /**
     * 队员图片资源映射
     */
    private List<BusinessResIdentifier> businessResIdentifierList;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private Long updateId;
}
