package com.xk.goods.application.commons;

import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * application错误码
 * 13000-13999
 */
@Getter
@AllArgsConstructor
public enum XkGoodsApplicationErrorEnum implements ExceptionIdentifier {

    APPLICATION_ERROR(13000, "application错误"),

    GIFT_REPORT_OPERATION_FAIL(13001,"卡密已被他人操作"),
    GIFT_REPORT_NEXT_FILTER(13002,"您选择的卡密中有已被他人选择的，将会被剔除"),
    GIFT_REPORT_PUBLISHED(13003,"拆卡报告已经发布"),
    GIFT_REPORT_NOT_HAS_RES(13004,"有其他用户未上传图片，图片缺失不能发布"),
    GIFT_REPORT_COUNT_FAIL(13005,"超过最大可修改次数"),
    MERCHANT_RANDOM_TYPE_ERROR(13006,"该产品类型不允许设置剩余随机"),
    GOODS_NOT_LISTING(13007,"商品未上架"),
    MERCHANT_ALREADY_REMAIN_RANDOM(13008,"商品已设置剩余随机,不可再次设置"),


    TEAM_MEMBER_CREATE_EVENT_SEND_ERROR(13051, "队员创建事件发送异常"),

    RES_NOT_EXIST(13052, "资源不存在"),

    CATEGORY_HAS_GROUP(13053, "分类下存在卡密组，不能删除"),
    PAGER_QUERY_ERROR(13054, "分页查询失败"),
    SERIALS_TEMPLATE_EMPTY_ERROR(13055, "卡密模板不能为空，或存在空字段"),
    SERIALS_TEAM_EMPTY_ERROR(13056, "选择系列队伍和卡密队伍不匹配"),

    MERCHANT_PUBLICITY_ERROR(13057,"商家商品已经公示完成"),
    GOODS_DOWN_ERROR(13058,"商品下架异常"),
    DATE_CONVERT_ERROR(13059, "日期转换错误"),
    MERCHANT_NOT_GROUP(13060,"商家商品未组齐"),
    ;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}
