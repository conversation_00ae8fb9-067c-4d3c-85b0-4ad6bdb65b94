package com.xk.goods.application.action.command.score;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.score.entity.ScoreRuleEntity;
import com.xk.goods.infrastructure.convertor.score.ScoreRuleTypeEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = ScoreRuleEntity.class, uses = {ScoreRuleTypeEnumConvertor.class}),})
public class UpdateScoreRuleCommand extends AbstractActionCommand {

    /**
     * 规则类型 1-基础 2-福盒 3-边锋盒子 4-错卡密 5-原盒
     */
    private Integer scoreRuleType;

    /**
     * 卡商7日消费额加分
     */
    private Integer corpPayment7dayRule;

    /**
     * 24小时销售金额分数加分
     */
    private Integer saleAmount24hRule;

    /**
     * 累计销售金额分数加分
     */
    private Integer saleAmountTotalRule;

    /**
     * 24小时购买人数分数加分
     */
    private Integer buyerCount24hRule;

    /**
     * 累计购买人数分数加分
     */
    private Integer buyerCountTotalRule;

    /**
     * 半小时销售数量加分
     */
    private Integer saleCount30mRule;

    /**
     * 累计销售数量加分
     */
    private Integer saleCountTotalRule;

    /**
     * 更新人ID
     */
    @AutoMappings({
            @AutoMapping(targetClass = ScoreRuleEntity.class, target = "updateValObj.updateId")})
    private Long updateId;

    /**
     * 更新时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = ScoreRuleEntity.class, target = "updateValObj.updateTime")})
    private Date updateTime;
}
