package com.xk.goods.application.handler.query.series;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.xk.goods.application.action.query.series.SeriesCategoryListQuery;
import com.xk.goods.interfaces.dto.res.series.SeriesTreeNodeDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class SeriesCategoryTreeQueryHandler
        implements IActionQueryManyHandler<SeriesCategoryListQuery, SeriesTreeNodeDto> {

    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;
    private final Converter converter;

    @Override
    public Flux<SeriesTreeNodeDto> execute(Mono<SeriesCategoryListQuery> mono) {
        return Flux.empty();
    }

}
