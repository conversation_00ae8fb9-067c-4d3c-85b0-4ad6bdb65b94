package com.xk.goods.application.action.command.team;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.team.entity.TeamMemberEntity;
import com.xk.goods.interfaces.dto.req.team.TeamMemberIdentifierDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@AutoMappers({@AutoMapper(target = TeamMemberIdentifierDto.class),
        @AutoMapper(target = TeamMemberEntity.class),})
public class DeleteTeamMemberCommand extends AbstractActionCommand {

    private Long teamMemberId;
}
