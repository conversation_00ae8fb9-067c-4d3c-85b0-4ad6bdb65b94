package com.xk.goods.application.handler.query.gift;

import java.util.Objects;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.xk.domain.model.res.SysResourceEntity;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;
import com.xk.goods.application.action.query.gift.GiftReportInnerSearchQuery;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.repository.business.BusinessResRootQueryRepository;
import com.xk.goods.domain.repository.gift.GiftReportRootQueryRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.interfaces.dto.res.gift.GiftSearchRep;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class GiftReportInnerSearchQueryHandler
        implements IActionQueryManyHandler<GiftReportInnerSearchQuery, GiftSearchRep> {

    private final GiftReportRootQueryRepository giftReportRootQueryRepository;
    private final BusinessResRootQueryRepository businessResRootQueryRepository;
    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    @Override
    public Flux<GiftSearchRep> execute(Mono<GiftReportInnerSearchQuery> mono) {
        return mono.flatMapMany(query -> giftReportRootQueryRepository
                .searchByGoodsId(GoodsIdentifierConvertor.map(query.getGoodsId()))
                .filter(v -> query.getGiftReportId() == null
                        || Objects.equals(query.getGiftReportId(), v.getGiftReportId()))
                .map(entity -> {
                    GiftSearchRep dto = new GiftSearchRep();
                    BeanUtils.copyProperties(entity, dto);
                    if (entity.getSerialItemId() != null) {
                        dto.setSerialItemId(entity.getSerialItemId().getSerialItemId());
                    }
                    if (entity.getWinnerStatus() != null) {
                        dto.setWinnerStatus(entity.getWinnerStatus().getCode());
                    }
                    dto.setCreateTime(entity.getCreateValObj().getCreateTime());
                    return dto;
                }))
                .flatMap(dto -> businessResRootQueryRepository
                        .searchByBusinessGroup(BusinessResEntity.builder()
                                .businessGroupType(BusinessGroupTypeEnum.GIFT)
                                .businessId(dto.getGiftReportId()).build())
                        .flatMap(res -> sysResourceRootQueryRepository
                                .findById(IntegerIdentifier.builder().id(res.getResId()).build()))
                        .collectList().flatMap(resList -> {
                            dto.setResAddrList(
                                    resList.stream().map(SysResourceEntity::getAddr).toList());
                            return Mono.just(dto);
                        }));
    }
}
