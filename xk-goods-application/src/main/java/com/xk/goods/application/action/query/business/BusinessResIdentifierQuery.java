package com.xk.goods.application.action.query.business;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.xk.goods.domain.model.business.id.BusinessResIdentifier;
import com.xk.goods.enums.business.BusinessResTypeEnum;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = BusinessResIdentifier.class,
        uses = { IntegerIdentifier.class})})
public class BusinessResIdentifierQuery implements IActionQuery {

    private BusinessResTypeEnum businessResType;

    private Long businessId;

    private Integer resId;
}
