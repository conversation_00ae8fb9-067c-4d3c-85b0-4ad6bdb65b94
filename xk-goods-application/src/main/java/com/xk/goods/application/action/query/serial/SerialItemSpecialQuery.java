package com.xk.goods.application.action.query.serial;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.domain.model.serial.entity.SerialSpecialItemEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialItemSpecialReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@AutoMappers({@AutoMapper(target = SerialItemSpecialReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialSpecialItemEntity.class, reverseConvertGenerate = false)})
@EqualsAndHashCode(callSuper = true)
public class SerialItemSpecialQuery extends PagerQuery implements IActionQuery {

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 卡密名称
     */
    private String serialName;

    /**
     * 是否显示
     */
    private Integer isShow;

}
