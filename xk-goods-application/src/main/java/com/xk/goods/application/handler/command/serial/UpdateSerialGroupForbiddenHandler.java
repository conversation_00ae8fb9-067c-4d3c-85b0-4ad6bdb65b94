package com.xk.goods.application.handler.command.serial;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.serial.UpdateSerialGroupForbiddenCommand;
import com.xk.goods.domain.model.serial.SerialGroupRoot;
import com.xk.goods.domain.model.serial.entity.SerialGroupEntity;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.repository.serial.SerialGroupRootRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateSerialGroupForbiddenHandler implements IActionCommandHandler<UpdateSerialGroupForbiddenCommand, Void> {

    private final SerialGroupRootRepository serialGroupRootRepository;

    @Override
    public Mono<Void> execute(Mono<UpdateSerialGroupForbiddenCommand> commandMono) {
        return commandMono.flatMap(command -> {
            SerialGroupEntity entity = SerialGroupEntity.builder()
                    .serialGroupId(command.getSerialGroupId())
                    .status(command.getStatus())
                    .updateId(command.getUserId())
                    .updateTime(new Date())
                    .build();
            SerialGroupIdentifier identifier = SerialGroupIdentifier.builder().serialGroupId(command.getSerialGroupId()).build();
            return serialGroupRootRepository.update(SerialGroupRoot.builder()
                    .identifier(identifier).serialGroupEntity(entity)
                    .build());
        });
    }
}
