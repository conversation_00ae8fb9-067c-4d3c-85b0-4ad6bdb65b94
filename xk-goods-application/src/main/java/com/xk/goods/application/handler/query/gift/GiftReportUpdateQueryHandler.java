package com.xk.goods.application.handler.query.gift;

import java.util.Objects;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;
import com.xk.goods.application.action.query.gift.GiftReportUpdateQuery;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.repository.business.BusinessResRootQueryRepository;
import com.xk.goods.domain.repository.gift.GiftReportRootQueryRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.interfaces.dto.req.business.BusinessResReqDto;
import com.xk.goods.interfaces.dto.res.gift.GiftSearchUpdateRep;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class GiftReportUpdateQueryHandler
        implements IActionQueryManyHandler<GiftReportUpdateQuery, GiftSearchUpdateRep> {

    private final GiftReportRootQueryRepository giftReportRootQueryRepository;
    private final BusinessResRootQueryRepository businessResRootQueryRepository;
    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    @Override
    public Flux<GiftSearchUpdateRep> execute(Mono<GiftReportUpdateQuery> mono) {
        return mono.flatMapMany(query -> giftReportRootQueryRepository
                .searchByGoodsId(GoodsIdentifier.builder().goodsId(query.getGoodsId()).build())
                .filter(v -> Objects.equals(v.getCorpId(), query.getCorpId()))
                .switchIfEmpty(Mono
                        .error(new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                .map(entity -> {
                    GiftSearchUpdateRep dto = new GiftSearchUpdateRep();
                    dto.setGiftReportId(entity.getGiftReportId());
                    dto.setSerialItemId(entity.getSerialItemId().getSerialItemId());
                    dto.setSerialItemName(entity.getSerialItemName());
                    dto.setWinnerStatus(entity.getWinnerStatus().getCode());
                    dto.setGoodsId(entity.getGoodsId().getGoodsId());
                    return dto;
                }).switchIfEmpty(Mono.empty())
                .flatMap(dto -> businessResRootQueryRepository
                        .searchByBusinessGroup(BusinessResEntity.builder()
                                .businessGroupType(BusinessGroupTypeEnum.GIFT)
                                .businessId(dto.getGiftReportId()).build())
                        .flatMap(res -> sysResourceRootQueryRepository
                                .findById(IntegerIdentifier.builder().id(res.getResId()).build()))
                        .collectList()
                        .doOnNext(resList -> dto.setResDtoList(resList.stream().map(res -> {
                            BusinessResReqDto resDto = new BusinessResReqDto();
                            resDto.setAddr(res.getAddr());
                            resDto.setResId(res.getResId());
                            return resDto;
                        }).toList())).thenReturn(dto))
                .switchIfEmpty(Mono.empty()));
    }
}
