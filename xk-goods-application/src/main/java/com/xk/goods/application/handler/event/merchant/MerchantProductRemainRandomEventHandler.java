package com.xk.goods.application.handler.event.merchant;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.domain.event.merchant.MerchantProductRemainRandomEvent;
import com.xk.goods.domain.model.random.id.RandomDistributionIdentifier;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.service.merchant.MerchantProductRootService;
import com.xk.goods.domain.service.random.RandomDistributionDomainService;
import com.xk.goods.domain.service.specification.SpecificationRootService;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.merchant.MerchantProductIdentifierConvertor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class MerchantProductRemainRandomEventHandler
        extends AbstractEventVerticle<MerchantProductRemainRandomEvent> {

    private final MerchantProductRootService merchantProductRootService;
    private final SpecificationRootService specificationRootService;
    private final RandomDistributionDomainService randomDistributionDomainService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<MerchantProductRemainRandomEvent> mono) {
        return mono.flatMap(event -> specificationRootService
                .searchByGoodsId(GoodsIdentifierConvertor.map(event.getGoodsId()))
                .flatMap(specList -> {
                    List<RandomDistributionIdentifier> identifiers =
                            specList.stream().map(SpecificationRoot::getSpecificationEntity)
                                    .map(v -> RandomDistributionIdentifier.builder()
                                            .distributionId(v.getDistributionId()).build())
                                    .toList();
                    return randomDistributionDomainService
                            .createSurplusRandom(Flux.fromIterable(identifiers)).flatMap(
                                    identifier -> merchantProductRootService
                                            .getRoot(MerchantProductIdentifierConvertor
                                                    .map(event.getGoodsId()))
                                            .flatMap(productRoot -> {
                                                productRoot.getMerchantProductConfigEntity()
                                                        .setRemainRandomDistributionId(
                                                                identifier.distributionId());
                                                return merchantProductRootService
                                                        .updateRoot(Mono.just(productRoot));
                                            }));
                })).doOnError(throwable -> log.error("处理商品剩余随机事件失败", throwable));
    }
}
