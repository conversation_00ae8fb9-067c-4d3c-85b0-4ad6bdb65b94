package com.xk.goods.application.handler.command.goods;

import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.goods.application.action.command.goods.CreateGoodsCategoryCommand;
import com.xk.goods.application.commons.XkGoodsApplicationErrorEnum;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.goods.CreateGoodsCateEvent;
import com.xk.goods.domain.model.category.GoodsCategoryRoot;
import com.xk.goods.domain.model.category.entity.GoodsCategoryEntity;
import com.xk.goods.domain.repository.category.GoodsCategoryRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateGoodsCategoryHandler
        implements IActionCommandHandler<CreateGoodsCategoryCommand, Void> {

    private final GoodsCategoryRootRepository goodsCategoryRootRepository;
    private final Converter converter;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<CreateGoodsCategoryCommand> mono) {
        return mono.flatMap(command -> {
            // 命令执行函数
            Supplier<Mono<Long>> executeCommand = () -> execute(Mono.just(command), data -> {
                GoodsCategoryEntity entity =
                        this.converter.convert(data, GoodsCategoryEntity.class);
                return GoodsCategoryRoot.builder()
                        .identifier(entity.getIdentifier())
                        .goodsCategoryEntity(entity).build();
            }, goodsCategoryRootRepository::save).thenReturn(command.getGoodsCategoryId());

            // 事件发布函数
            Function<Long, Mono<Void>> publishEvent = id -> {
                EventRoot eventRoot = EventRoot.builder().domainEvent(CreateGoodsCateEvent.builder()
                        .identifier(EventRoot
                                .getCommonsDomainEventIdentifier(CreateGoodsCateEvent.class))
                        .parentId(command.getParentId()).categoryName(command.getCategoryName())
                        .sort(command.getSort()).status(command.getStatus()).goodsCategoryId(id)
                        .groupBusinessType(GroupBusinessTypeEnum.GOODS.getValue())
                        .groupId(Long.valueOf(command.getGoodsType())).build()).isQueue(true).build();

                return  eventRootService.publisheByMono(eventRoot)
                        .doOnSuccess(v -> log.info("CreateGoodsCateEvent事件发布完成: {}", id)).then();
            };

            return executeCommand.get().flatMap(publishEvent);
        }).onErrorResume(e -> {
            log.error("CreateSeriesCategoryCommand命令执行异常", e);
            return Mono.error(
                    new XkGoodsApplicationException(XkGoodsApplicationErrorEnum.APPLICATION_ERROR));
        });
    }
}
