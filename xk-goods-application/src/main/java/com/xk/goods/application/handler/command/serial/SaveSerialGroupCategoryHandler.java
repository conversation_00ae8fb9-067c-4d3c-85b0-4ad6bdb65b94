package com.xk.goods.application.handler.command.serial;

import java.util.Date;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.goods.application.action.command.serial.SaveSerialGroupCategoryCommand;
import com.xk.goods.domain.event.serial.SaveSerialGroupCategoryCateEvent;
import com.xk.goods.domain.model.serial.SerialGroupRoot;
import com.xk.goods.domain.model.serial.entity.SerialGroupCategoryEntity;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.repository.serial.SerialGroupCategoryRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaveSerialGroupCategoryHandler implements IActionCommandHandler<SaveSerialGroupCategoryCommand, Void> {

    private final SerialGroupCategoryRootRepository serialGroupCategoryRootRepository;

    private final Converter converter;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<SaveSerialGroupCategoryCommand> mono) {
        // 1、保存
        Function<SaveSerialGroupCategoryCommand, Mono<Void>> saveCommand = command -> {
            SerialGroupCategoryEntity entity = this.converter.convert(command, SerialGroupCategoryEntity.class);
            entity.setUpdateId(command.getUserId());
            entity.setCreateId(command.getUserId());
            entity.setUpdateTime(new Date());
            entity.setCreateTime(new Date());
            SerialGroupIdentifier serialGroupIdentifier = SerialGroupIdentifier.builder().serialGroupId(entity.getSerialGroupCategoryId()).build();
            return serialGroupCategoryRootRepository.save(SerialGroupRoot.builder().identifier(serialGroupIdentifier)
                    .serialGroupCategoryEntity(entity).build());
        };

        // 2、发事件
        Function<SaveSerialGroupCategoryCommand, Mono<Void>> publishEvent = command -> {
            Long groupId = (long) (command.getGroupType() * 10 + command.getBlockType());
            EventRoot eventRoot = EventRoot.builder().domainEvent(SaveSerialGroupCategoryCateEvent.builder()
                    .identifier(EventRoot
                            .getCommonsDomainEventIdentifier(SaveSerialGroupCategoryCateEvent.class))
                    .parentId(command.getParentId()).categoryName(command.getName())
                    .sort(0).status(1).serialGroupCategoryId(command.getSerialGroupCategoryId())
                    .groupBusinessType(GroupBusinessTypeEnum.SERIAL.getValue())
                    .groupId(groupId).build()).build();

            return eventRootService.publisheByMono(eventRoot)
                    .then()
                    .doOnSuccess(v -> log.info("SaveSerialGroupCategoryCateEvent事件发布完成: {}", command.getSerialGroupCategoryId()));
        };
        return mono.flatMap(command -> saveCommand.apply(command).then(publishEvent.apply(command).then()));
    }
}
