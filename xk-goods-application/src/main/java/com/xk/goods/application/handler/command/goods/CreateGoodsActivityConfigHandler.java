package com.xk.goods.application.handler.command.goods;

import java.util.Map;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.activity.CreateGoodsActivityConfigCommand;
import com.xk.goods.domain.model.activity.GoodsActivityRoot;
import com.xk.goods.domain.model.activity.entity.GoodsActivityConfigEntity;
import com.xk.goods.domain.repository.activity.GoodsActivityRootRepository;
import com.xk.goods.infrastructure.convertor.activity.GoodsActivityIdentifierConvertor;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateGoodsActivityConfigHandler
        implements IActionCommandHandler<CreateGoodsActivityConfigCommand, Void> {

    private final GoodsActivityRootRepository goodsActivityRootRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateGoodsActivityConfigCommand> mono) {
        return execute(mono, GoodsActivityConfigEntity.class, converter::convert,
                configEntity -> GoodsActivityRoot.builder()
                        .identifier(GoodsActivityIdentifierConvertor.map(configEntity.getGoodsId()))
                        .goodsActivityConfigEntityMap(
                                Map.of(configEntity.getActivityType(), configEntity))
                        .build(),
                goodsActivityRootRepository::save);
    }
}
