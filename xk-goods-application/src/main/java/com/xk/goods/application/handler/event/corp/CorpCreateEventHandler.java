package com.xk.goods.application.handler.event.corp;

import static com.xk.goods.application.commons.XkGoodsApplicationErrorEnum.APPLICATION_ERROR;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.corp.domain.event.corp.CorpCreateEvent;
import com.xk.domain.service.stock.StockRootService;
import com.xk.goods.application.action.command.stock.CreateStockCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CorpCreateEventHandler extends AbstractEventVerticle<CorpCreateEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final StockRootService stockRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CorpCreateEvent> mono) {
        return mono.flatMap(event -> stockRootService.generateId().flatMap(
                stockId -> commandDispatcher.executeCommand(Mono.just(new CreateStockCommand()),
                        CreateStockCommand.class, command -> {
                            command.setStockId(stockId);
                            command.buildCorp(event.getCorpInfoId(), event.getAdminUserId());
                            command.setTotalVirtualStock(event.getFreeQuota());
                            command.setTotalRealStock(event.getFreeQuota());
                            return command;
                        })
                        .doOnSuccess(
                                v -> log.info("CorpCreateEvent已创建业务资源: {}", event.getCorpInfoId()))
                        .onErrorResume(e -> {
                            log.error("处理CorpCreateEvent事件失败: {}", event, e);
                            return Mono.error(new XkGoodsApplicationException(APPLICATION_ERROR));
                        })))
                .then();
    }
}
