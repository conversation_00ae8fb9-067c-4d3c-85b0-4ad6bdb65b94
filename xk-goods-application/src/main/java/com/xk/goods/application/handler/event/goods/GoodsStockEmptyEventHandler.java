package com.xk.goods.application.handler.event.goods;

import java.util.Date;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.domain.event.goods.GoodsStockEmptyEvent;
import com.xk.goods.domain.event.merchant.MerchantProductGroupedEvent;
import com.xk.goods.domain.model.goods.GoodsRoot;
import com.xk.goods.domain.model.goods.valobj.GoodsListingValObj;
import com.xk.goods.domain.model.merchant.entity.MerchantProductEntity;
import com.xk.goods.domain.service.goods.GoodsRootService;
import com.xk.goods.domain.service.merchant.MerchantProductRootService;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.goods.ListingStatusEnum;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.merchant.MerchantProductIdentifierConvertor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsStockEmptyEventHandler extends AbstractEventVerticle<GoodsStockEmptyEvent> {

    private final GoodsRootService goodsRootService;
    private final MerchantProductRootService merchantProductRootService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<GoodsStockEmptyEvent> mono) {
        return mono.flatMap(event -> {
            Mono<GoodsRoot> getGoodsRoot =
                    goodsRootService.getRoot(GoodsIdentifierConvertor.map(event.getGoodsId()));

            Function<GoodsRoot, Mono<GoodsRoot>> doUpdateListing = root -> {
                GoodsListingValObj goodsListingValObj =
                        root.getGoodsEntity().getGoodsListingValObj();
                goodsListingValObj.setListingStatus(ListingStatusEnum.TIME_OUT_DOWN);
                goodsListingValObj.setActualDownTime(new Date());
                return goodsRootService.updateRoot(Mono.just(root)).thenReturn(root);
            };

            Function<GoodsRoot, Mono<GoodsRoot>> doMerchant = root -> {
                if (!GoodsTypeEnum.MERCHANT_PRODUCT.equals(root.getGoodsEntity().getGoodsType())) {
                    return Mono.empty();
                }
                return merchantProductRootService
                        .getRoot(MerchantProductIdentifierConvertor.map(event.getGoodsId()))
                        .flatMap(merchantRoot -> {
                            MerchantProductEntity merchantProductEntity =
                                    merchantRoot.getMerchantProductEntity();
                            merchantProductEntity.setSoldOutStatus(CommonStatusEnum.ENABLE);
                            merchantProductEntity.setSoldOutTime(new Date());
                            merchantProductEntity.setGroupStatus(CommonStatusEnum.ENABLE);
                            merchantProductEntity.setGroupTime(new Date());
                            return merchantProductRootService.updateRoot(Mono.just(merchantRoot));
                        }).thenReturn(root);
            };

            Function<GoodsRoot, Mono<Void>> publishEvent = root -> {
                EventRoot eventRoot = EventRoot.builder()
                        .domainEvent(MerchantProductGroupedEvent.builder()
                                .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                        MerchantProductGroupedEvent.class))
                                .goodsId(event.getGoodsId()).build())
                        .isQueue(true).build();
                return eventRootService.publisheByMono(eventRoot).doOnSuccess(
                        v -> log.info("MerchantProductGroupedEvent事件发布完成: {}", event.getGoodsId()))
                        .then();
            };

            Function<Throwable, Mono<Void>> onError = e -> {
                log.error("商品编号{}处理库存为空事件异常", event.getGoodsId(), e);
                return Mono.error(e);
            };

            return getGoodsRoot.flatMap(doUpdateListing).flatMap(doMerchant).flatMap(publishEvent)
                    .onErrorResume(onError);
        });
    }
}
