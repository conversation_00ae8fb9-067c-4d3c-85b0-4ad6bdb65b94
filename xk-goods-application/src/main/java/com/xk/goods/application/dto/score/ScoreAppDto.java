package com.xk.goods.application.dto.score;

import java.util.Date;

import com.xk.goods.domain.model.score.entity.ScoreEntity;
import com.xk.goods.infrastructure.convertor.merchant.ProductTypeEnumConvertor;
import com.xk.goods.interfaces.dto.res.score.ScoreResDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = ScoreResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = ScoreEntity.class, convertGenerate = false,
                uses = {ProductTypeEnumConvertor.class})})
public class ScoreAppDto {

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 规则版本
     */
    private Long ruleVersion;

    /**
     * 商品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;

    /**
     * 24小时销售金额分数
     */
    private Long saleAmount24h;

    /**
     * 累计销售金额分数
     */
    private Long saleAmountTotal;

    /**
     * 24小时购买人数分数
     */
    private Long buyerCount24h;

    /**
     * 累计购买人数分数
     */
    private Long buyerCountTotal;

    /**
     * 半小时销售数量
     */
    private Long saleCount30m;

    /**
     * 累计销售数量
     */
    private Long saleCountTotal;

    /**
     * 手动调整分数
     */
    private Long handleScore;

    /**
     * 总分
     */
    private Long totalScore;

    /**
     * 更新时间
     */
    private Date updateTime;
}
