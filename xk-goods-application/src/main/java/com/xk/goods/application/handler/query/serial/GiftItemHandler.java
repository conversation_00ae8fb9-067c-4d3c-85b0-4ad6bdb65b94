package com.xk.goods.application.handler.query.serial;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.object.goods.SpecificationValueObject;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.goods.application.action.query.serial.GiftItemQuery;
import com.xk.goods.domain.model.serial.entity.SerialSpecialItemEntity;
import com.xk.goods.domain.model.specification.id.SpecificationIdentifier;
import com.xk.goods.domain.repository.serial.SerialItemRootQueryRepository;
import com.xk.goods.domain.repository.specification.SpecificationRootQueryRepository;
import com.xk.goods.interfaces.dto.res.serial.GiftItemResDto;
import com.xk.goods.interfaces.dto.res.serial.SerialSpecialItemResDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 卡密组
 * 
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class GiftItemHandler implements IActionQueryHandler<GiftItemQuery, Pagination> {

    private final SerialItemRootQueryRepository serialItemRootQueryRepository;

    private final SelectorRootService selectorRootService;

    private final SpecificationRootQueryRepository specificationRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<GiftItemQuery> queryMono) {
        return queryMono.flatMap(query -> selectorRootService.getGoodsObject(query.getGoodsId())
                .flatMap(goodsObjectRoot -> {
                    // 返回信息
                    Pagination pagination = new Pagination();
                    pagination.setLimit(query.getLimit());
                    pagination.setOffset(query.getOffset());
                    pagination.setRecords(new ArrayList<>());

                    // 规则列表
                    List<SpecificationValueObject> specificationList =
                            goodsObjectRoot.getSpecificationList();

                    if (specificationList == null || specificationList.isEmpty()) {
                        // 如果没有规格列表，返回空的分页结果
                        pagination.setRecords(new ArrayList<>());
                        pagination.setTotalCount(0);
                        return Mono.just(pagination);
                    }

                    // 使用响应式方式获取所有赠品ID
                    return Flux
                            .fromIterable(
                                    specificationList)
                            .flatMap(
                                    specification -> specificationRootQueryRepository
                                            .getRoot(SpecificationIdentifier.builder()
                                                    .specificationId(
                                                            specification.getSpecificationId())
                                                    .build())
                                            .map(specificationRoot -> specificationRoot
                                                    .getSpecificationGiftValObj()
                                                    .getGiftBusinessIdList())
                                            .onErrorReturn(new ArrayList<>()) // 如果查询失败，返回空列表
                    ).collectList() // 收集所有的赠品ID列表
                            .flatMap(giftIdLists -> {
                                // 合并所有赠品ID列表
                                List<Long> giftBusinessIdList = new ArrayList<>();
                                for (List<Long> giftIds : giftIdLists) {
                                    if (giftIds != null) {
                                        giftBusinessIdList.addAll(giftIds);
                                    }
                                }

                                if (giftBusinessIdList.isEmpty()) {
                                    // 如果没有赠品ID，返回空的赠品列表
                                    GiftItemResDto resDto = new GiftItemResDto();
                                    resDto.setGiftList(new ArrayList<>());
                                    return Mono.just(pagination);
                                }

                                // 获取所有的卡密列表
                                SerialSpecialItemEntity entity = new SerialSpecialItemEntity();
                                entity.setSerialItemIdList(giftBusinessIdList);
                                entity.setSerialName(query.getKeyword());
                                pagination.setCriteria(CollectionHelper.converBeanToMap(entity));
                                return execute(queryMono, itemQuery -> {
                                    pagination
                                            .setCriteria(CollectionHelper.converBeanToMap(entity));
                                    return pagination;
                                }, serialItemRootQueryRepository::searchSpecialItem,
                                        SerialSpecialItemEntity.class).flatMap(rePagination -> {
                                            List<SerialSpecialItemEntity> entities =
                                                    rePagination.getRecords();
                                            List<SerialSpecialItemResDto> giftList =
                                                    new ArrayList<>();
                                            for (SerialSpecialItemEntity serialSpecialItemEntity : entities) {
                                                SerialSpecialItemResDto itemResDto =
                                                        new SerialSpecialItemResDto();
                                                BeanUtils.copyProperties(serialSpecialItemEntity,
                                                        itemResDto);
                                                giftList.add(itemResDto);
                                            }
                                            rePagination.setRecords(giftList);
                                            return Mono.just(rePagination);
                                        });

                            });
                }));
    }
}

