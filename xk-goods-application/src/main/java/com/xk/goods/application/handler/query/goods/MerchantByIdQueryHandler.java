package com.xk.goods.application.handler.query.goods;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.goods.application.action.query.business.BusinessResIdentifierQuery;
import com.xk.goods.application.action.query.goods.MerchantByIdQuery;
import com.xk.goods.domain.model.goods.GoodsRoot;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.merchant.MerchantProductRoot;
import com.xk.goods.domain.model.merchant.id.MerchantProductIdentifier;
import com.xk.goods.domain.model.price.id.PriceIdentifier;
import com.xk.goods.domain.model.specification.entity.SpecificationEntity;
import com.xk.goods.domain.service.goods.GoodsRootService;
import com.xk.goods.domain.service.merchant.MerchantProductRootService;
import com.xk.goods.domain.service.price.PriceRootService;
import com.xk.goods.domain.service.specification.SpecificationRootService;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;
import com.xk.goods.interfaces.dto.res.goods.GoodsSearchMerchantResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class MerchantByIdQueryHandler
        implements IActionQueryHandler<MerchantByIdQuery, GoodsSearchMerchantResDto> {

    private final GoodsRootService goodsRootService;
    private final SpecificationRootService specificationRootService;
    private final PriceRootService priceRootService;
    private final MerchantProductRootService merchantProductRootService;
    private final Converter converter;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public Mono<GoodsSearchMerchantResDto> execute(Mono<MerchantByIdQuery> query) {
        return query.flatMap(dto -> {
            // 1. 转换商品ID
            GoodsIdentifier goodsId = GoodsIdentifierConvertor.map(dto.getGoodsId());

            // 2. 异步并行获取商品Root和规格列表（减少串行等待）
            Mono<GoodsRoot> goodsRootMono = goodsRootService.getRoot(goodsId).cache();
            Mono<List<SpecificationEntity>> specsMono =
                    specificationRootService.searchEntityByGoodsId(goodsId).collectList();
            Mono<MerchantProductRoot> productRootMono = merchantProductRootService
                    .getRoot(MerchantProductIdentifier.builder().goodsIdentifier(goodsId).build());

            return Mono.zip(goodsRootMono, specsMono, productRootMono).flatMap(tuple -> {
                // 3. 转换基础DTO
                GoodsSearchMerchantResDto result =
                        converter.convert(tuple.getT1(), GoodsSearchMerchantResDto.class);

                // 4. 获取首个规格的价格（如果存在）
                Mono<Void> setAmount = Mono.justOrEmpty(tuple.getT2())
                        .filter(list -> !list.isEmpty()).flatMap(list -> {
                            PriceIdentifier priceId = list.getFirst().getPriceIdentifier();
                            return priceRootService.getRoot(priceId)
                                    .doOnNext(priceRoot -> result.setAmount(priceRoot
                                            .getPriceItemEntityList().getFirst().getAmount()))
                                    .then();
                        });

                Mono<Void> setPicture = Flux.fromIterable(tuple.getT1().getResIdentifierList())
                        .flatMap(res -> this.queryDispatcher.executeQuery(Mono.just(res),
                                BusinessResIdentifierQuery.class, BusinessResDto.class))
                        .collectList().doOnNext(result::setProductPicList).then();

                Mono<GoodsSearchMerchantResDto> setMerchant = Mono.justOrEmpty(tuple.getT3())
                        .map(root -> converter.convert(root, result));
                // 4. 获取首个规格的价格（如果存在）
                return setPicture.then(setAmount).then(setMerchant);
            });
        });
    }
}
