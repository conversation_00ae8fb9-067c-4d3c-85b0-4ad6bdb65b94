package com.xk.goods.application.handler.command.goods;

import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.goods.application.action.command.goods.DeleteGoodsCategoryCommand;
import com.xk.goods.domain.event.goods.DeleteGoodsCateEvent;
import com.xk.goods.domain.model.category.GoodsCategoryRoot;
import com.xk.goods.domain.model.category.entity.GoodsCategoryEntity;
import com.xk.goods.domain.model.category.id.GoodsCategoryIdentifier;
import com.xk.goods.domain.repository.category.GoodsCategoryRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteGoodsCategoryHandler
        implements IActionCommandHandler<DeleteGoodsCategoryCommand, Void> {

    private final GoodsCategoryRootRepository goodsCategoryRootRepository;
    private final Converter converter;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<DeleteGoodsCategoryCommand> mono) {
        return mono.flatMap(command -> {
            // 命令执行函数
            Supplier<Mono<Long>> executeFunc = () -> execute(Mono.just(command), data -> {
                GoodsCategoryEntity entity =
                        this.converter.convert(data, GoodsCategoryEntity.class);
                return GoodsCategoryRoot.builder()
                        .identifier(GoodsCategoryIdentifier.builder()
                                .goodsCategoryId(entity.getGoodsCategoryId()).build())
                        .goodsCategoryEntity(entity).build();
            }, goodsCategoryRootRepository::remove).thenReturn(command.getGoodsCategoryId());

            // 事件发布函数
            Function<Long, Mono<Void>> publishEvent = id -> {
                EventRoot eventRoot = EventRoot.builder().domainEvent(DeleteGoodsCateEvent.builder()
                        .identifier(EventRoot
                                .getCommonsDomainEventIdentifier(DeleteGoodsCateEvent.class))
                        .goodsCategoryId(command.getGoodsCategoryId())
                        .groupBusinessType(GroupBusinessTypeEnum.GOODS.getValue())
                        .build()).isQueue(true).build();

                return  eventRootService.publisheByMono(eventRoot)
                        .doOnSuccess(v -> log.info("DeleteGoodsCateEvent事件发布完成: {}", id)).then();
            };

            return executeFunc.get().flatMap(publishEvent);
        });
    }
}
