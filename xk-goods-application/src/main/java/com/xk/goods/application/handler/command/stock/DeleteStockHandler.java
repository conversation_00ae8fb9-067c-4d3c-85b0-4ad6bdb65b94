package com.xk.goods.application.handler.command.stock;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.domain.model.stock.StockRoot;
import com.xk.domain.model.stock.id.StockIdentifier;
import com.xk.domain.service.stock.StockRootService;
import com.xk.goods.application.action.command.stock.DeleteStockCommand;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteStockHandler implements IActionCommandHandler<DeleteStockCommand, Void> {

    private final StockRootService stockRootService;

    @Override
    public Mono<Void> execute(Mono<DeleteStockCommand> mono) {
        return mono.flatMap(command -> stockRootService.removeRoot(buildRoot(command)));
    }

    private Mono<StockRoot> buildRoot(DeleteStockCommand command) {
        return stockRootService.getRootNoDeduction(
                StockIdentifier.builder().stockId(command.getStockId()).build());
    }
}
