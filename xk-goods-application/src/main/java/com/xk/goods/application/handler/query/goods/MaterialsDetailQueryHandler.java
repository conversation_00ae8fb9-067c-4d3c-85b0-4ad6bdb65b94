package com.xk.goods.application.handler.query.goods;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.goods.application.action.query.business.BusinessResIdentifierQuery;
import com.xk.goods.application.action.query.goods.MaterialsDetailQuery;
import com.xk.goods.domain.model.goods.GoodsRoot;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.price.entity.PriceItemEntity;
import com.xk.goods.domain.model.price.id.PriceIdentifier;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.model.specification.entity.SpecificationEntity;
import com.xk.goods.domain.service.goods.GoodsRootService;
import com.xk.goods.domain.service.price.PriceRootService;
import com.xk.goods.domain.service.specification.SpecificationRootService;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;
import com.xk.goods.interfaces.dto.res.goods.MaterialDetailResDto;
import com.xk.goods.interfaces.dto.res.goods.SpecResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class MaterialsDetailQueryHandler
        implements IActionQueryHandler<MaterialsDetailQuery, MaterialDetailResDto> {

    private final GoodsRootService goodsRootService;
    private final SpecificationRootService specificationRootService;
    private final PriceRootService priceRootService;
    private final Converter converter;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public Mono<MaterialDetailResDto> execute(Mono<MaterialsDetailQuery> query) {
        return query.flatMap(dto -> {
            // 1. 转换商品ID
            GoodsIdentifier goodsId = GoodsIdentifierConvertor.map(dto.getGoodsId());

            // 2. 异步并行获取商品Root和规格列表（减少串行等待）
            Mono<GoodsRoot> goodsRootMono = goodsRootService.getRoot(goodsId).cache();
            Mono<List<SpecificationRoot>> specsMono =
                    specificationRootService.searchByGoodsId(goodsId);

            return Mono.zip(goodsRootMono, specsMono).flatMap(tuple -> {
                // 3. 转换基础DTO
                MaterialDetailResDto result =
                        converter.convert(tuple.getT1(), MaterialDetailResDto.class);

                Mono<Void> setPicture = Flux.fromIterable(tuple.getT1().getResIdentifierList())
                        .flatMap(res -> this.queryDispatcher.executeQuery(Mono.just(res),
                                BusinessResIdentifierQuery.class, BusinessResDto.class))
                        .collectList().doOnNext(result::setProductPicList).then();

                // 4. 获取首个规格的价格（如果存在）
                Mono<MaterialDetailResDto> setAmount = Mono.justOrEmpty(tuple.getT2())
                        .flatMapMany(Flux::fromIterable).flatMap(specRoot -> {
                            SpecificationEntity specificationEntity =
                                    specRoot.getSpecificationEntity();
                            PriceIdentifier priceId = specificationEntity.getPriceIdentifier();
                            result.setUnitType(specificationEntity.getUnitType());
                            return priceRootService.getRoot(priceId).doOnNext(priceRoot -> {
                                PriceItemEntity priceItem =
                                        priceRoot.getPriceItemEntityList().getFirst();
                                if (priceItem != null) {
                                    List<SpecResDto> specResDtoList = Objects.requireNonNullElse(
                                            result.getSpecDtoList(), new ArrayList<>());
                                    specResDtoList.add(SpecResDto.builder()
                                            .specificationId(
                                                    specificationEntity.getSpecificationId())
                                            .specName(specificationEntity
                                                    .getSpecificationNameValObj().getSpecName())
                                            .amount(priceItem.getAmount())
                                            .currencyType(priceItem.getCurrencyType().getCode())
                                            .unitType(specificationEntity.getUnitType()).build());
                                    result.setSpecDtoList(specResDtoList);
                                }
                            });
                        }).then().thenReturn(result);

                Function<MaterialDetailResDto, Mono<MaterialDetailResDto>> sortSpec = resDto -> {
                    resDto.getSpecDtoList()
                            .sort(Comparator.comparing(SpecResDto::getSpecificationId));
                    return Mono.just(resDto);
                };

                // 4. 获取首个规格的价格（如果存在）
                return setPicture.then(setAmount).flatMap(sortSpec).thenReturn(result);
            });
        });
    }
}
