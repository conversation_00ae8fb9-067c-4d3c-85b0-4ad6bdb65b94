package com.xk.goods.application.query.goods;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.math3.util.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.config.application.action.query.item.ItemValueIdQuery;
import com.myco.mydata.config.interfaces.dto.req.item.ItemValueIdentifierReqDto;
import com.myco.mydata.config.interfaces.dto.rsp.item.ItemValueRspDto;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.object.user.UserObjectRoot;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.config.enums.dict.CorpRuleTypeEnum;
import com.xk.config.interfaces.dto.rsp.res.DictRspDto;
import com.xk.goods.application.action.query.goods.PickCategoryQuery;
import com.xk.goods.application.action.query.goods.PickStockQuery;
import com.xk.goods.domain.repository.merchant.MerchantProductRootQueryRepository;
import com.xk.goods.infrastructure.convertor.merchant.MerchantProductIdentifierConvertor;
import com.xk.goods.interfaces.api.query.goods.MerchantProductAppDocQueryService;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdRequireReqDto;
import com.xk.goods.interfaces.dto.req.goods.GoodsRuleReqDto;
import com.xk.goods.interfaces.dto.req.goods.PickStockReq;
import com.xk.goods.interfaces.dto.req.goods.merchant.GoodsIdAbstractReqDto;
import com.xk.goods.interfaces.dto.res.goods.GoodsRuleRspDto;
import com.xk.goods.interfaces.dto.res.goods.PickCategoryRsp;
import com.xk.goods.interfaces.dto.res.goods.PickStockRsp;
import com.xk.goods.interfaces.dto.res.goods.merchant.BuyerResDto;
import com.xk.goods.interfaces.dto.res.goods.merchant.PromotionResDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class MerchantProductAppQueryServiceImpl implements MerchantProductAppDocQueryService {

    private final List<Long> userIds = Arrays.asList(626L, 627L, 632L, 633L, 609L);
    private final MerchantProductRootQueryRepository merchantProductRootQueryRepository;
    private final SelectorRootService selectorRootService;
    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    @BusiCode
    @Override
    public Mono<List<PromotionResDto>> searchPromotionDetail(Mono<GoodsIdAbstractReqDto> mono) {
        return mono.flatMap(dto -> merchantProductRootQueryRepository
                .searchPromotionValObj(MerchantProductIdentifierConvertor.map(dto.getGoodsId()))
                .map(v -> PromotionResDto.builder().promotionContent(v.getPromotionContent())
                        .promotionSequence(v.getPromotionSequence())
                        .promotionTitle(v.getPromotionTitle()).build())
                .collectList());
    }

    @BusiCode
    @Override
    public Mono<List<BuyerResDto>> searchBuyerDetail(Mono<GoodsIdRequireReqDto> mono) {
        return Flux.fromIterable(userIds).flatMap(userId -> {
            Mono<UserObjectRoot> userObject = selectorRootService.getUserObject(userId);
            return userObject.flatMap(object -> {
                UserDataObjectEntity userDataObjectEntity = object.getUserDataObjectEntity();
                return Mono.just(BuyerResDto.builder().userId(userId)
                        .nickname(userDataObjectEntity.getNickname()).buyTime(new Date())
                        .picId(userDataObjectEntity.getPicId()).buyCount((int) (userId % 5))
                        .build());
            });
        }).collectList();
    }

    @BusiCode
    @Override
    public Mono<List<PickCategoryRsp>> searchPickCategory(Mono<GoodsIdRequireReqDto> mono) {
        return mono
                .flatMap(req -> merchantProductRootQueryRepository
                        .findEntity(MerchantProductIdentifierConvertor.map(req.getGoodsId()))
                        .flatMap(entity -> actionQueryManyDispatcher
                                .executeQuery(Mono.just(new PickCategoryQuery()),
                                        PickCategoryQuery.class, query -> {
                                            query.setSerialGroupId(entity.getSerialGroupIdentifier()
                                                    .getSerialGroupId());
                                            return query;
                                        }, PickCategoryRsp.class)
                                .collectList()));
    }

    @BusiCode
    @Override
    public Mono<List<PickStockRsp>> searchPickStock(Mono<PickStockReq> mono) {
        return mono.flatMap(req -> actionQueryManyDispatcher
                .executeQuery(Mono.just(req), PickStockQuery.class, PickStockRsp.class)
                .collectList());
    }

    @BusiCode
    @Override
    public Mono<GoodsRuleRspDto> goodsRule(Mono<GoodsRuleReqDto> mono) {
        return mono
                .flatMap(dto -> Flux.fromArray(CorpRuleTypeEnum.values()).flatMap(corpRuleType -> {
                    ItemValueIdentifierReqDto identifierReqDto = new ItemValueIdentifierReqDto();
                    identifierReqDto.setItemKey(corpRuleType.name());
                    identifierReqDto.setItemValue(String.valueOf(corpRuleType.getCode()));
                    identifierReqDto.setLang(SystemLanguageLocale.ZH.name());
                    identifierReqDto.setSessionId(dto.getSessionId());
                    return actionQueryDispatcher.executeQuery(Mono.just(identifierReqDto),
                            ItemValueIdQuery.class, ItemValueRspDto.class).map(itemValueRspDto -> {
                                DictRspDto dictRspDto = JSON.parseObject(
                                        itemValueRspDto.getItemJson(), DictRspDto.class);
                                return new Pair<>(corpRuleType, dictRspDto);
                            }).defaultIfEmpty(new Pair<>(corpRuleType, new DictRspDto()));
                }).collectMap(Pair::getKey, // 以CorpRuleTypeEnum为key
                        Pair::getValue // 以DictRspDto为value
                ).map(resultMap -> {
                    GoodsRuleRspDto response = new GoodsRuleRspDto();

                    // 根据不同类型设置相应属性
                    for (Integer type : dto.getTypes()) {
                        if (type == CorpRuleTypeEnum.ANNOUNCE_INFO.getCode()) {
                            BeanUtils.copyProperties(resultMap.get(CorpRuleTypeEnum.ANNOUNCE_INFO),
                                    response);
                        } else if (type == CorpRuleTypeEnum.GIFT_RULE.getCode()) {
                            response.setGiftRule(
                                    resultMap.get(CorpRuleTypeEnum.GIFT_RULE).getContent());
                        } else if (type == CorpRuleTypeEnum.PURCHASE_NOTE.getCode()) {
                            response.setPurchaseNote(
                                    resultMap.get(CorpRuleTypeEnum.PURCHASE_NOTE).getContent());
                        } else if (type == CorpRuleTypeEnum.RISK_WARNING.getCode()) {
                            response.setRiskWarning(
                                    resultMap.get(CorpRuleTypeEnum.RISK_WARNING).getContent());
                        } else if (type == CorpRuleTypeEnum.GIFT_INFO.getCode()) {
                            response.setGiftInfo(
                                    resultMap.get(CorpRuleTypeEnum.GIFT_INFO).getContent());
                        }
                    }
                    response.setLastUpdateTime(new Date());
                    return response;
                }));
    }
}
