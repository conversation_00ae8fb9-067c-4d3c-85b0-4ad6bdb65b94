package com.xk.goods.application.query.score;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.goods.application.action.query.score.ScoreRuleSearchQuery;
import com.xk.goods.interfaces.dto.res.score.ScoreRuleResDto;
import com.xk.goods.interfaces.query.score.ScoreRuleQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScoreRuleQueryServiceImpl implements ScoreRuleQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    @Override
    public Mono<ScoreRuleResDto> search(Mono<RequireSessionDto> mono) {
        return actionQueryDispatcher.executeQuery(Mono.just(new ScoreRuleSearchQuery()),
                ScoreRuleSearchQuery.class, ScoreRuleResDto.class);
    }
}
