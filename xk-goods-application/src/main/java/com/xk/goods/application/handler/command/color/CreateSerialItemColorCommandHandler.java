package com.xk.goods.application.handler.command.color;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.color.CreateSerialItemColorCommand;
import com.xk.goods.domain.service.color.SerialItemColorRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 创建卡密特效命令处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CreateSerialItemColorCommandHandler
        implements IActionCommandHandler<CreateSerialItemColorCommand, Void> {

    private final Converter converter;
    private final SerialItemColorRootService serialItemColorRootService;

    @Override
    public Mono<Void> execute(Mono<CreateSerialItemColorCommand> mono) {
        return mono.flatMap(
                command -> serialItemColorRootService.saveRoot(command.buildRoot(converter)));
    }
}
