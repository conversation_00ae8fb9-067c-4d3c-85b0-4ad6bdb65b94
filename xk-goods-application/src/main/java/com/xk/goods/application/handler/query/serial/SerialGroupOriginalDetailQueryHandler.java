package com.xk.goods.application.handler.query.serial;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.goods.application.action.query.serial.SerialGroupOriginalDetailQuery;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.repository.serial.SerialGroupRootQueryRepository;
import com.xk.goods.interfaces.dto.res.serial.SerialGroupOriginalDetailResDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * 卡密组
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SerialGroupOriginalDetailQueryHandler implements
        IActionQueryHandler<SerialGroupOriginalDetailQuery, SerialGroupOriginalDetailResDto> {

    private final SerialGroupRootQueryRepository serialGroupRootQueryRepository;

    @Override
    public Mono<SerialGroupOriginalDetailResDto> execute(
            Mono<SerialGroupOriginalDetailQuery> queryMono) {
        return queryMono
                .flatMap(query -> serialGroupRootQueryRepository
                        .findById(SerialGroupIdentifier.builder()
                                .serialGroupId(query.getSerialGroupId()).build())
                        .flatMap(entity -> {
                            SerialGroupOriginalDetailResDto dto =
                                    new SerialGroupOriginalDetailResDto();
                            BeanUtils.copyProperties(entity, dto);
                            dto.setFileName(dto.getName() + "条目明细.xlsx");
                            return Mono.just(dto);
                        }));
    }
}

