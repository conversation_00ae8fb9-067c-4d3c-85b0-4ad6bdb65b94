package com.xk.goods.application.query.goods;

import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.commons.constant.SystemConstant;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.goods.application.action.query.goods.MallDetailQuery;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdReqDto;
import com.xk.goods.interfaces.dto.req.goods.mall.MallSearchPagerReqDto;
import com.xk.goods.interfaces.dto.res.goods.MallDetailResDto;
import com.xk.goods.interfaces.query.goods.MallProductQueryService;
import com.xk.search.interfaces.dto.req.goods.SearchGoodsMallReqDto;
import com.xk.search.interfaces.query.goods.MallQueryService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class MallProductQueryServiceImpl implements MallProductQueryService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final MallQueryService mallQueryService;

    @BusiCode
    @Override
    public Mono<Pagination> searchPager(Mono<MallSearchPagerReqDto> mono) {
        return mono.flatMap(dto -> {
            SearchGoodsMallReqDto req =
                    SearchGoodsMallReqDto.builder().goodsName(dto.getGoodsName())
                            .nodeId(dto.getNodeId()).listingStatus(dto.getListingStatus()).build();
            req.setSessionId(dto.getSessionId());
            req.setSort(dto.getSort());
            req.setOrder(SystemConstant.BusinessOrderType.valueOf(dto.getOrder()));
            req.setCurrentPage(dto.getCurrentPage());
            req.setLimit(dto.getLimit());
            req.setOffset(dto.getOffset());
            return mallQueryService.searchGoodsByIds(Mono.just(req))
                    .flatMap(result -> Flux.fromIterable(result.getRecords())
                            .map(v -> objectMapper.convertValue(v, GoodsIdReqDto.class))
                            .flatMap(resDto -> actionQueryDispatcher.executeQuery(resDto,
                                    MallDetailQuery.class, MallDetailResDto.class))
                            .collectList().doOnNext(result::setRecords).thenReturn(result));
        });
    }

    @BusiCode
    @Override
    public Mono<MallDetailResDto> detail(Mono<GoodsIdReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, MallDetailQuery.class,
                MallDetailResDto.class);
    }
}
