package com.xk.goods.application.dto.merchant;

import java.io.Serializable;

import com.xk.goods.domain.model.merchant.valobj.DiscountValObj;
import com.xk.goods.interfaces.dto.req.goods.merchant.DiscountReqDto;
import com.xk.goods.interfaces.dto.res.goods.merchant.DiscountResDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = DiscountReqDto.class, convertGenerate = false),
        @AutoMapper(target = DiscountResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = DiscountValObj.class),})
public class DiscountDto implements Serializable {
    /**
     * 满减阈值
     */
    private Long thresholdAmount;

    /**
     * 减免金额
     */
    private Long discountAmount;
}
