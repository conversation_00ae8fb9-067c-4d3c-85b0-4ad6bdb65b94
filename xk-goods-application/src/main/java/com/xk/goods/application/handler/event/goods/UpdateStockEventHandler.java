package com.xk.goods.application.handler.event.goods;

import org.springframework.stereotype.Component;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.event.stock.UpdateStockEvent;
import com.xk.domain.service.stock.StockRootService;
import com.xk.infrastructure.convertor.stock.StockIdentifierConvertor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateStockEventHandler extends AbstractEventVerticle<UpdateStockEvent> {

    private final StockRootService stockRootService;

    @Override
    public Mono<Void> handle(Mono<UpdateStockEvent> mono) {
        return mono.flatMap(event -> stockRootService.refreshStockCache(
                StockIdentifierConvertor.map(event.getStockId()), event.getUpdateTime(),
                event.getStockBusinessType()));
    }
}
