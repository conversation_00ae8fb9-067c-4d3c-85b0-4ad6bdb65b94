package com.xk.goods.application.action.query.serial;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.domain.model.serial.entity.SerialGroupEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupQuickReqDto;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMappers({@AutoMapper(target = SerialGroupReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialGroupQuickReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialGroupEntity.class, reverseConvertGenerate = false)})
public class SerialGroupQuery extends PagerQuery implements IActionQuery {
    /**
     * 卡密组类目ids【全部为空，列表传二级类目ID】
     */
    private List<Long> serialGroupCategoryIds;

    /**
     * 卡密组名称
     */
    private String name;

    /**
     * 状态（0：正常，1：禁用）
     */
    private Integer status;

    /**
     * 卡密组类型：1球队卡密组；2自定义卡密组
     */
    private Integer groupType;

    /**
     * 板块类型：1球队；2动漫
     */
    private Integer blockType;

    /**
     * 关键字
     */
    private String keyword;
}
