package com.xk.goods.application.action.command.specification;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.specification.entity.SpecificationEntity;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SpecificationEntity.class, reverseConvertGenerate = false)})
public class DeleteSpecificationCommand extends AbstractActionCommand {

    /**
     * 商品规格id
     */
    private Long specificationId;
}
