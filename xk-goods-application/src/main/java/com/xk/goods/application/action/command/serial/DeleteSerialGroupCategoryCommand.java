package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.serial.entity.SerialGroupCategoryEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupCategoryDeleteReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialGroupCategoryDeleteReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialGroupCategoryEntity.class, reverseConvertGenerate = false)})
public class DeleteSerialGroupCategoryCommand extends AbstractActionCommand {

    private Long serialGroupCategoryId;

    private Long userId;

    private Long parentId;

}
