package com.xk.goods.application.handler.query.business;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;
import com.xk.goods.application.action.query.business.BusinessResIdentifierQuery;
import com.xk.goods.domain.model.business.id.BusinessResIdentifier;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class BusinessResIdentifierQueryHandler
        implements IActionQueryHandler<BusinessResIdentifierQuery, BusinessResDto> {

    private final Converter converter;
    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    @Override
    public Mono<BusinessResDto> execute(Mono<BusinessResIdentifierQuery> mono) {
        return mono.flatMap(query -> {
            Function<BusinessResIdentifier, Mono<BusinessResDto>> queryResource =
                    id -> sysResourceRootQueryRepository
                            .findById(IntegerIdentifier.builder().id(query.getResId()).build())
                            .map(resource -> BusinessResDto.builder().resId(resource.getResId())
                                    .addr(resource.getAddr())
                                    .businessResType(id.getBusinessResType().getCode()).build());
            return queryResource.apply(converter.convert(query, BusinessResIdentifier.class));
        });
    }
}
