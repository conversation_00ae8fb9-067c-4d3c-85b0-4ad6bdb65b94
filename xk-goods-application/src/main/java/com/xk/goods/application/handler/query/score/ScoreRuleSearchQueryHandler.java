package com.xk.goods.application.handler.query.score;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.goods.application.action.query.score.ScoreRuleSearchQuery;
import com.xk.goods.domain.model.score.entity.ScoreRuleEntity;
import com.xk.goods.domain.repository.score.ScoreRuleRootQueryRepository;
import com.xk.goods.interfaces.dto.res.score.ScoreRuleResDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class ScoreRuleSearchQueryHandler
        implements IActionQueryHandler<ScoreRuleSearchQuery, ScoreRuleResDto> {

    private final ScoreRuleRootQueryRepository scoreRuleRootQueryRepository;

    @BusiCode
    @Override
    public Mono<ScoreRuleResDto> execute(Mono<ScoreRuleSearchQuery> query) {
        ScoreRuleResDto.ScoreRuleResDtoBuilder builder = ScoreRuleResDto.builder();
        return scoreRuleRootQueryRepository.searchAllRule().flatMap(entity -> {
            switch (entity.getScoreRuleType()) {
                case BASIC -> builder.corpPayment7dayRule(entity.getCorpPayment7dayRule());
                case FORTUNE_BOX -> builder.fortuneRuleDto(getRuleResDto(entity));
                case EDGE_BOX -> builder.edgeRuleDto(getRuleResDto(entity));
                case RUBBED_CARD_PACK -> builder.rubbedRuleDto(getRuleResDto(entity));
                case ORIGINAL_BOX -> builder.originalRuleDto(getRuleResDto(entity));
            }
            return Mono.empty();
        }).collectList().map(v -> builder.build());
    }

    private ScoreRuleResDto.RuleResDto getRuleResDto(ScoreRuleEntity entity) {
        ScoreRuleResDto.RuleResDto.RuleResDtoBuilder resBuilder =
                ScoreRuleResDto.RuleResDto.builder();
        resBuilder.saleAmount24hRule(entity.getSaleAmount24hRule());
        resBuilder.saleAmountTotalRule(entity.getSaleAmountTotalRule());
        resBuilder.buyerCount24hRule(entity.getBuyerCount24hRule());
        resBuilder.buyerCountTotalRule(entity.getBuyerCountTotalRule());
        resBuilder.saleCount30mRule(entity.getSaleCount30mRule());
        resBuilder.saleCountTotalRule(entity.getSaleCountTotalRule());
        return resBuilder.build();
    }
}
