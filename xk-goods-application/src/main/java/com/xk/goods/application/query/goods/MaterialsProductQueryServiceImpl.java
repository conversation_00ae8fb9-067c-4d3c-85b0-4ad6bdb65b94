package com.xk.goods.application.query.goods;

import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.commons.constant.SystemConstant;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.application.action.query.goods.MaterialsDetailQuery;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdReqDto;
import com.xk.goods.interfaces.dto.req.goods.materials.MaterialsSearchPagerReqDto;
import com.xk.goods.interfaces.dto.res.goods.MaterialAppPagerResDto;
import com.xk.goods.interfaces.dto.res.goods.MaterialDetailResDto;
import com.xk.goods.interfaces.query.goods.MaterialsProductQueryService;
import com.xk.search.interfaces.dto.req.goods.SearchGoodsMaterialsReqDto;
import com.xk.search.interfaces.query.goods.MaterialsQueryService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class MaterialsProductQueryServiceImpl implements MaterialsProductQueryService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final MaterialsQueryService materialsQueryService;
    private final Converter converter;

    @BusiCode
    @Override
    public Mono<Pagination> searchPager(Mono<MaterialsSearchPagerReqDto> mono) {
        return mono.flatMap(dto -> {
            SearchGoodsMaterialsReqDto req = SearchGoodsMaterialsReqDto.builder()
                    .goodsName(dto.getGoodsName()).listingStatus(dto.getListingStatus())
                    .showStatus(dto.getShowStatus()).build();
            req.setSessionId(dto.getSessionId());
            req.setSort(dto.getSort());
            req.setOrder(SystemConstant.BusinessOrderType.valueOf(dto.getOrder()));
            req.setCurrentPage(dto.getCurrentPage());
            req.setLimit(dto.getLimit());
            req.setOffset(dto.getOffset());
            return materialsQueryService.searchGoodsByIds(Mono.just(req))
                    .flatMap(result -> Flux.fromIterable(result.getRecords())
                            .map(v -> objectMapper.convertValue(v, GoodsIdReqDto.class))
                            .flatMap(resDto -> actionQueryDispatcher.executeQuery(Mono.just(resDto),
                                    MaterialsDetailQuery.class, MaterialDetailResDto.class))
                            .collectList().doOnNext(result::setRecords).thenReturn(result));
        });
    }

    @BusiCode
    @Override
    public Mono<MaterialDetailResDto> detail(Mono<GoodsIdReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, MaterialsDetailQuery.class,
                MaterialDetailResDto.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> appSearchPager(Mono<RequireSessionDtoPager> mono) {
        return mono.flatMap(dto -> {
            SearchGoodsMaterialsReqDto req = SearchGoodsMaterialsReqDto.builder()
                    .showStatus(CommonStatusEnum.ENABLE.getCode()).build();
            req.setSessionId(dto.getSessionId());
            req.setSort(dto.getSort());
            req.setOrder(SystemConstant.BusinessOrderType.valueOf(dto.getOrder()));
            req.setCurrentPage(dto.getCurrentPage());
            req.setLimit(dto.getLimit());
            req.setOffset(dto.getOffset());
            return materialsQueryService.searchGoodsByIds(Mono.just(req))
                    .flatMap(result -> Flux.fromIterable(result.getRecords())
                            .map(v -> objectMapper.convertValue(v, GoodsIdReqDto.class))
                            .flatMap(resDto -> actionQueryDispatcher.executeQuery(Mono.just(resDto),
                                    MaterialsDetailQuery.class, MaterialDetailResDto.class))
                            .map(v -> converter.convert(v, MaterialAppPagerResDto.class))
                            .collectList().doOnNext(result::setRecords).thenReturn(result));
        });
    }
}
