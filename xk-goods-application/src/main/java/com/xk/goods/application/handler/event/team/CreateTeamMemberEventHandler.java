package com.xk.goods.application.handler.event.team;

import static com.xk.goods.application.commons.XkGoodsApplicationErrorEnum.APPLICATION_ERROR;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.application.action.command.business.CreateBusinessResCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.team.CreateTeamMemberEvent;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateTeamMemberEventHandler extends AbstractEventVerticle<CreateTeamMemberEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final Converter converter;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CreateTeamMemberEvent> mono) {
        return mono.flatMap(event -> Flux.fromIterable(event.getBaseBusinessResList()).index()
                .flatMap(tuple2 -> commandDispatcher.executeCommand(Mono.just(tuple2.getT2()),
                        CreateBusinessResCommand.class, command -> {
                            command.setSort(Math.toIntExact(tuple2.getT1()));
                            return command;
                        }).doOnSuccess(v -> log.info("CreateTeamMemberEvent已创建业务资源: {}",
                                tuple2.getT2().getResId())))
                .onErrorResume(e -> {
                    log.error("处理CreateTeamMemberEvent事件失败: {}", event, e);
                    return Mono.error(new XkGoodsApplicationException(APPLICATION_ERROR));
                }).then());
    }
}
