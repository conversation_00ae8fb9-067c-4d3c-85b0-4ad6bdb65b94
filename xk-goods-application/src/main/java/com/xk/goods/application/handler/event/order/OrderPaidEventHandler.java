package com.xk.goods.application.handler.event.order;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.merchant.MerchantProductRoot;
import com.xk.goods.domain.model.merchant.valobj.BuyerCountValObj;
import com.xk.goods.domain.model.merchant.valobj.OrderValObj;
import com.xk.goods.domain.model.score.entity.ScoreEntity;
import com.xk.goods.domain.model.score.id.ScoreIdentifier;
import com.xk.goods.domain.repository.score.ScoreRootQueryRepository;
import com.xk.goods.domain.service.merchant.MerchantProductRootService;
import com.xk.goods.domain.service.score.ScoreRootService;
import com.xk.goods.infrastructure.convertor.merchant.MerchantProductIdentifierConvertor;
import com.xk.order.domain.event.order.OrderPaidEvent;
import com.xk.order.enums.order.OrderTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPaidEventHandler extends AbstractEventVerticle<OrderPaidEvent> {


    private final MerchantProductRootService merchantProductRootService;
    private final ScoreRootQueryRepository scoreRootQueryRepository;
    private final ScoreRootService scoreRootService;

    @Override
    public Mono<Void> handle(Mono<OrderPaidEvent> mono) {
        return mono.flatMap(event -> {
            if (!OrderTypeEnum.MERCHANT_PRODUCT.equals(event.getOrderType())) {
                return Mono.empty();
            }
            Mono<MerchantProductRoot> getRoot = Mono.justOrEmpty(event.getGoodsIdList().getFirst())
                    .flatMap(goodsId -> merchantProductRootService
                            .getRoot(MerchantProductIdentifierConvertor.map(goodsId)));

            Function<MerchantProductRoot, Mono<Void>> updateScore = root -> {
                OrderValObj orderValObj = new OrderValObj();
                GoodsIdentifier goodsIdentifier = root.getIdentifier().getGoodsIdentifier();
                orderValObj.setIdentifier(goodsIdentifier);
                orderValObj.setOrderNo(event.getOrderNo());
                orderValObj.setCount(event.getOrderTotalBuyCount());
                orderValObj.setAmount(event.getTotalAmount());
                orderValObj.setUserId(event.getUserId());
                BuyerCountValObj buyerCountValObj = new BuyerCountValObj();
                buyerCountValObj.setIdentifier(goodsIdentifier);
                buyerCountValObj.setUserId(event.getUserId());
                return scoreRootQueryRepository.findGoodsScore(goodsIdentifier)
                        .defaultIfEmpty(new ScoreEntity())
                        .flatMap(entity -> scoreRootService.reCalculate(ScoreIdentifier.builder()
                                .goodsIdentifier(goodsIdentifier)
                                .ruleVersion(entity.getRuleVersion())
                                .productType(root.getMerchantProductEntity().getProductType())
                                .build()));
            };
            return getRoot.flatMap(updateScore);
        });
    }
}
