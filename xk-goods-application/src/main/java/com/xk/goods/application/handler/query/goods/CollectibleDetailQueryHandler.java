package com.xk.goods.application.handler.query.goods;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.goods.application.action.query.business.BusinessResIdentifierQuery;
import com.xk.goods.application.action.query.goods.CollectibleDetailQuery;
import com.xk.goods.domain.model.collectible.CollectibleCardRoot;
import com.xk.goods.domain.model.collectible.entity.CollectibleCardEntity;
import com.xk.goods.domain.model.collectible.id.CollectibleCardIdentifier;
import com.xk.goods.domain.model.goods.GoodsRoot;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.price.entity.PriceItemEntity;
import com.xk.goods.domain.model.price.id.PriceIdentifier;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.service.collectible.CollectibleCardRootService;
import com.xk.goods.domain.service.goods.GoodsRootService;
import com.xk.goods.domain.service.price.PriceRootService;
import com.xk.goods.domain.service.specification.SpecificationRootService;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;
import com.xk.goods.interfaces.dto.res.goods.CollectibleDetailResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class CollectibleDetailQueryHandler
        implements IActionQueryHandler<CollectibleDetailQuery, CollectibleDetailResDto> {

    private final GoodsRootService goodsRootService;
    private final SpecificationRootService specificationRootService;
    private final PriceRootService priceRootService;
    private final CollectibleCardRootService collectibleCardRootService;
    private final Converter converter;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public Mono<CollectibleDetailResDto> execute(Mono<CollectibleDetailQuery> query) {
        return query.flatMap(dto -> {
            // 1. 转换商品ID
            GoodsIdentifier goodsId = GoodsIdentifierConvertor.map(dto.getGoodsId());

            // 2. 异步并行获取商品Root和规格列表（减少串行等待）
            Mono<GoodsRoot> goodsRootMono = goodsRootService.getRoot(goodsId).cache();
            Mono<List<SpecificationRoot>> specsMono =
                    specificationRootService.searchByGoodsId(goodsId);
            Mono<CollectibleCardRoot> cardRootMono = collectibleCardRootService
                    .getRoot(CollectibleCardIdentifier.builder().goodsIdentifier(goodsId).build());

            return Mono.zip(goodsRootMono, specsMono, cardRootMono).flatMap(tuple -> {
                // 3. 转换基础DTO
                CollectibleDetailResDto result =
                        converter.convert(tuple.getT1(), CollectibleDetailResDto.class);

                Mono<Void> setPicture = Flux.fromIterable(tuple.getT1().getResIdentifierList())
                        .flatMap(res -> this.queryDispatcher.executeQuery(Mono.just(res),
                                BusinessResIdentifierQuery.class, BusinessResDto.class))
                        .collectList().doOnNext(result::setProductPicList).then();

                // 4. 获取首个规格的价格（如果存在）
                Mono<Void> setAmount = Mono.justOrEmpty(tuple.getT2())
                        .filter(list -> !list.isEmpty()).flatMap(list -> {
                            PriceIdentifier priceId =
                                    list.getFirst().getSpecificationEntity().getPriceIdentifier();
                            return priceRootService.getRoot(priceId).doOnNext(priceRoot -> {
                                PriceItemEntity first =
                                        priceRoot.getPriceItemEntityList().getFirst();
                                if (first != null) {
                                    result.setAmount(first.getAmount());
                                    result.setCurrencyType(first.getCurrencyType().getCode());
                                }
                            }).then();
                        });

                Mono<Void> setCollectible = Mono.justOrEmpty(tuple.getT3()).doOnNext(root -> {
                    CollectibleCardEntity entity = root.getCollectibleCardEntity();
                    if (entity.getBlockType() != null) {
                        result.setBlockType(entity.getBlockType().getCode());
                    }
                    result.setSeriesName(entity.getSeriesName());
                }).then();
                // 4. 获取首个规格的价格（如果存在）
                return setPicture.then(setAmount).then(setCollectible).thenReturn(result);
            });
        });
    }
}
