package com.xk.goods.application.handler.command.gift;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.gift.DeleteGiftReportCommand;
import com.xk.goods.domain.model.gift.GiftReportRoot;
import com.xk.goods.domain.model.gift.entity.GiftReportEntity;
import com.xk.goods.domain.service.gift.GiftReportRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteGiftReportHandler
        implements IActionCommandHandler<DeleteGiftReportCommand, Void> {

    private final GiftReportRootService giftReportRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<DeleteGiftReportCommand> mono) {
        return execute(mono, GiftReportEntity.class, this.converter::convert,
                entity -> GiftReportRoot.builder().identifier(entity.getIdentifier())
                        .giftReportEntity(entity).build(),
                v -> giftReportRootService.removeRoot(Mono.just(v))).then();
    }
}
