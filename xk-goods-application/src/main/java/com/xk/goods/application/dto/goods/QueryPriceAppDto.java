package com.xk.goods.application.dto.goods;

import com.xk.goods.domain.model.activity.valobj.GoodsActivityPayValObj;
import com.xk.goods.domain.model.activity.valobj.GoodsActivityShowValObj;
import com.xk.goods.interfaces.dto.res.goods.PayPriceResDto;
import com.xk.goods.interfaces.dto.res.goods.ShowPriceResDto;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.infrastructure.convertor.common.LongIdentifierConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = GoodsActivityPayValObj.class, convertGenerate = false,
                uses = {CommonStatusEnumConvertor.class, LongIdentifierConvertor.class}),
        @AutoMapper(target = GoodsActivityShowValObj.class, convertGenerate = false,
                uses = {CommonStatusEnumConvertor.class, LongIdentifierConvertor.class}),
        @AutoMapper(target = PayPriceResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = ShowPriceResDto.class, reverseConvertGenerate = false)})
public class QueryPriceAppDto {

    private Long userId;

    /**
     * 总金额
     */
    private Long totalAmount;

    /**
     * 满减状态
     */
    private Integer discountStatus;

    /**
     * 满减金额
     */
    private Long discountAmount;

    /**
     * 首购优惠状态
     */
    private Integer firstBuyDiscountStatus;

    /**
     * 首购优惠金额
     */
    private Long firstBuyDiscountAmount;

    /**
     * 优惠券状态
     */
    private Integer couponStatus;

    /**
     * 优惠券金额
     */
    private Long couponAmount;

    /**
     * 实付金额
     */
    private Long payAmount;

    /**
     * 剩余随机状态
     */
    private Integer remainRandomStatus;

    /**
     * 剩余随机金额
     */
    private Long remainRandomAmount;

    /**
     * 展示金额
     */
    private Long showAmount;
}
