package com.xk.goods.application.handler.query.gift;

import java.util.Map;
import java.util.Objects;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.domain.model.res.SysResourceEntity;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;
import com.xk.goods.application.action.query.gift.GiftReportQuery;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.repository.business.BusinessResRootQueryRepository;
import com.xk.goods.domain.repository.gift.GiftReportRootQueryRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.interfaces.dto.res.gift.GiftSearchPagerRep;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class GiftReportQueryHandler implements IActionQueryHandler<GiftReportQuery, Pagination> {

    private final GiftReportRootQueryRepository giftReportRootQueryRepository;
    private final BusinessResRootQueryRepository businessResRootQueryRepository;
    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<GiftReportQuery> mono) {

        return mono.filter(Objects::nonNull).map(query -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(query.getLimit());
            pagination.setOffset(query.getOffset());
            Map<String, Object> criteria = CollectionHelper.converBeanToMap(query);
            pagination.setCriteria(criteria);
            return pagination;
        }).flatMap(pagination -> giftReportRootQueryRepository.searchByPage(pagination)
                .switchIfEmpty(Mono.empty()).map(entity -> {
                    GiftSearchPagerRep dto = new GiftSearchPagerRep();
                    BeanUtils.copyProperties(entity, dto);
                    if (entity.getWinnerStatus() != null) {
                        dto.setWinnerStatus(entity.getWinnerStatus().getCode());
                    }
                    dto.setCreateTime(entity.getCreateValObj().getCreateTime());
                    return dto;
                }).switchIfEmpty(Mono.empty())
                .flatMap(dto -> businessResRootQueryRepository
                        .searchByBusinessGroup(BusinessResEntity.builder()
                                .businessGroupType(BusinessGroupTypeEnum.GIFT)
                                .businessId(dto.getGiftReportId()).build())
                        .flatMap(res -> sysResourceRootQueryRepository
                                .findById(IntegerIdentifier.builder().id(res.getResId()).build()))
                        .collectList()
                        .doOnNext(resList -> dto.setResAddrList(
                                resList.stream().map(SysResourceEntity::getAddr).toList()))
                        .thenReturn(dto))
                .collectList().doOnNext(pagination::setRecords).thenReturn(pagination))
                .switchIfEmpty(Mono.empty());
    }
}
