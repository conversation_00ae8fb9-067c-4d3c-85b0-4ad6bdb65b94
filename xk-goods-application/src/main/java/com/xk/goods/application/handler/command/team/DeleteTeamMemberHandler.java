package com.xk.goods.application.handler.command.team;

import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.goods.application.action.command.team.DeleteTeamMemberCommand;
import com.xk.goods.application.commons.XkGoodsApplicationErrorEnum;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.team.DeleteTeamMemberEvent;
import com.xk.goods.domain.model.team.TeamMemberRoot;
import com.xk.goods.domain.model.team.entity.TeamMemberEntity;
import com.xk.goods.domain.repository.team.TeamMemberRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteTeamMemberHandler
        implements IActionCommandHandler<DeleteTeamMemberCommand, Void> {

    private final Converter converter;
    private final TeamMemberRootRepository memberRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<DeleteTeamMemberCommand> command) {
        return command.flatMap(cmd -> {
            // 命令执行函数
            Function<DeleteTeamMemberCommand, Mono<Void>> executeFunc = cmdParam -> execute(
                    Mono.just(cmdParam), TeamMemberEntity.class, this.converter::convert,
                    entity -> TeamMemberRoot.builder()
                            .identifier(
                                    LongIdentifier.builder().id(entity.getTeamMemberId()).build())
                            .teamMemberEntity(entity).build(),
                    memberRootRepository::remove).then();

            // 事件发布函数
            Function<DeleteTeamMemberCommand, Mono<Void>> publishFunc = cmdParam -> Mono
                    .just(cmdParam.getTeamMemberId())
                    .map(businessId -> EventRoot.builder()
                            .domainEvent(DeleteTeamMemberEvent.builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            DeleteTeamMemberEvent.class))
                                    .teamMemberId(businessId).build())
                            .build())
                    .flatMap(event -> Mono.fromCallable(() -> {
                        try {
                            return eventRootService.publish(event);
                        } catch (ExceptionWrapperThrowable e) {
                            throw new RuntimeException(e);
                        }
                    }).onErrorResume(e -> {
                        log.warn("DeleteTeamMemberEvent事件发布失败: {}", cmdParam, e);
                        return Mono.just(false);
                    })).then().doOnSuccess(v -> log.info("DeleteTeamMemberEvent事件发布完成: {}",
                            cmdParam.getTeamMemberId()));

            return executeFunc.apply(cmd).then(publishFunc.apply(cmd));
        }).onErrorResume(e -> {
            log.error("DeleteTeamMemberCommand命令执行异常", e);
            return Mono.error(
                    new XkGoodsApplicationException(XkGoodsApplicationErrorEnum.APPLICATION_ERROR));
        });
    }
}
