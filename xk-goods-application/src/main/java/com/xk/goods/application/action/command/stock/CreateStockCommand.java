package com.xk.goods.application.action.command.stock;

import java.util.Date;
import java.util.Objects;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.domain.model.stock.StockRoot;
import com.xk.domain.model.stock.entity.*;
import com.xk.domain.model.stock.id.StockIdentifier;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.infrastructure.convertor.common.StringIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.specification.SpecificationIdentifierConvertor;
import com.xk.goods.interfaces.dto.req.stock.StockReqDto;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.infrastructure.convertor.stock.StockBusinessTypeEnumConvertor;

import io.github.linpeilie.Converter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;
import reactor.core.publisher.Mono;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = StockReqDto.class, convertGenerate = false,
                uses = {StringIdentifierConvertor.class}),
        @AutoMapper(target = StockEntity.class,
                uses = {CommonStatusEnumConvertor.class, SpecificationIdentifierConvertor.class,
                        GoodsIdentifierConvertor.class, StockBusinessTypeEnumConvertor.class},
                reverseConvertGenerate = false)})
public class CreateStockCommand extends AbstractActionCommand {

    public static final Long MAX_STOCK = 999999999L;

    /**
     * 库存id
     */
    private Long stockId;

    /**
     * 库存业务类型
     */
    private Integer stockBusinessType;

    /**
     * 业务标识符
     */
    private StringIdentifier businessIdentifier;

    /**
     * 总真实库存
     */
    private Long totalRealStock;

    /**
     * 总虚拟库存
     */
    private Long totalVirtualStock;

    /**
     * 创建人
     */
    @AutoMappings({@AutoMapping(targetClass = StockEntity.class, target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = StockEntity.class, target = "createValObj.createTime")})
    private Date createTime;

    public void buildStock() {
        this.totalRealStock = MAX_STOCK;
        this.totalVirtualStock = MAX_STOCK;
    }

    public void buildStock(Long stockAmount) {
        this.totalRealStock = stockAmount;
        this.totalVirtualStock = stockAmount;
    }

    public void buildGoods(Long goodsId, Long userId) {
        this.businessIdentifier = StringIdentifier.builder().id(String.valueOf(goodsId)).build();
        this.stockBusinessType = StockBusinessTypeEnum.GOODS.getCode();
        this.createId = userId;
        this.createTime = new Date();
    }

    public Long getGoodsId() {
        return Long.valueOf(businessIdentifier.getIdentifier().id());
    }

    public StockGoodsEntity getStockGoodsEntity() {
        return StockGoodsEntity.builder().stockId(this.getStockId())
                .remainRealStock(this.getTotalRealStock())
                .remainVirtualStock(this.getTotalVirtualStock())
                .paidRealStock(this.getTotalRealStock())
                .paidVirtualStock(this.getTotalVirtualStock()).goodsId(this.getGoodsId())
                .deleted(CommonStatusEnum.DISABLE).build();
    }

    public void buildSpecification(Long specificationId, Long userId) {
        this.businessIdentifier =
                StringIdentifier.builder().id(String.valueOf(specificationId)).build();
        this.stockBusinessType = StockBusinessTypeEnum.SPECIFICATION.getCode();
        this.createId = userId;
        this.createTime = new Date();
    }

    public Long getSpecificationId() {
        return Long.valueOf(businessIdentifier.getIdentifier().id());
    }

    public StockSpecificationEntity getStockSpecificationEntity() {
        return StockSpecificationEntity.builder().stockId(this.getStockId())
                .remainRealStock(this.getTotalRealStock())
                .remainVirtualStock(this.getTotalVirtualStock())
                .paidRealStock(this.getTotalRealStock())
                .paidVirtualStock(this.getTotalVirtualStock())
                .specificationId(this.getSpecificationId()).deleted(CommonStatusEnum.DISABLE)
                .build();
    }

    public Long getCorpId() {
        return Long.valueOf(businessIdentifier.getIdentifier().id());
    }

    public StockCorpEntity getStockCorpEntity() {
        return StockCorpEntity.builder().stockId(this.getStockId())
                .remainRealStock(this.getTotalRealStock())
                .remainVirtualStock(this.getTotalVirtualStock()).corpId(this.getCorpId())
                .deleted(CommonStatusEnum.DISABLE).build();
    }

    public void buildCorp(Long corpId, Long userId) {
        this.businessIdentifier = StringIdentifier.builder().id(String.valueOf(corpId)).build();
        this.stockBusinessType = StockBusinessTypeEnum.CORP_FREE_QUOTA.getCode();
        this.createId = userId;
        this.createTime = new Date();
    }

    public Long getCouponId() {
        return Long.valueOf(businessIdentifier.getIdentifier().id());
    }

    public StockCouponEntity getStockCouponEntity() {
        return StockCouponEntity.builder().stockId(this.getStockId())
                .remainRealStock(this.getTotalRealStock())
                .remainVirtualStock(this.getTotalVirtualStock()).couponId(this.getCouponId())
                .deleted(CommonStatusEnum.DISABLE.getCode()).build();
    }


    public Mono<StockRoot> getRoot(Converter converter) {
        StockEntity stockEntity = converter.convert(this, StockEntity.class);
        StockRoot.StockRootBuilder builder = StockRoot.builder()
                .identifier(StockIdentifier.builder().stockId(this.getStockId()).build())
                .stockEntity(stockEntity);
        if (Objects.requireNonNull(
                stockEntity.getStockBusinessType()) == StockBusinessTypeEnum.SPECIFICATION) {
            builder.stockSpecificationEntity(this.getStockSpecificationEntity());
        } else if (stockEntity.getStockBusinessType() == StockBusinessTypeEnum.GOODS) {
            builder.stockGoodsEntity(this.getStockGoodsEntity());
        } else if (stockEntity.getStockBusinessType() == StockBusinessTypeEnum.CORP_FREE_QUOTA) {
            builder.stockCorpEntity(this.getStockCorpEntity());
        } else if (stockEntity.getStockBusinessType() == StockBusinessTypeEnum.COUPON) {
            builder.stockCouponEntity(this.getStockCouponEntity());
        }
        return Mono.just(builder.build());
    }
}
