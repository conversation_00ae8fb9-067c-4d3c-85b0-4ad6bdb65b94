package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.serial.entity.SerialGroupEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupUpdateOriginalReqDto;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupUpdateSpecialReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialGroupUpdateOriginalReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialGroupUpdateSpecialReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialGroupEntity.class, reverseConvertGenerate = false)})
public class UpdateSerialGroupCommand extends AbstractActionCommand {

    private Long userId;

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 卡密组名称
     */
    private String name;

    /**
     * 卡密组类目ID
     */
    private Long serialGroupCategoryId;

    /**
     * 卡密组类型：1球队卡密组；2自定义卡密组
     */
    private Integer groupType;

    /**
     * 板块类型：1球队；2动漫
     */
    private Integer blockType;
}
