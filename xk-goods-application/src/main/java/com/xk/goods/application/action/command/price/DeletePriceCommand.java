package com.xk.goods.application.action.command.price;

import com.myco.mydata.application.handler.command.AbstractActionCommand;

import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class DeletePriceCommand extends AbstractActionCommand {

    /**
     * 价格id
     */
    private Long priceId;
}
