package com.xk.goods.application.action.command.collectible;

import static com.xk.application.commons.CommonUtil.setIfNotNull;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.common.UpdateValObj;
import com.xk.goods.domain.model.collectible.CollectibleCardRoot;
import com.xk.goods.domain.model.collectible.entity.CollectibleCardEntity;
import com.xk.goods.infrastructure.convertor.common.BlockTypeEnumConvertor;
import com.xk.goods.interfaces.dto.req.goods.collectible.CollectibleCardReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CollectibleCardReqDto.class, convertGenerate = false),
        @AutoMapper(target = CollectibleCardEntity.class, uses = {BlockTypeEnumConvertor.class},
                reverseConvertGenerate = false)})
public class UpdateCollectibleCardCommand extends AbstractActionCommand {

    /**
     * 商品主键
     */
    private Long goodsId;

    /**
     * 版块
     */
    private Integer blockType;

    /**
     * 所属系列
     */
    private String seriesName;

    /**
     * 创建人ID
     */
    @AutoMappings({@AutoMapping(targetClass = CollectibleCardEntity.class,
            target = "updateValObj.updateId")})
    private Long updateId;

    /**
     * 创建时间
     */
    @AutoMappings({@AutoMapping(targetClass = CollectibleCardEntity.class,
            target = "updateValObj.updateTime")})
    private Date updateTime;

    public CollectibleCardRoot getRoot(CollectibleCardRoot root) {
        CollectibleCardEntity oldEntity = root.getCollectibleCardEntity();

        UpdateValObj updateValObj = UpdateValObj.builder()
                .updateTime(setIfNotNull(updateTime, oldEntity.getUpdateValObj().getUpdateTime()))
                .updateId(setIfNotNull(updateId, oldEntity.getUpdateValObj().getUpdateId()))
                .build();
        CollectibleCardEntity newEntity = CollectibleCardEntity.builder()
                .goodsId(oldEntity.getGoodsId())
                .blockType(setIfNotNull(BlockTypeEnumConvertor.map(blockType),
                        oldEntity.getBlockType()))
                .seriesName(setIfNotNull(seriesName, oldEntity.getSeriesName()))
                .status(oldEntity.getStatus()).deleted(oldEntity.getDeleted())
                .createValObj(oldEntity.getCreateValObj()).updateValObj(updateValObj).build();

        return CollectibleCardRoot.builder().identifier(root.getIdentifier())
                .collectibleCardEntity(newEntity).build();
    }
}
