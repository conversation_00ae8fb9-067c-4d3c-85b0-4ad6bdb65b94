package com.xk.goods.application.action.query.serial;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.interfaces.dto.req.serial.SerialTemplateReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialTemplateReqDto.class, convertGenerate = false)})
public class SerialTemplateQuery extends PagerQuery implements IActionQuery {
    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 卡密组名称
     */
    private String name;

}
