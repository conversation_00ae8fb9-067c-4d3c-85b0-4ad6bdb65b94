package com.xk.goods.application.handler.query.serial;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.config.interfaces.dto.req.res.SysResourceIdReqDto;
import com.xk.config.interfaces.dto.rsp.res.SysResourceRspDto;
import com.xk.config.interfaces.query.res.SysResourceQueryService;
import com.xk.goods.application.action.query.serial.SerialItemSpecialQuery;
import com.xk.goods.domain.model.serial.entity.SerialSpecialItemEntity;
import com.xk.goods.domain.repository.serial.SerialItemRootQueryRepository;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;
import com.xk.goods.interfaces.dto.res.serial.SerialSpecialItemResDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SerialItemSpecialQueryHandler implements IActionQueryHandler<SerialItemSpecialQuery, Pagination> {

    private final SerialItemRootQueryRepository serialItemRootQueryRepository;

    private final SysResourceQueryService sysResourceQueryService;

    @Override
    public Mono<Pagination> execute(Mono<SerialItemSpecialQuery> queryMono) {
        // 定义转换函数：将实体转换为响应DTO（包含资源信息）
        Function<SerialSpecialItemEntity, Mono<SerialSpecialItemResDto>> entityToDto = entity -> {

            // 1. 查询普通图片资源
            Mono<BusinessResDto> ordinaryPicMono = sysResourceQueryService.getById(
                            Mono.just(new SysResourceIdReqDto(Integer.valueOf(entity.getOrdinaryPic()))))
                    .switchIfEmpty(Mono.just(new SysResourceRspDto()))
                    .map(ordinaryPicRsp -> {
                        BusinessResDto dto = new BusinessResDto();
                        dto.setAddr(ordinaryPicRsp.getAddr());
                        dto.setResId(ordinaryPicRsp.getResId());
                        dto.setBusinessResType(BusinessResTypeEnum.SERIAL_PICTURE.getCode());
                        return dto;
                    });

            // 2. 查询特殊图片资源
            Mono<BusinessResDto> specialPicMono = sysResourceQueryService.getById(
                            Mono.just(new SysResourceIdReqDto(Integer.valueOf(entity.getSpecialPic()))))
                    .switchIfEmpty(Mono.just(new SysResourceRspDto()))
                    .map(specialPicRsp -> {
                        BusinessResDto dto = new BusinessResDto();
                        dto.setAddr(specialPicRsp.getAddr());
                        dto.setResId(specialPicRsp.getResId());
                        dto.setBusinessResType(BusinessResTypeEnum.SERIAL_PICTURE.getCode());
                        return dto;
                    });

            // 3. 组合两个资源查询结果
            return Mono.zip(ordinaryPicMono, specialPicMono)
                    .map(tuple -> {
                        SerialSpecialItemResDto resDto = new SerialSpecialItemResDto();
                        // 复制基本属性
                        BeanUtils.copyProperties(entity, resDto);

                        // 添加资源信息
                        List<BusinessResDto> ordinaryPicInfos = new ArrayList<>();
                        List<BusinessResDto> specialPicInfos = new ArrayList<>();
                        ordinaryPicInfos.add(tuple.getT1());
                        specialPicInfos.add(tuple.getT2());
                        resDto.setOrdinaryPicInfos(ordinaryPicInfos);
                        resDto.setSpecialPicInfos(specialPicInfos);

                        return resDto;
                    });
        };

        // 执行分页查询并转换结果
        return execute(queryMono, query -> {
            Pagination pagination = new Pagination();
            pagination.setOffset(query.getOffset());
            pagination.setLimit(query.getLimit());
            pagination.setCriteria(CollectionHelper.converBeanToMap(query));
            return pagination;
        }, serialItemRootQueryRepository::searchSpecialItem, SerialSpecialItemEntity.class)
                .flatMap(pagination -> {
                    List<SerialSpecialItemEntity> entities = pagination.getRecords();
                    return Flux.fromIterable(entities)
                            .flatMap(entityToDto) // 异步转换每个实体
                            .collectList()        // 收集转换后的DTO列表
                            .map(dtoList -> {
                                pagination.setRecords(dtoList);
                                return pagination;
                            });
                });
    }
}
