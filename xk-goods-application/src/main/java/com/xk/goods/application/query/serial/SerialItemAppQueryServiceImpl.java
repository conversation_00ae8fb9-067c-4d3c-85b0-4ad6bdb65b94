package com.xk.goods.application.query.serial;

import java.util.List;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdRequireReqDto;
import com.xk.goods.interfaces.dto.res.serial.SerialOriginalTeamAppDto;
import com.xk.goods.interfaces.query.serial.SerialItemAppQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class SerialItemAppQueryServiceImpl implements SerialItemAppQueryService {

    @BusiCode
    @Override
    public Mono<List<SerialOriginalTeamAppDto>> searchOriginalTeam(
            Mono<GoodsIdRequireReqDto> mono) {
        return null;
    }
}
