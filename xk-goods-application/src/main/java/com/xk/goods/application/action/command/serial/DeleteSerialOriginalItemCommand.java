package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.serial.entity.SerialSpecialItemEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupUpdateOriginalReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialGroupUpdateOriginalReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialSpecialItemEntity.class, reverseConvertGenerate = false)})
public class DeleteSerialOriginalItemCommand extends AbstractActionCommand {

    private Long userId;

    /**
     * 卡密组id
     */
    private Long serialGroupId;
}
