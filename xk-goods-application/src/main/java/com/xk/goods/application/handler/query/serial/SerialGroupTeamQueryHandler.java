package com.xk.goods.application.handler.query.serial;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.goods.application.action.query.serial.SerialGroupQuery;
import com.xk.goods.application.action.query.serial.SerialGroupTeamQuery;
import com.xk.goods.domain.model.serial.entity.SerialGroupEntity;
import com.xk.goods.domain.model.serial.entity.SerialGroupTeamEntity;
import com.xk.goods.domain.repository.serial.SerialGroupRootQueryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SerialGroupTeamQueryHandler implements IActionQueryHandler<SerialGroupTeamQuery, Pagination> {

    private final SerialGroupRootQueryRepository serialGroupRootQueryRepository;


    @Override
    public Mono<Pagination> execute(Mono<SerialGroupTeamQuery> queryMono) {
        return execute(queryMono, serialGroupTeamQuery -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(serialGroupTeamQuery.getLimit());
            pagination.setOffset(serialGroupTeamQuery.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(serialGroupTeamQuery));
            return pagination;
        }, serialGroupRootQueryRepository::searchSerialGroupTeam, SerialGroupTeamEntity.class);
    }
}
