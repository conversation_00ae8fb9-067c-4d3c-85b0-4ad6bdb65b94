package com.xk.goods.application.handler.event.merchant;

import static com.xk.goods.application.commons.XkGoodsApplicationErrorEnum.APPLICATION_ERROR;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.object.goods.CardGoodsValueObject;
import com.myco.mydata.domain.model.object.goods.GoodsObjectRoot;
import com.myco.mydata.domain.model.object.goods.SpecificationValueObject;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.application.commons.CommonUtil;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.application.action.command.activity.CreateGoodsActivityConfigCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.merchant.MerchantProductFirstListingEvent;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.random.RandomDistributionRoot;
import com.xk.goods.domain.model.score.id.ScoreIdentifier;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.repository.serial.SerialItemRootQueryRepository;
import com.xk.goods.domain.service.goods.GoodsRootService;
import com.xk.goods.domain.service.merchant.MerchantProductRootService;
import com.xk.goods.domain.service.random.RandomDistributionDomainService;
import com.xk.goods.domain.service.score.ScoreRootService;
import com.xk.goods.domain.service.specification.SpecificationRootService;
import com.xk.goods.enums.activity.ActivityTypeEnum;
import com.xk.goods.enums.merchant.ProductRandomTypeEnum;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.merchant.MerchantProductIdentifierConvertor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class MerchantProductFirstListingEventHandler
        extends AbstractEventVerticle<MerchantProductFirstListingEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final ScoreRootService scoreRootService;
    private final GoodsRootService goodsRootService;
    private final MerchantProductRootService merchantProductRootService;
    private final SpecificationRootService specificationRootService;
    private final StockRootService stockRootService;
    private final SelectorRootService selectorRootService;
    private final RandomDistributionDomainService randomDistributionDomainService;
    private final SerialItemRootQueryRepository serialItemRootQueryRepository;

    @Override
    public Mono<Void> handle(Mono<MerchantProductFirstListingEvent> mono) {
        return mono.flatMap(event -> specificationRootService
                .searchByGoodsId(GoodsIdentifierConvertor.map(event.getGoodsId()))
                .flatMap(specRoot -> selectorRootService.getGoodsObject(event.getGoodsId())
                        .flatMap(goodsObject -> Mono.when(processCreateGoodsActivity(goodsObject),
                                processRecalcScore(goodsObject),
                                processMerchantProductDownCache(event),
                                processStockCache(goodsObject.getSpecificationList()),
                                processCreateRandomDistribution(specRoot, goodsObject))))
                .doOnSuccess(v -> log.info("MerchantProductFirstListingEvent处理完成,goodsId:{}",
                        event.getGoodsId()))
                .onErrorResume(this::handleError));
    }

    /**
     * 处理创建商品活动事件
     *
     * @return Mono<Void>
     */
    private Mono<Void> processCreateGoodsActivity(GoodsObjectRoot root) {
        Date date = new Date();
        return Flux.fromIterable(Arrays.asList(ActivityTypeEnum.values())).flatMap(activityType -> {
            CreateGoodsActivityConfigCommand command = new CreateGoodsActivityConfigCommand();
            command.setGoodsId(root.getGoodsInfo().getGoodsId());
            command.setActivityType(activityType.getCode());
            command.buildCreate(-1L, date);
            CardGoodsValueObject cardGoods = root.getGoodsInfo().getCardGoods();
            switch (activityType) {
                case DISCOUNT -> command.setStatus(cardGoods.getDiscountStatus());
                case FIRST_BUY -> command.setStatus(cardGoods.getFirstBuyDiscountStatus());
                case REMAIN_RANDOM -> command.setStatus(CommonStatusEnum.DISABLE.getCode());
                default -> command.setStatus(CommonStatusEnum.ENABLE.getCode());
            }
            return commandDispatcher.executeCommand(Mono.just(command),
                    CreateGoodsActivityConfigCommand.class);
        }).collectList().then();
    }

    /**
     * 处理创建商品分数
     *
     * @return Mono<Void>
     */
    private Mono<Void> processRecalcScore(GoodsObjectRoot root) {
        Long goodsId = root.getGoodsInfo().getGoodsId();
        return merchantProductRootService.getEntity(MerchantProductIdentifierConvertor.map(goodsId))
                .flatMap(entity -> scoreRootService.reCalculate(ScoreIdentifier.builder()
                        .goodsIdentifier(GoodsIdentifier.builder().goodsId(goodsId).build())
                        .productType(entity.getProductType()).ruleVersion(1L).build()));
    }

    /**
     * 处理商家下架时间缓存
     *
     * @param event 创建商品活动事件
     * @return Mono<Void>
     */
    private Mono<Void> processMerchantProductDownCache(MerchantProductFirstListingEvent event) {
        return goodsRootService.getGoodsObject(GoodsIdentifierConvertor.map(event.getGoodsId()))
                .flatMap(goodsRootService::addGoodsDownCache);
    }


    /**
     * 处理商品库存缓存
     *
     * @param specRoot 规格
     * @return Mono<Void>
     */
    private Mono<Void> processStockCache(List<SpecificationValueObject> specRoot) {
        return Flux.fromIterable(specRoot).map(SpecificationValueObject::getSpecificationId)
                .flatMap(id -> stockRootService.deductionStock(
                        StringIdentifier.builder().id(id.toString()).build(), 0,
                        StockBusinessTypeEnum.SPECIFICATION))
                .then();
    }

    /**
     * 处理创建随机分发
     *
     * @param specRoot 规格
     * @return Mono<Void>
     */
    private Mono<Void> processCreateRandomDistribution(List<SpecificationRoot> specRoot,
            GoodsObjectRoot goodsObject) {
        ProductTypeEnum productType = ProductTypeEnum
                .valueOf(goodsObject.getGoodsInfo().getCardGoods().getCardGoodsType());

        if (ProductTypeEnum.FORTUNE_BOX.equals(productType)) {
            return processFortuneRandom(specRoot, goodsObject);
        }
        ProductRandomTypeEnum randomType = ProductRandomTypeEnum
                .getByCode(goodsObject.getGoodsInfo().getCardGoods().getRandomType());

        if (CommonUtil.equalsAny(randomType, ProductRandomTypeEnum.TEAM_RANDOM_PLAYER,
                ProductRandomTypeEnum.TEAM_RANDOM_CARD)) {
            return processTeamRandom(specRoot, goodsObject);
        }
        return processNoneTeamRandom(specRoot, goodsObject);
    }

    /**
     * 处理福盒随机分发
     *
     * @param specRoot specRoot
     * @param goodsObject goodsObject
     * @return Mono<Void>
     */
    private Mono<Void> processFortuneRandom(List<SpecificationRoot> specRoot,
            GoodsObjectRoot goodsObject) {
        return RandomDistributionRoot.createFortuneRoot(specRoot, goodsObject)
                .flatMap(v -> randomDistributionDomainService.createRandomDistribute(Mono.just(v)))
                .flatMapMany(v -> Flux.fromIterable(specRoot))
                .flatMap(v -> specificationRootService.updateRoot(Mono.just(v))).then();
    }

    /**
     * 处理选队随机分发
     *
     * @param specRoot specRoot
     * @param goodsObject goodsObject
     * @return Mono<Void>
     */
    private Mono<Void> processTeamRandom(List<SpecificationRoot> specRoot,
            GoodsObjectRoot goodsObject) {
        SerialGroupIdentifier identifier = SerialGroupIdentifier.builder()
                .serialGroupId(
                        specRoot.getFirst().getSpecificationGiftValObj().getBusinessGroupId())
                .build();
        return serialItemRootQueryRepository.searchOriginalBySerialGroupId(identifier).collectList()
                .defaultIfEmpty(new ArrayList<>())
                .flatMapMany(
                        list -> RandomDistributionRoot.createTeamRoot(specRoot, goodsObject, list))
                .flatMap(v -> randomDistributionDomainService.createRandomDistribute(Mono.just(v)))
                .collectList().flatMapMany(v -> Flux.fromIterable(specRoot))
                .flatMap(v -> specificationRootService.updateRoot(Mono.just(v))).then();
    }

    /**
     * 处理非选队随机分发
     *
     * @param specRoot specRoot
     * @param goodsObject goodsObject
     * @return Mono<Void>
     */
    private Mono<Void> processNoneTeamRandom(List<SpecificationRoot> specRoot,
            GoodsObjectRoot goodsObject) {
        SerialGroupIdentifier identifier = SerialGroupIdentifier.builder()
                .serialGroupId(
                        specRoot.getFirst().getSpecificationGiftValObj().getBusinessGroupId())
                .build();
        return Mono
                .zip(serialItemRootQueryRepository.searchOriginalBySerialGroupId(identifier)
                        .collectList().defaultIfEmpty(new ArrayList<>()),
                        serialItemRootQueryRepository.searchSpecialBySerialGroupId(identifier)
                                .collectList().defaultIfEmpty(new ArrayList<>()))
                .flatMapMany(tuple -> RandomDistributionRoot.createNoneTeamRoot(specRoot,
                        goodsObject, tuple.getT1(), tuple.getT2()))
                .flatMap(v -> randomDistributionDomainService.createRandomDistribute(Mono.just(v)))
                .collectList().flatMapMany(distributionId -> Flux.fromIterable(specRoot))
                .flatMap(root -> specificationRootService.updateRoot(Mono.just(root))).then();
    }

    /**
     * 统一错误处理
     *
     * @param throwable 异常
     * @return Mono<Void>
     */
    private Mono<Void> handleError(Throwable throwable) {
        log.error("处理MerchantProductFirstListingEvent事件失败", throwable);
        return Mono.error(new XkGoodsApplicationException(APPLICATION_ERROR));
    }
}
