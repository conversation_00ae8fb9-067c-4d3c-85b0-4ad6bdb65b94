package com.xk.goods.application.action.query.serial;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.interfaces.dto.req.serial.SerialItemReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@AutoMappers({@AutoMapper(target = SerialItemReqDto.class, convertGenerate = false)})
public class SerialItemQuery implements IActionQuery {

    /**
     * 卡密组类型：1球队卡密组；2自定义卡密组
     */
    private Integer groupType;

    /**
     * 卡密组id
     */
    private Long serialGroupId;

}
