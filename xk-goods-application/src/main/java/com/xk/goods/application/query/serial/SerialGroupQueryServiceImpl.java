package com.xk.goods.application.query.serial;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.application.action.query.serial.SerialGroupOriginalDetailQuery;
import com.xk.goods.application.action.query.serial.SerialGroupQuery;
import com.xk.goods.application.action.query.serial.SerialGroupSpecialDetailQuery;
import com.xk.goods.application.action.query.serial.SerialGroupTeamQuery;
import com.xk.goods.domain.model.serial.entity.SerialGroupEntity;
import com.xk.goods.interfaces.dto.req.serial.*;
import com.xk.goods.interfaces.dto.res.serial.SerialGroupOriginalDetailResDto;
import com.xk.goods.interfaces.dto.res.serial.SerialGroupSpecialDetailResDto;
import com.xk.goods.interfaces.query.serial.SerialGroupQueryService;
import com.xk.goods.interfaces.query.serial.SerialItemQueryService;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SerialGroupQueryServiceImpl implements SerialGroupQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    private final Converter converter;

    private final SerialItemQueryService serialItemQueryService;

    @Override
    public Mono<Pagination> search(Mono<SerialGroupReqDto> mono) {
        return mono.flatMap(dto -> actionQueryDispatcher
                .executeQuery(mono, SerialGroupQuery.class, Pagination.class)
                .flatMap(pagination -> {
                    List<SerialGroupEntity> groups = pagination.getRecords();

                    // 并行处理每个分组，获取卡密条目数量
                    Flux<SerialGroupEntity> updatedGroups =
                            Flux.fromIterable(groups).flatMap(entity -> {
                                SerialItemReqDto serialItemReqDto = new SerialItemReqDto();
                                serialItemReqDto.setSerialGroupId(entity.getSerialGroupId());
                                serialItemReqDto.setGroupType(entity.getGroupType());
                                serialItemReqDto.setSessionId(dto.getSessionId());

                                // 卡密条目数量
                                return serialItemQueryService.searchNum(Mono.just(serialItemReqDto))
                                        .doOnNext(num -> entity.setSerialItemNum(num.getNum())) // 更新实体
                                        .thenReturn(entity); // 返回更新后的实体
                            });

                    // 收集所有更新后的实体，然后返回原始的分页对象
                    return updatedGroups.collectList().map(updatedRecords -> {
                        pagination.setRecords(updatedRecords);
                        return pagination;
                    });
                }));
    }

    @Override
    public Mono<Pagination> searchQuick(Mono<SerialGroupQuickReqDto> mono) {
        return mono.flatMap(
                dto -> actionQueryDispatcher.executeQuery(mono, SerialGroupQuery.class, query -> {
                    if(StringUtils.hasLength(dto.getKeyword())) {
                        query.setKeyword(dto.getKeyword().replace("'", ""));
                    }
                    return query;
                }, Pagination.class));
    }

    @Override
    public Mono<SerialGroupOriginalDetailResDto> searchOriginalDetail(
            Mono<SerialGroupDetailReqDto> mono) {
        return mono.flatMap(dto -> actionQueryDispatcher.executeQuery(mono,
                SerialGroupOriginalDetailQuery.class, SerialGroupOriginalDetailResDto.class));
    }

    @Override
    public Mono<SerialGroupSpecialDetailResDto> searchSpecialDetail(
            Mono<SerialGroupDetailReqDto> mono) {
        return mono.flatMap(dto -> actionQueryDispatcher.executeQuery(mono,
                SerialGroupSpecialDetailQuery.class, SerialGroupSpecialDetailResDto.class));
    }

    @Override
    public Mono<Pagination> searchGroupTeam(Mono<AppSerialGroupTeamReqDto> mono) {
        return mono.flatMap(dto -> actionQueryDispatcher.executeQuery(mono,
                SerialGroupTeamQuery.class, Pagination.class));
    }
}
