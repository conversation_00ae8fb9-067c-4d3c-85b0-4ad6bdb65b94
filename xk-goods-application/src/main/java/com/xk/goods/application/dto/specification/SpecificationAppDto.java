package com.xk.goods.application.dto.specification;

import com.xk.goods.domain.model.specification.entity.SpecificationEntity;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.price.PriceIdentifierConvertor;
import com.xk.goods.interfaces.dto.res.goods.SpecDetailResDto;
import com.xk.infrastructure.convertor.stock.StockIdentifierConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = SpecificationEntity.class, convertGenerate = false,
                uses = {GoodsIdentifierConvertor.class, StockIdentifierConvertor.class,
                        PriceIdentifierConvertor.class}),
        @AutoMapper(target = SpecDetailResDto.class, reverseConvertGenerate = false)})
public class SpecificationAppDto {

    /**
     * 商品id
     */
    @AutoMappings({
            @AutoMapping(targetClass = SpecificationEntity.class, target = "goodsIdentifier")})
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 规格id
     */
    private Long specificationId;

    /**
     * 规格名称
     */
    @AutoMappings({@AutoMapping(targetClass = SpecificationEntity.class,
            target = "specificationNameValObj.specName")})
    private String specName;

    /**
     * 规格单位
     */
    private String unitType;

    /**
     * 规格价格
     */
    private Long amount;

    /**
     * 货币类型
     */
    private Integer currencyType;

    /**
     * 库存id
     */
    @AutoMappings({
            @AutoMapping(targetClass = SpecificationEntity.class, target = "stockIdentifier")})
    private Long stockId;

    /**
     * 价格id
     */
    @AutoMappings({
            @AutoMapping(targetClass = SpecificationEntity.class, target = "priceIdentifier")})
    private Long priceId;

    /**
     * 分发id
     */
    private Long distributionId;
}
