package com.xk.goods.application.handler.command.goods;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.xk.goods.application.action.command.goods.UpdateGoodsCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.service.goods.GoodsRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateGoodsHandler implements IActionCommandHandler<UpdateGoodsCommand, Void> {

    private final GoodsRootService goodsRootService;

    @Override
    public Mono<Void> execute(Mono<UpdateGoodsCommand> mono) {
        return mono.flatMap(command -> goodsRootService
                .getRoot(GoodsIdentifier.builder().goodsId(command.getGoodsId()).build())
                .switchIfEmpty(Mono.error(
                        new XkGoodsApplicationException(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                .map(command::getRoot)
                .flatMap(root -> goodsRootService.updateRoot(Mono.just(root))));
    }
}
