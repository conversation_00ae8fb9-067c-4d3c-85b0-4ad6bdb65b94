package com.xk.goods.application.handler.command.score;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.score.UpdateScoreRuleCommand;
import com.xk.goods.domain.model.score.ScoreRuleRoot;
import com.xk.goods.domain.model.score.entity.ScoreRuleEntity;
import com.xk.goods.domain.service.score.ScoreRuleRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateScoreRuleHandler implements IActionCommandHandler<UpdateScoreRuleCommand, Void> {

    private final ScoreRuleRootService scoreRuleRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdateScoreRuleCommand> mono) {
        return this.execute(mono, command -> {
            ScoreRuleEntity entity = converter.convert(command, ScoreRuleEntity.class);
            return Mono.just(ScoreRuleRoot.builder().identifier(entity.getIdentifier())
                    .scoreRuleEntity(entity).build());
        }, scoreRuleRootService::updateRoot);
    }
}
