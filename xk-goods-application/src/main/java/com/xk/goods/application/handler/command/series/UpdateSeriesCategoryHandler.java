package com.xk.goods.application.handler.command.series;

import java.util.List;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.goods.application.action.command.series.UpdateSeriesCategoryCommand;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.domain.event.series.UpdateSeriesCateEvent;
import com.xk.goods.domain.model.series.SeriesCategoryRoot;
import com.xk.goods.domain.model.series.entity.SeriesCategoryEntity;
import com.xk.goods.domain.repository.series.SeriesCategoryRootRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateSeriesCategoryHandler
        implements IActionCommandHandler<UpdateSeriesCategoryCommand, Void> {

    private final Converter converter;

    private final SeriesCategoryRootRepository categoryRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<UpdateSeriesCategoryCommand> mono) {
        return mono.flatMap(command -> {
            // 命令执行函数
            Function<UpdateSeriesCategoryCommand, Mono<Void>> executeFunc = cmdParam -> execute(
                    Mono.just(cmdParam), SeriesCategoryEntity.class, this.converter::convert,
                    entity -> SeriesCategoryRoot.builder()
                            .identifier(LongIdentifier.builder().id(entity.getSeriesCategoryId())
                                    .build())
                            .seriesCategoryEntity(entity).build(),
                    categoryRootRepository::update).then();

            // 选队图片：事件发布函数
            Function<UpdateSeriesCategoryCommand, Mono<Void>> publishFunc =
                    cmdParam -> Mono.just(cmdParam).map(cmd -> {
                        // 选队图片
                        List<BaseBusinessRes> convertPic = converter.convert(cmd.getSeriesPicList(), BaseBusinessRes.class);
                        // 赠品图片
                        convertPic.addAll(converter.convert(cmd.getGiftPicList(), BaseBusinessRes.class));
                        convertPic.forEach(res -> res.setBusinessGroupType(BusinessGroupTypeEnum.SERIES));
                        return EventRoot.builder().domainEvent(UpdateSeriesCateEvent.builder()
                                .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                        UpdateSeriesCateEvent.class))
                                .categoryName(cmd.getName()).sort(cmd.getSort())
                                .status(cmd.getStatus()).seriesCategoryId(cmd.getSeriesCategoryId())
                                .groupBusinessType(GroupBusinessTypeEnum.SERIES.getValue())
                                .groupId(Long.valueOf(cmd.getBusiType()))
                                .baseBusinessResList(convertPic).build()).build();
                    }).flatMap(eventRootService::publisheByMono).then()
                            .doOnSuccess(v -> log.info("UpdateSeriesCateEvent事件发布完成: {}",
                                    cmdParam.getSeriesCategoryId()));

            return executeFunc.apply(command)
                    .then(publishFunc.apply(command));
        });
    }
}
