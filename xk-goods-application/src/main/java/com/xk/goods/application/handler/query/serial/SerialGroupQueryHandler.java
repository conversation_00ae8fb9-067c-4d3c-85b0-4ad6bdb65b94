package com.xk.goods.application.handler.query.serial;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.goods.application.action.query.serial.SerialGroupQuery;
import com.xk.goods.domain.model.serial.entity.SerialGroupEntity;
import com.xk.goods.domain.repository.serial.SerialGroupRootQueryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SerialGroupQueryHandler implements IActionQueryHandler<SerialGroupQuery, Pagination> {

    private final SerialGroupRootQueryRepository serialGroupRootQueryRepository;


    @Override
    public Mono<Pagination> execute(Mono<SerialGroupQuery> queryMono) {
        return execute(queryMono, serialGroupQuery -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(serialGroupQuery.getLimit());
            pagination.setOffset(serialGroupQuery.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(serialGroupQuery));
            return pagination;
        }, serialGroupRootQueryRepository::searchSerialGroup, SerialGroupEntity.class);
    }
}
