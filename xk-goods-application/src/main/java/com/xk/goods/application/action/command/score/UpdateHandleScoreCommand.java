package com.xk.goods.application.action.command.score;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.score.entity.ScoreEntity;
import com.xk.goods.interfaces.dto.req.score.ScoreUpdateReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = ScoreUpdateReqDto.class, convertGenerate = false),
        @AutoMapper(target = ScoreEntity.class, reverseConvertGenerate = false),})
public class UpdateHandleScoreCommand extends AbstractActionCommand {

    private Long goodsId;

    /**
     * 手动调整分数
     */
    private Long handleScore;

    /**
     * 更新时间
     */
    private Date updateTime;
}
