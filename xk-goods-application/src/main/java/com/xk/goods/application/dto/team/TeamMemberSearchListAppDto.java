package com.xk.goods.application.dto.team;

import java.util.Set;

import com.xk.goods.domain.model.team.entity.TeamMemberEntity;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;
import com.xk.goods.interfaces.dto.res.team.TeamMemberResDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = TeamMemberResDto.class),
        @AutoMapper(target = TeamMemberEntity.class)
})
public class TeamMemberSearchListAppDto {

    private Long teamMemberId;

    /**
     * 队员类型
     */
    private Integer memberType;

    /**
     * 队员中文名
     */
    private String memberCnName;

    /**
     * 队员英文名
     */
    private String memberEnName;

    /**
     * 资源地址集合
     */
    private Set<BusinessResDto> businessResDtoSet;
}
