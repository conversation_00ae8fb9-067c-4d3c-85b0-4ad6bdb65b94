package com.xk.goods.application.handler.event.merchant;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.application.commons.XkGoodsApplicationErrorEnum;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.merchant.MerchantProductDownEvent;
import com.xk.goods.domain.event.merchant.MerchantProductDownJobEvent;
import com.xk.goods.domain.model.goods.GoodsRoot;
import com.xk.goods.domain.model.goods.entity.GoodsEntity;
import com.xk.goods.domain.model.goods.valobj.GoodsListingValObj;
import com.xk.goods.domain.service.goods.GoodsRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class MerchantProductDownJobEventHandler
        extends AbstractEventVerticle<MerchantProductDownJobEvent> {

    private final GoodsRootService goodsRootService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<MerchantProductDownJobEvent> event) {
        return event.flatMap(data -> processProductDown(data.getTimeFormat()));
    }

    private Mono<Void> processProductDown(String timeFormat) {
        return Mono.defer(() -> {
            SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmmss");
            try {
                Date date = sdf.parse(timeFormat);
                GoodsEntity goodsEntity = GoodsEntity.builder().goodsId(-1L)
                        .goodsListingValObj(GoodsListingValObj.builder().planDownTime(date).build())
                        .build();
                GoodsRoot goodsRoot = GoodsRoot.builder().identifier(goodsEntity.getIdentifier())
                        .goodsEntity(goodsEntity).build();
                return goodsRootService.getGoodsDownCache(goodsRoot)
                        .switchIfEmpty(
                                goodsRootService.deleteGoodsDownCache(goodsRoot).then(Mono.empty()))
                        .flatMap(goodsIdentifier -> {
                            Long goodsId = goodsIdentifier.getGoodsId();
                            MerchantProductDownEvent downJobEvent =
                                    MerchantProductDownEvent.builder()
                                            .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                    MerchantProductDownEvent.class))
                                            .goodsId(goodsId).retryCount(0).build();
                            EventRoot eventRoot = EventRoot.builder().domainEvent(downJobEvent)
                                    .isQueue(true).isTry(true).build();
                            return eventRootService.publisheByMono(eventRoot)
                                    .doOnSuccess(v -> log.info("商品下架事件发布完成: {}", goodsId))
                                    .then(processProductDown(timeFormat));
                        });
            } catch (ParseException e) {
                return Mono.error(new XkGoodsApplicationException(
                        XkGoodsApplicationErrorEnum.DATE_CONVERT_ERROR));
            }
        });
    }
}
