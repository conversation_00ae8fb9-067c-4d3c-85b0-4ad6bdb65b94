package com.xk.goods.application.handler.event.goods;

import static com.xk.goods.application.commons.XkGoodsApplicationErrorEnum.APPLICATION_ERROR;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.application.action.command.business.CreateBusinessResCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.domain.event.goods.CreateGoodsEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateGoodsEventHandler extends AbstractEventVerticle<CreateGoodsEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CreateGoodsEvent> mono) {
        return mono.flatMap(this::processCreateGoodsEvent)
                .doOnSuccess(v -> log.debug("CreateGoodsEvent处理完成"))
                .onErrorResume(this::handleError);
    }

    /**
     * 处理创建商品资源
     *
     * @param event 创建商品事件
     * @return Mono<Void>
     */
    private Mono<Void> processCreateGoodsEvent(CreateGoodsEvent event) {
        List<BaseBusinessRes> resList = event.getBaseBusinessResList();

        // 如果业务资源列表为空，直接返回
        if (CollectionUtils.isEmpty(resList)) {
            log.debug("商品[{}]无业务资源需要创建", event.getGoodsId());
            return Mono.empty();
        }

        log.info("开始为商品[{}]创建{}个业务资源", event.getGoodsId(), resList.size());

        return Flux.fromIterable(resList).index()
                .flatMap(tuple -> createBusinessResource(tuple.getT2(),
                        Math.toIntExact(tuple.getT1()), event.getGoodsId()))
                .then().doOnSuccess(v -> log.info("商品[{}]的所有业务资源创建完成", event.getGoodsId()));
    }

    /**
     * 创建单个业务资源
     *
     * @param baseBusinessRes 业务资源信息
     * @param sort 排序号
     * @param goodsId 商品ID
     * @return Mono<Void>
     */
    private Mono<Void> createBusinessResource(BaseBusinessRes baseBusinessRes, Integer sort,
            Long goodsId) {
        return commandDispatcher.executeCommand(Mono.just(baseBusinessRes),
                CreateBusinessResCommand.class, command -> {
                    command.setSort(sort);
                    return command;
                }).doOnSuccess(v -> log.debug("商品[{}]业务资源创建成功: resId={}, sort={}", goodsId,
                        baseBusinessRes.getResId(), sort))
                .onErrorResume(e -> {
                    log.error("商品[{}]业务资源创建失败: resId={}, sort={}", goodsId,
                            baseBusinessRes.getResId(), sort, e);
                    return Mono.error(e);
                });
    }

    /**
     * 统一错误处理
     *
     * @param throwable 异常
     * @return Mono<Void>
     */
    private Mono<Void> handleError(Throwable throwable) {
        log.error("处理CreateGoodsEvent事件失败", throwable);
        return Mono.error(new XkGoodsApplicationException(APPLICATION_ERROR));
    }
}
