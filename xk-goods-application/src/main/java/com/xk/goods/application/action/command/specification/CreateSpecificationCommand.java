package com.xk.goods.application.action.command.specification;

import java.util.Date;
import java.util.List;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.model.specification.entity.SpecificationEntity;
import com.xk.goods.domain.model.specification.id.SpecificationIdentifier;
import com.xk.goods.domain.model.specification.valobj.SpecificationGiftValObj;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.specification.GiftBusinessTypeEnum;
import com.xk.goods.enums.specification.SpecificationTypeEnum;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.price.PriceIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.series.SeriesCategoryIdentifierConvertor;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.infrastructure.convertor.stock.StockIdentifierConvertor;

import io.github.linpeilie.Converter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SpecificationEntity.class,
        uses = {BusinessTypeEnumConvertor.class, PriceIdentifierConvertor.class,
                GoodsIdentifierConvertor.class, StockIdentifierConvertor.class,
                PlatformTypeEnumConvertor.class, SeriesCategoryIdentifierConvertor.class},
        reverseConvertGenerate = false)})
public class CreateSpecificationCommand extends AbstractActionCommand {

    /**
     * 商品规格id
     */
    private Long specificationId;

    /**
     * 商品id
     */
    @AutoMapping(targetClass = SpecificationEntity.class, target = "goodsIdentifier")
    private Long goodsId;

    /**
     * 库存id
     */
    @AutoMapping(targetClass = SpecificationEntity.class, target = "stockIdentifier")
    private Long stockId;

    /**
     * 价格id
     */
    @AutoMapping(targetClass = SpecificationEntity.class, target = "priceIdentifier")
    private Long priceId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 规格名称
     */
    @AutoMappings({@AutoMapping(targetClass = SpecificationEntity.class,
            target = "specificationNameValObj.specName")})
    private String specName;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 规格单位
     */
    private String unitType;

    /**
     * 赠品组id
     */
    private Long businessGroupId;

    /**
     * 赠品业务类型
     */
    private Integer giftBusinessType;

    /**
     * 赠品id
     */
    private List<Long> giftBuisnessIdList;
    /**
     * 系列id
     */
    private Long seriesCategoryId;

    /**
     * 系列名称
     */
    private String categoryName;

    /**
     * 创建人ID
     */
    @AutoMappings({@AutoMapping(targetClass = SpecificationEntity.class, source = "createId",
            target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({@AutoMapping(targetClass = SpecificationEntity.class, source = "createTime",
            target = "createValObj.createTime")})
    private Date createTime;

    public void setDefaultUnitType(@NonNull GoodsTypeEnum goodsTypeEnum) {
        switch (goodsTypeEnum) {
            case MATERIAL_PRODUCT, MALL_PRODUCT -> this.unitType = "个";
            case COLLECTIBLE_CARD -> this.unitType = "包";
            default -> this.unitType = "盒";
        }
    }

    public SpecificationRoot getRoot(Converter converter) {
        SpecificationEntity entity = converter.convert(this, SpecificationEntity.class);
        SpecificationIdentifier identifier =
                SpecificationIdentifier.builder().specificationId(this.specificationId).build();
        SpecificationRoot.SpecificationRootBuilder builder =
                SpecificationRoot.builder().identifier(identifier).specificationEntity(entity);

        if (CollectionUtils.isNotEmpty(giftBuisnessIdList)) {
            builder.specificationGiftValObj(
                    SpecificationGiftValObj.builder().giftBusinessIdList(giftBuisnessIdList)
                            .giftBusinessType(GiftBusinessTypeEnum.getByCode(giftBusinessType))
                            .businessGroupId(this.businessGroupId)
                            .specificationId(entity.getIdentifier()).build());
            entity.setSpecificationType(SpecificationTypeEnum.GIFT);
        } else {
            entity.setSpecificationType(SpecificationTypeEnum.PRODUCT);
        }

        return builder.build();
    }

    public void buildCreate(Long userId) {
        this.createId = userId;
        this.createTime = new Date();
    }

    public void buildPickGift(Long serialGroupId, Long teamId) {
        this.setBusinessGroupId(serialGroupId);
        this.setGiftBusinessType(GiftBusinessTypeEnum.PICK.getCode());
        this.giftBuisnessIdList = List.of(teamId);
    }

    public void buildNonePickGift(Long serialGroupId) {
        this.setBusinessGroupId(serialGroupId);
        this.setGiftBusinessType(GiftBusinessTypeEnum.NONE_PICK.getCode());
        // 防止缓存穿透
        this.giftBuisnessIdList = List.of(-1L);
    }

    public void buildFortuneGift(Long serialGroupId) {
        this.setBusinessGroupId(serialGroupId);
        this.setGiftBusinessType(GiftBusinessTypeEnum.FORTUNE_BOX.getCode());
    }
}
