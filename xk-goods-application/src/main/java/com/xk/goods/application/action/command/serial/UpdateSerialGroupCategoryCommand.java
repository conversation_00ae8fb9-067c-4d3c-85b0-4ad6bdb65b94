package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.serial.entity.SerialGroupCategoryEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupCategoryUpdateReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialGroupCategoryUpdateReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialGroupCategoryEntity.class, reverseConvertGenerate = false)})
public class UpdateSerialGroupCategoryCommand extends AbstractActionCommand {

    private Long serialGroupCategoryId;

    /**
     * 卡密组类型：1球队卡密组；2自定义卡密组
     */
    private Integer groupType;

    /**
     * 模块类型：1球队；2动漫
     */
    private Integer blockType;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 用户ID
     */
    private Long userId;
}
