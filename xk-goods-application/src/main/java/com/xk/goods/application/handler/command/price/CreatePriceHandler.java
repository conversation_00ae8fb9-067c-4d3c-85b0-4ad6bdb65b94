package com.xk.goods.application.handler.command.price;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.price.CreatePriceCommand;
import com.xk.goods.domain.model.price.PriceRoot;
import com.xk.goods.domain.model.price.entity.PriceEntity;
import com.xk.goods.domain.model.price.entity.PriceItemEntity;
import com.xk.goods.domain.model.price.id.PriceIdentifier;
import com.xk.goods.domain.service.price.PriceRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreatePriceHandler implements IActionCommandHandler<CreatePriceCommand, Void> {

    private final PriceRootService priceRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreatePriceCommand> mono) {
        return mono.flatMap(command -> priceRootService.saveRoot(buildRoot(command)));
    }

    private Mono<PriceRoot> buildRoot(CreatePriceCommand command) {
        PriceEntity priceEntity = converter.convert(command, PriceEntity.class);
        List<PriceItemEntity> priceItemEntities =
                converter.convert(command.getPriceItemDtoList(), PriceItemEntity.class);
        return Mono.just(PriceRoot.builder()
                .identifier(PriceIdentifier.builder().priceId(command.getPriceId()).build())
                .priceEntity(priceEntity).priceItemEntityList(priceItemEntities).build());
    }
}
