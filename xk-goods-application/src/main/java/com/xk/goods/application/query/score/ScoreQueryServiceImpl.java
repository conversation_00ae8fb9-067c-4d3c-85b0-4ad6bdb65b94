package com.xk.goods.application.query.score;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.goods.application.action.query.score.ScoreSearchPagerQuery;
import com.xk.goods.interfaces.dto.req.score.ScorePagerReqDto;
import com.xk.goods.interfaces.query.score.ScoreQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScoreQueryServiceImpl implements ScoreQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    @BusiCode
    @Override
    public Mono<Pagination> searchPager(Mono<ScorePagerReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, ScoreSearchPagerQuery.class,
                Pagination.class);
    }
}
