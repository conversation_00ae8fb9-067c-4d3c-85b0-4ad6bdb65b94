package com.xk.goods.application.handler.query.serial;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.goods.application.action.query.serial.SerialTemplateQuery;
import com.xk.goods.domain.model.template.entity.SerialTemplateEntity;
import com.xk.goods.domain.repository.serial.SerialTemplateRootQueryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SerialTemplateQueryHandler implements IActionQueryHandler<SerialTemplateQuery, Pagination> {

    private final SerialTemplateRootQueryRepository serialTemplateRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<SerialTemplateQuery> queryMono) {
        return execute(queryMono, serialTemplateQuery -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(serialTemplateQuery.getLimit());
            pagination.setOffset(serialTemplateQuery.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(serialTemplateQuery));
            return pagination;
        }, serialTemplateRootQueryRepository::searchSerialTemplate, SerialTemplateEntity.class);
    }

}
