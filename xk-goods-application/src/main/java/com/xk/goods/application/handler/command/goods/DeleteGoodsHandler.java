package com.xk.goods.application.handler.command.goods;

import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.goods.DeleteGoodsCommand;
import com.xk.goods.domain.model.goods.GoodsRoot;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.service.goods.GoodsRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteGoodsHandler implements IActionCommandHandler<DeleteGoodsCommand, Void> {

    private final GoodsRootService goodsRootService;

    @Override
    public Mono<Void> execute(Mono<DeleteGoodsCommand> mono) {
        return mono.flatMap(command -> {
            Supplier<Mono<GoodsRoot>> buildRoot = () -> goodsRootService
                    .getRoot(GoodsIdentifier.builder().goodsId(command.getGoodsId()).build());

            Function<GoodsRoot, Mono<Void>> doRemove =
                    root -> goodsRootService.removeRoot(Mono.just(root));

            return buildRoot.get().flatMap(doRemove);
        });
    }
}
