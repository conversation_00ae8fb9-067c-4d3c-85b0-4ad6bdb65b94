package com.xk.goods.application.action.query.serial;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.interfaces.dto.req.goods.GiftItemReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@AutoMappers({@AutoMapper(target = GiftItemReqDto.class, convertGenerate = false)})
public class GiftItemQuery extends PagerQuery implements IActionQuery {
    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 关键字
     * */
    private String keyword;
}
