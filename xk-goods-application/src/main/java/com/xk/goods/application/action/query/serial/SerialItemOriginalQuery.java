package com.xk.goods.application.action.query.serial;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialItemOriginalReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMappers({@AutoMapper(target = SerialItemOriginalReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialOriginalItemEntity.class, reverseConvertGenerate = false)})
public class SerialItemOriginalQuery extends PagerQuery implements IActionQuery {

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 关键字
     */
    private List<String> keywords;

    /**
     * 系列ID
     */
    private Long seriesCategoryId;

    /**
     * 是否只显示限编
     */
    private Integer onlyLimitEdition;
}
