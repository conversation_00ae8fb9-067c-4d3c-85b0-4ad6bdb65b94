package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.template.entity.SerialTemplateEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialTemplateDetailReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialTemplateDetailReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialTemplateEntity.class, reverseConvertGenerate = false)})
public class DeleteSerialTemplateCommand extends AbstractActionCommand {

    private Long userId;

    /**
     * 卡密模板id
     */
    private Long serialTemplateId;
}
