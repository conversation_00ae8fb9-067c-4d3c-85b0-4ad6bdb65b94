package com.xk.goods.application.dto.merchant;

import java.io.Serializable;

import com.xk.goods.domain.model.merchant.valobj.RankingValObj;
import com.xk.goods.infrastructure.convertor.merchant.ProductRewardTypeEnumConvertor;
import com.xk.goods.interfaces.dto.req.goods.merchant.RankingReqDto;
import com.xk.goods.interfaces.dto.res.goods.merchant.RankingResDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = RankingReqDto.class, convertGenerate = false),
        @AutoMapper(target = RankingResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = RankingValObj.class, uses = {ProductRewardTypeEnumConvertor.class}),})
public class RankingDto implements Serializable {
    /**
     * 榜单排名
     */
    private Integer rewardRank;

    /**
     * 奖励类型
     */
    private Integer rewardType;

    /**
     * 奖励物品
     */
    private String reward;
}
