package com.xk.goods.application.handler.command.team;

import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.goods.application.action.command.team.UpdateTeamMemberCommand;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.domain.event.team.UpdateTeamMemberEvent;
import com.xk.goods.domain.model.team.TeamMemberRoot;
import com.xk.goods.domain.model.team.entity.TeamMemberEntity;
import com.xk.goods.domain.repository.team.TeamMemberRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateTeamMemberHandler
        implements IActionCommandHandler<UpdateTeamMemberCommand, Void> {
    private final Converter converter;

    private final TeamMemberRootRepository memberRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<UpdateTeamMemberCommand> command) {
        return command.flatMap(cmd -> {
            // 命令执行函数
            Function<UpdateTeamMemberCommand, Mono<Void>> executeFunc = cmdParam -> execute(
                    Mono.just(cmdParam), TeamMemberEntity.class, this.converter::convert,
                    entity -> TeamMemberRoot.builder()
                            .identifier(
                                    LongIdentifier.builder().id(entity.getTeamMemberId()).build())
                            .teamMemberEntity(entity).build(),
                    memberRootRepository::update).then();

            // 事件发布函数
            Function<UpdateTeamMemberCommand, Mono<Void>> publishFunc = cmdParam -> Mono
                    .just(cmdParam.getBusinessResIdentifierList())
                    .map(resList -> EventRoot.builder().domainEvent(UpdateTeamMemberEvent.builder()
                            .identifier(EventRoot
                                    .getCommonsDomainEventIdentifier(UpdateTeamMemberEvent.class))
                            .baseBusinessResList(converter.convert(resList, BaseBusinessRes.class))
                            .build()).build())
                    .flatMap(eventRootService::publisheByMono).then().doOnSuccess(v -> log
                            .info("UpdateTeamMemberEvent事件发布完成: {}", cmdParam.getTeamMemberId()));

            return executeFunc.apply(cmd).then(publishFunc.apply(cmd));
        });
    }
}
