package com.xk.goods.application.handler.event.score;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.domain.event.score.QueryScorePagerEvent;
import com.xk.goods.domain.event.score.UpdateScoreRuleEvent;
import com.xk.goods.domain.model.merchant.entity.MerchantProductEntity;
import com.xk.goods.domain.repository.merchant.MerchantProductRootQueryRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateScoreRuleEventHandler extends AbstractEventVerticle<UpdateScoreRuleEvent> {

    private static final Integer PAGE_SIZE = 100;
    private final MerchantProductRootQueryRepository merchantProductRootQueryRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<UpdateScoreRuleEvent> mono) {
        return mono.flatMap(event -> merchantProductRootQueryRepository
                .soldProductCount(
                        MerchantProductEntity.builder().productType(event.getProductType()).build())
                .flatMapMany(totalCount -> {
                    // 多算一页防止中途插入数据导致漏算商品
                    int totalPages = (int) Math.ceil((double) totalCount / PAGE_SIZE) + 2;
                    return Flux.range(1, totalPages).flatMap(page -> {
                        EventRoot root = EventRoot.builder()
                                .domainEvent(QueryScorePagerEvent.builder()
                                        .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                QueryScorePagerEvent.class))
                                        .scoreRuleType(event.getScoreRuleType())
                                        .ruleVersion(event.getRuleVersion()).pageNumber(page)
                                        .pageSize(PAGE_SIZE).build())
                                .isQueue(true).build();
                        return eventRootService.publisheByMono(root);
                    });
                }).then().doOnSuccess(
                        v -> log.info("UpdateScoreRuleEvent事件处理完成: {}", event.getScoreRuleType())))
                .onErrorResume(e -> {
                    log.error("UpdateScoreRuleEvent事件处理失败: {}", e.getMessage(), e);
                    return Mono.empty();
                }).then();
    }
}
