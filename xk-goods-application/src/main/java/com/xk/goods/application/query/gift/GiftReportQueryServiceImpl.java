package com.xk.goods.application.query.gift;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.domain.model.count.CountEntity;
import com.xk.domain.service.count.CountRootDomainService;
import com.xk.enums.count.CountBusiTypeEnum;
import com.xk.goods.application.action.query.gift.GiftReportInnerSearchQuery;
import com.xk.goods.application.action.query.gift.GiftReportQuery;
import com.xk.goods.application.action.query.gift.GiftReportUpdateQuery;
import com.xk.goods.application.commons.XkGoodsApplicationErrorEnum;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.infrastructure.cache.dao.gift.GiftReportDao;
import com.xk.goods.infrastructure.cache.key.gift.GiftReportKey;
import com.xk.goods.infrastructure.cache.po.gift.GiftReportPo;
import com.xk.goods.infrastructure.cache.po.gift.ResourcePo;
import com.xk.goods.interfaces.dto.req.gift.GiftReportInnerSearchReq;
import com.xk.goods.interfaces.dto.req.gift.GiftReportSyncReq;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdRequirePagerReqDto;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdRequireReqDto;
import com.xk.goods.interfaces.dto.req.goods.ReportPagerReqDto;
import com.xk.goods.interfaces.dto.res.gift.GiftReportPreQueryRep;
import com.xk.goods.interfaces.dto.res.gift.GiftReportSyncRep;
import com.xk.goods.interfaces.dto.res.gift.GiftSearchRep;
import com.xk.goods.interfaces.dto.res.gift.GiftSearchUpdateRep;
import com.xk.goods.interfaces.query.gift.GiftReportQueryService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class GiftReportQueryServiceImpl implements GiftReportQueryService {

    private final GiftReportDao giftReportDao;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;
    private final SelectorRootService selectorRootService;
    private final CountRootDomainService countRootDomainService;

    @BusiCode
    @Override
    public Mono<List<GiftReportSyncRep>> sync(Mono<GiftReportSyncReq> mono) {
        // 1. 创建缓存键函数
        BiFunction<Long, Long, GiftReportKey> createKey = (goodsId, serialItemId) -> GiftReportKey
                .builder().goodsId(goodsId).serialItemId(serialItemId).build();

        // 2. 获取缓存值并转换为初步响应
        Function<GiftReportKey, Mono<GiftReportSyncRep>> getValueAndConvert =
                key -> Mono.fromCallable(() -> giftReportDao.getValue(key)).filter(Objects::nonNull)
                        .map(po -> GiftReportSyncRep.builder()
                                .operatorUserId(po.getOperatorUserId())
                                .serialItemId(po.getSerialItemId()).build());

        // 3. 添加用户名信息
        Function<GiftReportSyncRep, Mono<GiftReportSyncRep>> enrichWithUserInfo =
                rep -> selectorRootService.getUserObject(rep.getOperatorUserId())
                        .map(userObject -> {
                            rep.setNickName(userObject.getUserDataObjectEntity().getNickname());
                            rep.setPicId(userObject.getUserDataObjectEntity().getPicId());
                            return rep;
                        }).onErrorResume(e -> Mono.just(rep)); // 获取用户信息失败时返回原始响应

        // 组合所有函数
        return mono.flatMap(dto -> Flux.fromIterable(dto.getSerialItemIdList())
                .flatMap(serialItemId -> getValueAndConvert
                        .apply(createKey.apply(dto.getGoodsId(), serialItemId))
                        .flatMap(enrichWithUserInfo))
                .collectList());
    }

    @BusiCode
    @Override
    public Mono<List<GiftReportPreQueryRep>> preQueryCurrent(Mono<GoodsIdRequireReqDto> mono) {
        // 1. 创建缓存键函数
        Function<GoodsIdRequireReqDto, GiftReportKey> createKey =
                dto -> GiftReportKey.builder().goodsId(dto.getGoodsId()).build();

        // 2. 获取并处理缓存值函数
        Function<GiftReportKey, Mono<List<GiftReportPo>>> getValues =
                key -> Mono.fromCallable(() -> {
                    var allValue = giftReportDao.getAllValue(key);
                    // 默认创建时添加了空键，移除掉
                    allValue.remove("");
                    return allValue.values().stream().toList();
                });

        // 3. 转换PO到DTO函数
        Function<GiftReportPo, GiftReportPreQueryRep.PreQueryDto> poToDto = po -> {
            List<ResourcePo> resDtoList =
                    Objects.requireNonNullElse(po.getResDtoList(), Collections.emptyList());
            return GiftReportPreQueryRep.PreQueryDto.builder().serialItemId(po.getSerialItemId())
                    .serialItemName(po.getSerialItemName())
                    .addrList(resDtoList.stream().map(ResourcePo::getAddr).toList())
                    .updateTime(po.getUpdateTime()).build();
        };

        // 4. 获取用户信息并构建最终响应函数
        BiFunction<Long, List<GiftReportPreQueryRep.PreQueryDto>, Mono<GiftReportPreQueryRep>> buildResponse =
                (operatorUserId, dtoList) -> selectorRootService.getUserObject(operatorUserId)
                        .map(userObject -> GiftReportPreQueryRep.builder()
                                .operatorUserId(operatorUserId).preQueryDto(dtoList)
                                .nickName(userObject.getUserDataObjectEntity().getNickname())
                                .picId(userObject.getUserDataObjectEntity().getPicId()).build())
                        .onErrorResume(e -> Mono.just(GiftReportPreQueryRep.builder()
                                .operatorUserId(operatorUserId).preQueryDto(dtoList).build()));

        // 组合所有函数
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> mono.flatMap(dto -> getValues.apply(createKey.apply(dto))
                        .flatMapMany(Flux::fromIterable).groupBy(GiftReportPo::getOperatorUserId)
                        .flatMap(group -> group.collectList()
                                .map(poList -> poList.stream().map(poToDto).toList())
                                .flatMap(dtoList -> buildResponse.apply(group.key(), dtoList)))
                        .filter(rsp -> Objects.equals(rsp.getOperatorUserId(), userId))
                        .collectList().defaultIfEmpty(Collections.emptyList())));
    }

    @BusiCode
    @Override
    public Mono<List<GiftReportPreQueryRep>> preQuery(Mono<GoodsIdRequireReqDto> mono) {
        // 1. 创建缓存键函数
        Function<GoodsIdRequireReqDto, GiftReportKey> createKey =
                dto -> GiftReportKey.builder().goodsId(dto.getGoodsId()).build();

        // 2. 获取并处理缓存值函数
        Function<GiftReportKey, Mono<List<GiftReportPo>>> getValues =
                key -> Mono.fromCallable(() -> {
                    var allValue = giftReportDao.getAllValue(key);
                    // 默认创建时添加了空键，移除掉
                    allValue.remove("");
                    return allValue.values().stream().toList();
                });

        // 3. 转换PO到DTO函数
        Function<GiftReportPo, GiftReportPreQueryRep.PreQueryDto> poToDto = po -> {
            List<ResourcePo> resDtoList =
                    Objects.requireNonNullElse(po.getResDtoList(), Collections.emptyList());
            return GiftReportPreQueryRep.PreQueryDto.builder().serialItemId(po.getSerialItemId())
                    .serialItemName(po.getSerialItemName())
                    .addrList(resDtoList.stream().map(ResourcePo::getAddr).toList())
                    .updateTime(po.getUpdateTime()).build();
        };

        // 4. 获取用户信息并构建最终响应函数
        BiFunction<Long, List<GiftReportPreQueryRep.PreQueryDto>, Mono<GiftReportPreQueryRep>> buildResponse =
                (operatorUserId, dtoList) -> selectorRootService.getUserObject(operatorUserId)
                        .map(userObject -> GiftReportPreQueryRep.builder()
                                .operatorUserId(operatorUserId).preQueryDto(dtoList)
                                .nickName(userObject.getUserDataObjectEntity().getNickname())
                                .picId(userObject.getUserDataObjectEntity().getPicId()).build())
                        .onErrorResume(e -> Mono.just(GiftReportPreQueryRep.builder()
                                .operatorUserId(operatorUserId).preQueryDto(dtoList).build()));

        // 组合所有函数
        return mono.flatMap(dto -> getValues.apply(createKey.apply(dto))
                .flatMapMany(Flux::fromIterable).groupBy(GiftReportPo::getOperatorUserId)
                .flatMap(group -> group.collectList()
                        .map(poList -> poList.stream().map(poToDto).toList())
                        .flatMap(dtoList -> buildResponse.apply(group.key(), dtoList)))
                .collectList().defaultIfEmpty(Collections.emptyList()));
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchPager(Mono<GoodsIdRequirePagerReqDto> mono) {
        return queryDispatcher.executeQuery(mono, GiftReportQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<List<GiftSearchUpdateRep>> searchUpdatePager(Mono<GoodsIdRequireReqDto> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObjectRoot -> mono.flatMap(dto -> {
                    Long corpId = userObjectRoot.getUserDataObjectEntity().getCorpId();
                    if (corpId == null) {
                        return Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.UNSUPPORTED_OPERATION));
                    }
                    Supplier<Mono<CountEntity>> buildCountEntity = () -> Mono.just(CountEntity
                            .builder().countBusiType(CountBusiTypeEnum.GIFT_REPORT)
                            .key(String.valueOf(dto.getGoodsId()))
                            .isExpired(CountBusiTypeEnum.GIFT_REPORT.getIsExpired()).build());

                    Function<CountEntity, Mono<Long>> getCount =
                            entity -> countRootDomainService.getCount(entity.getIdentifier());

                    Function<Long, Mono<Void>> validateCount = count -> count >= 3
                            ? Mono.error(new XkGoodsApplicationException(
                                    XkGoodsApplicationErrorEnum.GIFT_REPORT_COUNT_FAIL))
                            : Mono.empty();
                    return buildCountEntity.get().flatMap(getCount).flatMap(validateCount)
                            .then(queryManyDispatcher
                                    .executeQuery(mono, GiftReportUpdateQuery.class, query -> {
                                        query.setCorpId(corpId);
                                        return query;
                                    }, GiftSearchUpdateRep.class).collectList());
                }));
    }

    @BusiCode
    @Override
    public Mono<Pagination> innerSearchPager(Mono<ReportPagerReqDto> mono) {
        return queryDispatcher.executeQuery(mono, GiftReportQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<List<GiftSearchRep>> innerSearch(Mono<GiftReportInnerSearchReq> mono) {
        return queryManyDispatcher
                .executeQuery(mono, GiftReportInnerSearchQuery.class, GiftSearchRep.class)
                .collectList();
    }
}
