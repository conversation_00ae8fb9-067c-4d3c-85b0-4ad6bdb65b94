package com.xk.goods.application.action.command.goods;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.category.entity.GoodsCategoryEntity;
import com.xk.goods.interfaces.dto.req.goods.GoodsCategoryIdReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = GoodsCategoryIdReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsCategoryEntity.class, reverseConvertGenerate = false)})
public class DeleteGoodsCategoryCommand extends AbstractActionCommand {

    private Long goodsCategoryId;
}
