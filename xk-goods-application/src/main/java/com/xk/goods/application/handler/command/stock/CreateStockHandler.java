package com.xk.goods.application.handler.command.stock;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.domain.service.stock.StockRootService;
import com.xk.goods.application.action.command.stock.CreateStockCommand;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateStockHandler implements IActionCommandHandler<CreateStockCommand, Void> {

    private final StockRootService stockRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateStockCommand> mono) {
        return mono.flatMap(command -> stockRootService.saveRoot(command.getRoot(converter)));
    }
}
