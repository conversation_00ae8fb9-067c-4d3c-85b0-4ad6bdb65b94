package com.xk.goods.application.handler.query.goods;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.domain.model.stock.valobj.StockRemainValObj;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.application.action.query.goods.PickStockQuery;
import com.xk.goods.domain.model.merchant.entity.MerchantProductEntity;
import com.xk.goods.domain.model.serial.entity.SerialGroupTeamEntity;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.repository.merchant.MerchantProductRootQueryRepository;
import com.xk.goods.domain.repository.serial.SerialGroupRootQueryRepository;
import com.xk.goods.domain.service.price.PriceRootService;
import com.xk.goods.domain.service.specification.SpecificationRootService;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.merchant.MerchantProductIdentifierConvertor;
import com.xk.goods.interfaces.dto.res.goods.PickStockRsp;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

@Component
@RequiredArgsConstructor
public class PickStockQueryHandler
        implements IActionQueryManyHandler<PickStockQuery, PickStockRsp> {

    private final MerchantProductRootQueryRepository merchantProductRootQueryRepository;
    private final SerialGroupRootQueryRepository serialGroupRootQueryRepository;
    private final SpecificationRootService specificationRootService;
    private final StockRootService stockRootService;
    private final PriceRootService priceRootService;

    @Override
    public Flux<PickStockRsp> execute(Mono<PickStockQuery> mono) {
        return mono.flatMapMany(query -> buildPickStockData(query).flatMapMany(Flux::fromIterable)
                .flatMap(this::enrichWithStockInfo));
    }

    /**
     * 构建基础的库存选择数据
     */
    private Mono<List<PickStockRsp>> buildPickStockData(PickStockQuery query) {
        return getMerchantProductEntity(query.getGoodsId())
                .flatMap(entity -> buildStockResponseList(entity, query.getParentId()));
    }

    /**
     * 获取商品实体
     */
    private Mono<MerchantProductEntity> getMerchantProductEntity(Long goodsId) {
        return merchantProductRootQueryRepository
                .findEntity(MerchantProductIdentifierConvertor.map(goodsId));
    }

    /**
     * 构建库存响应列表
     */
    private Mono<List<PickStockRsp>> buildStockResponseList(MerchantProductEntity entity,
            Long parentId) {
        // 并行获取序列组团队数据和规格数据
        Mono<Map<Long, SerialGroupTeamEntity>> teamMapMono =
                getSerialGroupTeamMap(entity, parentId);
        Mono<List<SpecificationRoot>> specificationsMono = getSpecifications(entity);

        return Mono.zip(teamMapMono, specificationsMono)
                .flatMap(this::processSpecificationsWithTeams);
    }

    /**
     * 获取序列组团队映射
     */
    private Mono<Map<Long, SerialGroupTeamEntity>> getSerialGroupTeamMap(
            MerchantProductEntity entity, Long parentId) {
        return serialGroupRootQueryRepository
                .searchSerialGroupTeamByGroupId(entity.getSerialGroupIdentifier())
                .filter(v -> parentId == null || Objects.equals(parentId, v.getParentId()))
                .collectList().map(teams -> teams.stream().collect(Collectors
                        .toMap(SerialGroupTeamEntity::getSeriesCategoryId, Function.identity())));
    }

    /**
     * 获取规格列表
     */
    private Mono<List<SpecificationRoot>> getSpecifications(MerchantProductEntity entity) {
        return specificationRootService
                .searchByGoodsId(GoodsIdentifierConvertor.map(entity.getGoodsId()));
    }

    /**
     * 处理规格与团队数据的匹配
     */
    private Mono<List<PickStockRsp>> processSpecificationsWithTeams(
            Tuple2<Map<Long, SerialGroupTeamEntity>, List<SpecificationRoot>> tuple) {

        Map<Long, SerialGroupTeamEntity> teamMap = tuple.getT1();
        List<SpecificationRoot> specifications = tuple.getT2();

        return Flux.fromIterable(specifications)
                .flatMap(spec -> createPickStockResponse(spec, teamMap)).collectList();
    }

    /**
     * 创建单个库存选择响应
     */
    private Mono<PickStockRsp> createPickStockResponse(SpecificationRoot spec,
            Map<Long, SerialGroupTeamEntity> teamMap) {

        // 获取第一个礼品业务ID作为系列分类ID
        Long seriesCategoryId = getSeriesCategoryId(spec);
        if (seriesCategoryId == null) {
            return Mono.empty();
        }

        SerialGroupTeamEntity teamEntity = teamMap.get(seriesCategoryId);
        if (teamEntity == null) {
            return Mono.empty();
        }

        // 构建基础响应对象
        PickStockRsp response = buildBaseResponse(spec, teamEntity);

        // 异步获取价格信息
        return enrichWithPriceInfo(spec, response);
    }

    /**
     * 获取系列分类ID
     */
    private Long getSeriesCategoryId(SpecificationRoot spec) {
        return spec.getSpecificationGiftValObj().getGiftBusinessIdList().stream().findFirst()
                .orElse(null);
    }

    /**
     * 构建基础响应对象
     */
    private PickStockRsp buildBaseResponse(SpecificationRoot spec,
            SerialGroupTeamEntity teamEntity) {
        PickStockRsp response = new PickStockRsp();
        response.setSpecificationId(spec.getIdentifier().getSpecificationId());
        response.setCategoryName(teamEntity.getCategoryName());
        response.setAddr(teamEntity.getAddr());
        response.setTotalRealStock(teamEntity.getSerialItemNum().longValue());
        return response;
    }

    /**
     * 丰富价格信息
     */
    private Mono<PickStockRsp> enrichWithPriceInfo(SpecificationRoot spec, PickStockRsp response) {
        return priceRootService
                .getDefaultPriceItem(spec.getSpecificationEntity().getPriceIdentifier())
                .doOnNext(priceItem -> response.setAmount(priceItem.getAmount()))
                .thenReturn(response).onErrorReturn(response); // 价格获取失败时返回原对象
    }

    /**
     * 丰富库存信息
     */
    private Mono<PickStockRsp> enrichWithStockInfo(PickStockRsp response) {
        StringIdentifier specId =
                StringIdentifier.builder().id(response.getSpecificationId().toString()).build();

        return stockRootService.getRemainRealStock(StockBusinessTypeEnum.SPECIFICATION, specId)
                .map(StockRemainValObj::getRemainRealStock).defaultIfEmpty(0L)
                .doOnNext(response::setRemainRealStock).thenReturn(response)
                .onErrorReturn(response); // 库存获取失败时返回原对象
    }
}
