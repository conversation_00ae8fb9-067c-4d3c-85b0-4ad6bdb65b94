package com.xk.goods.application.handler.command.score;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.score.UpdateHandleScoreCommand;
import com.xk.goods.domain.model.score.entity.ScoreEntity;
import com.xk.goods.domain.service.score.ScoreRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateHandleScoreHandler
        implements IActionCommandHandler<UpdateHandleScoreCommand, Void> {

    private final ScoreRootService scoreRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdateHandleScoreCommand> mono) {
        return mono.flatMap(command -> scoreRootService
                .updateHandlerScore(converter.convert(command, ScoreEntity.class)));
    }
}
