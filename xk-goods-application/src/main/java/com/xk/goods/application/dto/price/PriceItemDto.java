package com.xk.goods.application.dto.price;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.xk.goods.domain.model.price.entity.PriceItemEntity;
import com.xk.goods.enums.price.CurrencyTypeEnum;
import com.xk.goods.enums.price.PricePlatformTypeEnum;
import com.xk.goods.enums.price.PriceTypeEnum;
import com.xk.goods.infrastructure.convertor.price.CurrencyTypeEnumConvertor;
import com.xk.goods.infrastructure.convertor.price.PriceIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.price.PricePlatformTypeEnumConvertor;
import com.xk.goods.infrastructure.convertor.price.PriceTypeEnumConvertor;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = PriceItemEntity.class,
        uses = {PriceIdentifierConvertor.class, CurrencyTypeEnumConvertor.class,
                PriceTypeEnumConvertor.class, PricePlatformTypeEnumConvertor.class,
                CommonStatusEnumConvertor.class}),})
public class PriceItemDto implements Serializable {
    /**
     * 价格id
     */
    @AutoMapping(targetClass = PriceItemEntity.class, target = "priceIdentifier")
    private Long priceId;

    /**
     * 货币类型:1-人民币
     */
    private Integer currencyType;

    /**
     * 价格类型 1-成本价 2-销售价
     */
    private Integer priceType;

    /**
     * 价格平台类型 1-ios 2-android
     */
    private Integer pricePlatformType;

    /**
     * 价格
     */
    private Long amount;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 逻辑删除:0-未删除,1-已删除
     */
    private Integer deleted;

    /**
     * 创建人ID
     */
    @AutoMappings({
            @AutoMapping(targetClass = PriceItemEntity.class, target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = PriceItemEntity.class, target = "createValObj.createTime")})
    private Date createTime;

    /**
     * 更新人ID
     */
    @AutoMappings({
            @AutoMapping(targetClass = PriceItemEntity.class, target = "updateValObj.updateId")})
    private Long updateId;

    /**
     * 更新时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = PriceItemEntity.class, target = "updateValObj.updateTime")})
    private Date updateTime;

    public static List<PriceItemDto> buildMaterialPrice(Long amount, Long priceId, Long userId,
            Integer specAmount) {
        return buildSalePrice(amount, priceId, userId, specAmount);
    }

    public static List<PriceItemDto> updateMaterialPrice(Long amount, Long priceId, Long userId) {
        return updateSalePrice(amount, priceId, userId);
    }

    public static List<PriceItemDto> buildMallPrice(Long saleAmount, Long costAmount, Long priceId,
            Long userId) {
        List<PriceItemDto> result = new ArrayList<>();
        result.addAll(buildCostPrice(costAmount, priceId, userId));
        result.addAll(buildSalePrice(saleAmount, priceId, userId, 1));
        return result;
    }

    public static List<PriceItemDto> updateMallPrice(Long saleAmount, Long costAmount, Long priceId,
            Long userId) {
        List<PriceItemDto> result = new ArrayList<>();
        result.addAll(buildCostPrice(costAmount, priceId, userId));
        result.addAll(updateSalePrice(saleAmount, priceId, userId));
        return result;
    }

    public static List<PriceItemDto> buildCollectivePrice(Long amount, Long priceId, Long userId) {
        return buildSalePrice(amount, priceId, userId, 1);
    }

    public static List<PriceItemDto> updateCollectivePrice(Long amount, Long priceId, Long userId) {
        return updateSalePrice(amount, priceId, userId);
    }

    public static List<PriceItemDto> buildMerchantProductPrice(Long amount, Long priceId,
            Long userId) {
        return buildSalePrice(amount, priceId, userId, 1);
    }

    private static List<PriceItemDto> buildCostPrice(Long costAmount, Long priceId, Long userId) {
        PriceItemDto iosDto =
                PriceItemDto.builder().priceId(priceId).currencyType(CurrencyTypeEnum.CNY.getCode())
                        .priceType(PriceTypeEnum.COST_PRICE.getCode())
                        .pricePlatformType(PricePlatformTypeEnum.IOS.getCode()).amount(costAmount)
                        .createId(userId).createTime(new Date()).build();
        PriceItemDto androidDto =
                PriceItemDto.builder().priceId(priceId).currencyType(CurrencyTypeEnum.CNY.getCode())
                        .priceType(PriceTypeEnum.COST_PRICE.getCode())
                        .pricePlatformType(PricePlatformTypeEnum.ANDROID.getCode())
                        .amount(costAmount).createId(userId).createTime(new Date()).build();
        return Arrays.asList(iosDto, androidDto);
    }

    private static List<PriceItemDto> buildSalePrice(Long amount, Long priceId, Long userId,
            Integer specAmount) {
        PriceItemDto iosDto = PriceItemDto.builder().priceId(priceId)
                .currencyType(CurrencyTypeEnum.CNY.getCode())
                .priceType(PriceTypeEnum.SALE_PRICE.getCode())
                .pricePlatformType(PricePlatformTypeEnum.IOS.getCode()).amount(amount * specAmount)
                .createId(userId).createTime(new Date()).build();
        PriceItemDto androidDto = PriceItemDto.builder().priceId(priceId)
                .currencyType(CurrencyTypeEnum.CNY.getCode())
                .priceType(PriceTypeEnum.SALE_PRICE.getCode())
                .pricePlatformType(PricePlatformTypeEnum.ANDROID.getCode())
                .amount(amount * specAmount).createId(userId).createTime(new Date()).build();
        return Arrays.asList(iosDto, androidDto);
    }

    private static List<PriceItemDto> updateSalePrice(Long amount, Long priceId, Long userId) {
        PriceItemDto iosDto =
                PriceItemDto.builder().priceId(priceId).currencyType(CurrencyTypeEnum.CNY.getCode())
                        .priceType(PriceTypeEnum.SALE_PRICE.getCode())
                        .pricePlatformType(PricePlatformTypeEnum.IOS.getCode()).amount(amount)
                        .updateId(userId).updateTime(new Date()).build();
        PriceItemDto androidDto =
                PriceItemDto.builder().priceId(priceId).currencyType(CurrencyTypeEnum.CNY.getCode())
                        .priceType(PriceTypeEnum.SALE_PRICE.getCode())
                        .pricePlatformType(PricePlatformTypeEnum.ANDROID.getCode()).amount(amount)
                        .updateId(userId).updateTime(new Date()).build();
        return Arrays.asList(iosDto, androidDto);
    }

    private static List<PriceItemDto> updateCostPrice(Long costAmount, Long priceId, Long userId) {
        PriceItemDto iosDto =
                PriceItemDto.builder().priceId(priceId).currencyType(CurrencyTypeEnum.CNY.getCode())
                        .priceType(PriceTypeEnum.COST_PRICE.getCode())
                        .pricePlatformType(PricePlatformTypeEnum.IOS.getCode()).amount(costAmount)
                        .updateId(userId).updateTime(new Date()).build();
        PriceItemDto androidDto =
                PriceItemDto.builder().priceId(priceId).currencyType(CurrencyTypeEnum.CNY.getCode())
                        .priceType(PriceTypeEnum.COST_PRICE.getCode())
                        .pricePlatformType(PricePlatformTypeEnum.ANDROID.getCode()).updateId(userId)
                        .updateTime(new Date()).build();
        return Arrays.asList(iosDto, androidDto);
    }
}
