package com.xk.goods.application.handler.command.serial;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.xk.domain.model.category.CateIdentifier;
import com.xk.domain.model.res.SysResourceEntity;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.domain.model.tree.TreeNodeIdentifier;
import com.xk.domain.repository.cate.CateRootQueryRepository;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;
import com.xk.domain.service.tree.TreeRootService;
import com.xk.goods.application.action.command.serial.SaveSerialGroupTeamCommand;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.model.serial.SerialGroupRoot;
import com.xk.goods.domain.model.serial.entity.SerialGroupTeamEntity;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.repository.business.BusinessResRootQueryRepository;
import com.xk.goods.domain.repository.serial.SerialGroupRootRepository;
import com.xk.goods.enums.business.BusinessResTypeEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaveSerialGroupTeamHandler
        implements IActionCommandHandler<SaveSerialGroupTeamCommand, Void> {

    private final SerialGroupRootRepository serialGroupRootRepository;

    private final BusinessResRootQueryRepository businessResRootQueryRepository;

    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    private final CateRootQueryRepository cateRootQueryRepository;

    private final TreeRootService treeRootService;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<SaveSerialGroupTeamCommand> mono) {
        return mono.flatMap(command -> businessResRootQueryRepository
                .searchByBusinessRes(
                        BusinessResEntity.builder().businessId(command.getSeriesCategoryId())
                                .businessResType(BusinessResTypeEnum.SERIES_PICTURE).build())
                .next() // 只取第一个结果，因为Flux可能返回多个
                .switchIfEmpty(Mono.just(BusinessResEntity.builder().resId(0).build()))
                .flatMap(businessResEntity -> sysResourceRootQueryRepository
                        .findById(IntegerIdentifier.builder().id(businessResEntity.getResId())
                                .build())
                        .switchIfEmpty(
                                Mono.just(SysResourceEntity.builder().resId(0).build()))
                        .flatMap(sysResourceEntity -> cateRootQueryRepository
                                .findCateByComposite(CompositeIdentifier.builder()
                                        .groupId(BusinessTypeEnum.XING_KA.getValue().longValue())
                                        .id(command.getSeriesCategoryId())
                                        .type(GroupBusinessTypeEnum.SERIES.getValue()).build())
                                .flatMap(categoryEntity ->

                                treeRootService.findByBusinessId(CompositeIdentifier.builder()
                                        .groupId(0L).type(GroupBusinessTypeEnum.CATE.getValue()).id(
                                                categoryEntity.getCateId())
                                        .build())
                                        .flatMap(treeNode -> treeRootService
                                                .findById(TreeNodeIdentifier.builder()
                                                        .nodeId(treeNode.getParentId()).build())
                                                .flatMap(parentNode -> {
                                                    // 查询父资源信息
                                                    return cateRootQueryRepository
                                                            .findById(CateIdentifier.builder()
                                                                    .cateId((Long) parentNode
                                                                            .getCompositeIdentifier()
                                                                            .id())
                                                                    .build())
                                                            .flatMap(parentCateEntity -> {
                                                                Long parentBusinessId =
                                                                        parentCateEntity
                                                                                .getCompositeIdentifiers() != null
                                                                                && !parentCateEntity
                                                                                        .getCompositeIdentifiers()
                                                                                        .isEmpty()
                                                                                                ? (Long) parentCateEntity
                                                                                                        .getCompositeIdentifiers()
                                                                                                        .stream()
                                                                                                        .findFirst()
                                                                                                        .map(CompositeIdentifier::id)
                                                                                                        .orElse(0L)
                                                                                                : 0L;

                                                                return businessResRootQueryRepository
                                                                        .searchByBusinessRes(
                                                                                BusinessResEntity
                                                                                        .builder()
                                                                                        .businessId(
                                                                                                parentBusinessId)
                                                                                        .businessResType(
                                                                                                BusinessResTypeEnum.SERIES_PICTURE)
                                                                                        .build())
                                                                        .next()
                                                                        .switchIfEmpty(Mono.just(
                                                                                BusinessResEntity
                                                                                        .builder()
                                                                                        .resId(0)
                                                                                        .build()))
                                                                        .flatMap(
                                                                                parentBusinessResEntity -> sysResourceRootQueryRepository
                                                                                        .findById(
                                                                                                IntegerIdentifier
                                                                                                        .builder()
                                                                                                        .id(parentBusinessResEntity
                                                                                                                .getResId())
                                                                                                        .build())
                                                                                        .switchIfEmpty(
                                                                                                Mono.just(
                                                                                                        SysResourceEntity
                                                                                                                .builder()
                                                                                                                .resId(0)
                                                                                                                .build()))
                                                                                        .map(parentSysResourceEntity -> {
                                                                                            List<SerialGroupTeamEntity> entityList =
                                                                                                    new ArrayList<>();
                                                                                            SerialGroupTeamEntity entity =
                                                                                                    converter
                                                                                                            .convert(
                                                                                                                    command,
                                                                                                                    SerialGroupTeamEntity.class);
                                                                                            entity.setResId(
                                                                                                    sysResourceEntity
                                                                                                            .getResId());
                                                                                            entity.setAddr(
                                                                                                    sysResourceEntity
                                                                                                            .getAddr());
                                                                                            entity.setParentId(
                                                                                                    parentBusinessId);
                                                                                            entity.setParentName(
                                                                                                    parentNode
                                                                                                            .getName());
                                                                                            entity.setParentResId(
                                                                                                    parentSysResourceEntity
                                                                                                            .getResId());
                                                                                            entity.setParentAddr(
                                                                                                    parentSysResourceEntity
                                                                                                            .getAddr());
                                                                                            SerialGroupIdentifier identifier =
                                                                                                    SerialGroupIdentifier
                                                                                                            .builder()
                                                                                                            .serialGroupId(
                                                                                                                    command.getSerialGroupId())
                                                                                                            .build();
                                                                                            entityList
                                                                                                    .add(entity);
                                                                                            return SerialGroupRoot
                                                                                                    .builder()
                                                                                                    .identifier(
                                                                                                            identifier)
                                                                                                    .serialGroupTeamEntityList(
                                                                                                            entityList)
                                                                                                    .build();
                                                                                        }).flatMap(
                                                                                                serialGroupRootRepository::save));
                                                            });
                                                }))
                                        .then()))));
    }
}
