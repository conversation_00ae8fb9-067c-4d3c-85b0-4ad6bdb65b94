package com.xk.goods.application.handler.command.series;

import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.goods.application.action.command.series.DeleteSeriesCategoryCommand;
import com.xk.goods.domain.event.series.DeleteSeriesCateEvent;
import com.xk.goods.domain.model.series.SeriesCategoryRoot;
import com.xk.goods.domain.model.series.entity.SeriesCategoryEntity;
import com.xk.goods.domain.repository.series.SeriesCategoryRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteSeriesCategoryHandler
        implements IActionCommandHandler<DeleteSeriesCategoryCommand, Void> {

    private final Converter converter;

    private final SeriesCategoryRootRepository categoryRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<DeleteSeriesCategoryCommand> mono) {
        return mono.flatMap(command -> {
            // 命令执行函数
            Function<DeleteSeriesCategoryCommand, Mono<Void>> executeFunc = cmdParam -> execute(
                    Mono.just(cmdParam), SeriesCategoryEntity.class, this.converter::convert,
                    entity -> SeriesCategoryRoot.builder()
                            .identifier(LongIdentifier.builder().id(entity.getSeriesCategoryId())
                                    .build())
                            .seriesCategoryEntity(entity).build(),
                    categoryRootRepository::remove).then();

            // 事件发布函数
            Function<DeleteSeriesCategoryCommand, Mono<Void>> publishFunc = cmdParam -> Mono
                    .just(cmdParam)
                    .map(cmd -> EventRoot.builder().domainEvent(DeleteSeriesCateEvent.builder()
                            .identifier(EventRoot
                                    .getCommonsDomainEventIdentifier(DeleteSeriesCateEvent.class))
                            .groupBusinessType(GroupBusinessTypeEnum.SERIES.getValue())
                            .seriesCategoryId(cmd.getSeriesCategoryId())
                            .groupId(Long.valueOf(cmd.getBusiType()))
                            .build()).build())
                    .flatMap(eventRootService::publisheByMono).then()
                    .doOnSuccess(v -> log.info("DeleteSeriesCateEvent事件发布完成: {}",
                            cmdParam.getSeriesCategoryId()));

            return executeFunc.apply(command).then(publishFunc.apply(command));
        });
    }
}
