package com.xk.goods.application.handler.command.merchant;

import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.merchant.DeleteMerchantProductCommand;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.merchant.MerchantProductRoot;
import com.xk.goods.domain.model.merchant.id.MerchantProductIdentifier;
import com.xk.goods.domain.service.merchant.MerchantProductRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;


@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteMerchantProductHandler
        implements IActionCommandHandler<DeleteMerchantProductCommand, Void> {

    private final MerchantProductRootService merchantProductRootService;

    @Override
    public Mono<Void> execute(Mono<DeleteMerchantProductCommand> mono) {
        return mono.flatMap(command -> {
            Supplier<Mono<MerchantProductRoot>> buildRoot =
                    () -> merchantProductRootService.getRoot(MerchantProductIdentifier.builder()
                            .goodsIdentifier(
                                    GoodsIdentifier.builder().goodsId(command.getGoodsId()).build())
                            .build());

            Function<MerchantProductRoot, Mono<Void>> doRemove =
                    root -> merchantProductRootService.removeRoot(Mono.just(root));

            return buildRoot.get().flatMap(doRemove);
        });
    }
}
