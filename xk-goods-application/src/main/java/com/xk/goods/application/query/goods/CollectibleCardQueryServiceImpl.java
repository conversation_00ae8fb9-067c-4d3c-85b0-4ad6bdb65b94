package com.xk.goods.application.query.goods;

import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.commons.constant.SystemConstant;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.application.action.query.goods.CollectibleDetailQuery;
import com.xk.goods.enums.goods.ListingStatusEnum;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdReqDto;
import com.xk.goods.interfaces.dto.req.goods.collectible.CollectibleSearchPagerReqDto;
import com.xk.goods.interfaces.dto.req.goods.collectible.CollectibleSearchShowPagerReqDto;
import com.xk.goods.interfaces.dto.res.goods.CollectibleDetailResDto;
import com.xk.goods.interfaces.query.goods.CollectibleCardQueryService;
import com.xk.search.interfaces.dto.req.goods.SearchCollectibleCardReqDto;
import com.xk.search.interfaces.query.goods.CollectibleQueryService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class CollectibleCardQueryServiceImpl implements CollectibleCardQueryService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final CollectibleQueryService collectibleQueryService;

    @BusiCode
    @Override
    public Mono<Pagination> searchPager(Mono<CollectibleSearchPagerReqDto> mono) {
        return mono.flatMap(dto -> {
            SearchCollectibleCardReqDto req =
                    SearchCollectibleCardReqDto.builder().goodsName(dto.getGoodsName())
                            .listingStatus(dto.getListingStatus()).showStatus(dto.getShowStatus())
                            .seriesName(dto.getSeriesName()).blockType(dto.getBlockType()).build();
            req.setSessionId(dto.getSessionId());
            req.setSort(dto.getSort());
            req.setOrder(SystemConstant.BusinessOrderType.valueOf(dto.getOrder()));
            req.setCurrentPage(dto.getCurrentPage());
            req.setLimit(dto.getLimit());
            req.setOffset(dto.getOffset());
            return collectibleQueryService.searchCardByIds(Mono.just(req))
                    .flatMap(result -> Flux.fromIterable(result.getRecords())
                            .map(v -> objectMapper.convertValue(v, GoodsIdReqDto.class))
                            .flatMap(resDto -> actionQueryDispatcher.executeQuery(resDto,
                                    CollectibleDetailQuery.class, CollectibleDetailResDto.class))
                            .collectList().doOnNext(result::setRecords).thenReturn(result));
        });
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchShowPager(Mono<CollectibleSearchShowPagerReqDto> mono) {
        return mono.flatMap(dto -> {
            SearchCollectibleCardReqDto req = SearchCollectibleCardReqDto.builder()
                    .goodsName(dto.getGoodsName()).listingStatus(ListingStatusEnum.UP.getCode())
                    .showStatus(CommonStatusEnum.ENABLE.getCode()).seriesName(dto.getSeriesName())
                    .blockType(dto.getBlockType()).build();
            req.setSessionId(dto.getSessionId());
            req.setSort(dto.getSort());
            req.setOrder(SystemConstant.BusinessOrderType.valueOf(dto.getOrder()));
            req.setCurrentPage(dto.getCurrentPage());
            req.setLimit(dto.getLimit());
            req.setOffset(dto.getOffset());
            return collectibleQueryService.searchCardByIds(Mono.just(req))
                    .flatMap(result -> Flux.fromIterable(result.getRecords())
                            .map(v -> objectMapper.convertValue(v, GoodsIdReqDto.class))
                            .flatMap(resDto -> actionQueryDispatcher.executeQuery(resDto,
                                    CollectibleDetailQuery.class, CollectibleDetailResDto.class))
                            .collectList().doOnNext(result::setRecords).thenReturn(result));
        });
    }

    @BusiCode
    @Override
    public Mono<CollectibleDetailResDto> detail(Mono<GoodsIdReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, CollectibleDetailQuery.class,
                CollectibleDetailResDto.class);
    }
}
