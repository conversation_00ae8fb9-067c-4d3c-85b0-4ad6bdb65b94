package com.xk.goods.application.action.query.team;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.interfaces.dto.req.team.TeamMemberSearchPagerReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@AutoMappers({
        @AutoMapper(target = TeamMemberSearchPagerReqDto.class)
})
public class TeamMemberSearchQuery extends PagerQuery implements IActionQuery {

    /**
     * 队员类型
     */
    private Integer memberType;

    /**
     * 队员中文名
     */
    private String memberCnName;

    /**
     * 队员英文名
     */
    private String memberEnName;
}
