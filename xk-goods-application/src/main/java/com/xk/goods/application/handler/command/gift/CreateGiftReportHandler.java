package com.xk.goods.application.handler.command.gift;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.gift.CreateGiftReportCommand;
import com.xk.goods.domain.model.gift.GiftReportRoot;
import com.xk.goods.domain.model.gift.entity.GiftReportEntity;
import com.xk.goods.domain.service.gift.GiftReportRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateGiftReportHandler
        implements IActionCommandHandler<CreateGiftReportCommand, Void> {

    private final GiftReportRootService giftReportRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateGiftReportCommand> mono) {
        return mono.map(command -> {
            GiftReportEntity entity = converter.convert(command, GiftReportEntity.class);
            return Mono.just(GiftReportRoot.builder().identifier(entity.getIdentifier())
                    .giftReportEntity(entity).build());
        }).flatMap(giftReportRootService::saveRoot);
    }
}
