package com.xk.goods.application.handler.event.gift;

import static com.xk.goods.application.commons.XkGoodsApplicationErrorEnum.APPLICATION_ERROR;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.application.action.command.business.CreateBusinessResCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.gift.GiftReportCreateEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class GiftReportCreateEventHandler extends AbstractEventVerticle<GiftReportCreateEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<GiftReportCreateEvent> mono) {
        return mono.flatMap(event -> Mono.justOrEmpty(event.getBaseBusinessResList())
                .flatMap(resList -> Flux.fromIterable(resList).index().flatMap(
                        tuple2 -> commandDispatcher.executeCommand(Mono.just(tuple2.getT2()),
                                CreateBusinessResCommand.class, command -> {
                                    command.setSort(Math.toIntExact(tuple2.getT1()));
                                    return command;
                                }).doOnSuccess(v -> log.info("GiftReportCreateEvent已创建业务资源: {}",
                                        event.getGiftReportId())))
                        .onErrorResume(e -> {
                            log.error("处理CreateGoodsEvent事件失败: {}", event, e);
                            return Mono.error(new XkGoodsApplicationException(APPLICATION_ERROR));
                        }).then()));
    }
}
