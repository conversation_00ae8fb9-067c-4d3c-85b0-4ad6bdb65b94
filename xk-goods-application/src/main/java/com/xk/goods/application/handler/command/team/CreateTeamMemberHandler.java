package com.xk.goods.application.handler.command.team;

import java.util.List;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.goods.application.action.command.team.CreateTeamMemberCommand;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.domain.event.team.CreateTeamMemberEvent;
import com.xk.goods.domain.model.team.TeamMemberRoot;
import com.xk.goods.domain.model.team.entity.TeamMemberEntity;
import com.xk.goods.domain.repository.team.TeamMemberRootRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateTeamMemberHandler
        implements IActionCommandHandler<CreateTeamMemberCommand, Void> {

    private final Converter converter;

    private final TeamMemberRootRepository memberRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<CreateTeamMemberCommand> command) {
        return command.flatMap(cmd -> {
            // 命令执行函数
            Function<CreateTeamMemberCommand, Mono<Void>> executeCommand = cmdParam -> execute(
                    Mono.just(cmdParam), TeamMemberEntity.class, this.converter::convert,
                    entity -> TeamMemberRoot.builder()
                            .identifier(
                                    LongIdentifier.builder().id(entity.getTeamMemberId()).build())
                            .teamMemberEntity(entity).build(),
                    memberRootRepository::save).then();

            // 事件发布函数
            Function<CreateTeamMemberCommand, Mono<Void>> publishEvent = cmdParam -> Mono
                    .just(cmdParam.getBusinessResIdentifierList())
                    .map(resList -> {
                        List<BaseBusinessRes> convert = converter.convert(resList, BaseBusinessRes.class);
                        convert.forEach(res-> res.setBusinessGroupType(BusinessGroupTypeEnum.MEMBER));
                        return EventRoot.builder().domainEvent(CreateTeamMemberEvent.builder()
                                .identifier(EventRoot
                                        .getCommonsDomainEventIdentifier(CreateTeamMemberEvent.class))
                                .baseBusinessResList(convert)
                                .build()).build();
                    })
                    .flatMap(eventRootService::publisheByMono).then().doOnSuccess(v -> log
                            .info("CreateTeamMemberEvent事件发布完成: {}", cmdParam.getTeamMemberId()));

            return executeCommand.apply(cmd).then(publishEvent.apply(cmd));
        });
    }
}
