package com.xk.goods.application.action.command.price;

import static com.xk.application.commons.CommonUtil.setIfNotNull;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.application.dto.price.PriceItemDto;
import com.xk.goods.domain.model.price.PriceRoot;
import com.xk.goods.domain.model.price.entity.PriceEntity;
import com.xk.goods.domain.model.price.entity.PriceItemEntity;
import com.xk.goods.domain.model.price.id.PriceItemIdentifier;
import com.xk.goods.infrastructure.convertor.specification.SpecificationIdentifierConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = PriceEntity.class,
        uses = {SpecificationIdentifierConvertor.class}, reverseConvertGenerate = false),})
public class UpdatePriceCommand extends AbstractActionCommand {

    /**
     * 价格id
     */
    private Long priceId;

    /**
     * 规格id
     */
    private Long specificationId;

    /**
     * 价格条目
     */
    private List<PriceItemDto> priceItemDtoList;

    public PriceRoot getRoot(PriceRoot root, List<PriceItemEntity> list) {
        Map<PriceItemIdentifier, PriceItemEntity> updateMap =
                list.stream().collect(Collectors.toMap(PriceItemEntity::getIdentifier, v -> v));
        List<PriceItemEntity> result = root.getPriceItemEntityList().stream()
                .filter(v -> updateMap.containsKey(v.getIdentifier())).map(oldEntity -> {
                    PriceItemEntity newEntity = updateMap.get(oldEntity.getIdentifier());
                    return PriceItemEntity.builder().priceIdentifier(oldEntity.getPriceIdentifier())
                            .currencyType(oldEntity.getCurrencyType())
                            .priceType(oldEntity.getPriceType())
                            .pricePlatformType(oldEntity.getPricePlatformType())
                            .amount(setIfNotNull(newEntity.getAmount(), oldEntity.getAmount()))
                            .status(oldEntity.getStatus()).deleted(oldEntity.getDeleted())
                            .deleted(oldEntity.getDeleted())
                            .createValObj(oldEntity.getCreateValObj())
                            .updateValObj(setIfNotNull(newEntity.getUpdateValObj(),
                                    oldEntity.getUpdateValObj()))
                            .build();
                }).toList();

        return PriceRoot.builder().identifier(root.getIdentifier())
                .priceEntity(root.getPriceEntity()).priceItemEntityList(result).build();
    }
}
