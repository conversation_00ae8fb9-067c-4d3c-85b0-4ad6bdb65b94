package com.xk.goods.application.handler.command.serial;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.serial.SaveSerialTemplateCommand;
import com.xk.goods.domain.model.template.SerialTemplateRoot;
import com.xk.goods.domain.model.template.entity.SerialTemplateEntity;
import com.xk.goods.domain.model.template.id.SerialTemplateIdentifier;
import com.xk.goods.domain.repository.serial.SerialTemplateRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaveSerialTemplateHandler implements IActionCommandHandler<SaveSerialTemplateCommand, Void> {

    private final Converter converter;

    private final SerialTemplateRootRepository serialTemplateRootRepository;

    @Override
    public Mono<Void> execute(Mono<SaveSerialTemplateCommand> commandMono) {
        return commandMono.flatMap(command -> {
            SerialTemplateEntity entity = new SerialTemplateEntity();
            converter.convert(command, entity);
            entity.setSerialTemplate(String.join(";", command.getSerialTemplates()));
            entity.setUpdateId(command.getUserId());
            entity.setCreateId(command.getUserId());
            SerialTemplateIdentifier identifier = SerialTemplateIdentifier.builder().serialTemplateId(entity.getSerialTemplateId()).build();
            return serialTemplateRootRepository.save(SerialTemplateRoot.builder()
                            .identifier(identifier).serialTemplateEntity(entity).build());
        });
    }
}
