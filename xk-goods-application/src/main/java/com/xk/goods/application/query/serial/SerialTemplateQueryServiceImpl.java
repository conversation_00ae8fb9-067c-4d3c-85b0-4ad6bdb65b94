package com.xk.goods.application.query.serial;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.goods.application.action.query.serial.SerialTemplateDetailQuery;
import com.xk.goods.application.action.query.serial.SerialTemplateQuery;
import com.xk.goods.interfaces.dto.req.serial.SerialTemplateDetailReqDto;
import com.xk.goods.interfaces.dto.req.serial.SerialTemplateReqDto;
import com.xk.goods.interfaces.dto.res.serial.SerialTemplateDetailResDto;
import com.xk.goods.interfaces.query.serial.SerialTemplateQueryService;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SerialTemplateQueryServiceImpl implements SerialTemplateQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    private final Converter converter;

    @BusiCode
    @Override
    public Mono<Pagination> getList(Mono<SerialTemplateReqDto> mono) {
        return mono.flatMap(dto -> actionQueryDispatcher.executeQuery(mono, SerialTemplateQuery.class, Pagination.class));
    }

    @BusiCode
    @Override
    public Mono<SerialTemplateDetailResDto> getDetail(Mono<SerialTemplateDetailReqDto> mono) {
        return mono.flatMap(dto -> actionQueryDispatcher.executeQuery(mono, SerialTemplateDetailQuery.class,
                SerialTemplateDetailResDto.class));
    }
}
