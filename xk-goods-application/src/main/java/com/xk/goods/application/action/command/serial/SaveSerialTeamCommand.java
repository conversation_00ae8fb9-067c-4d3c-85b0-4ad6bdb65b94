package com.xk.goods.application.action.command.serial;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.application.dto.serial.SerialGroupOriginalItemTemplate;
import com.xk.goods.domain.model.serial.entity.SerialTeamEntity;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialGroupOriginalItemTemplate.class, convertGenerate = false),
        @AutoMapper(target = SerialTeamEntity.class, reverseConvertGenerate = false)})
public class SaveSerialTeamCommand extends AbstractActionCommand {

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 球队名称
     */
    private String teamName;

    /**
     * 球队类型
     */
    private Integer teamType;

    /**
     * 特效为：TEAM
     */
    private String color;

    /**
     * 球员id
     */
    private Long teamMemberId;

    /**
     * 球员图片
     */
    private String memberPicAddr;

    /**
     * 球员头像
     */
    private String memberAvatarAddr;

    /**
     * 创建时间
     */
    private Date createTime;
}
