package com.xk.goods.application.handler.query.goods;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.xk.goods.application.action.query.goods.PickCategoryQuery;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.repository.serial.SerialGroupRootQueryRepository;
import com.xk.goods.interfaces.dto.res.goods.PickCategoryRsp;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class PickCategoryQueryHandler
        implements IActionQueryManyHandler<PickCategoryQuery, PickCategoryRsp> {

    private final SerialGroupRootQueryRepository serialGroupRootQueryRepository;

    @Override
    public Flux<PickCategoryRsp> execute(Mono<PickCategoryQuery> mono) {
        return execute(mono,
                query -> SerialGroupIdentifier.builder().serialGroupId(query.getSerialGroupId())
                        .build(),
                serialGroupRootQueryRepository::selectGroupSerialGroupId, data -> {
                    PickCategoryRsp rsp = new PickCategoryRsp();
                    rsp.setParentAddr(data.getParentAddr());
                    rsp.setParentId(data.getParentId());
                    return rsp;
                });
    }
}
