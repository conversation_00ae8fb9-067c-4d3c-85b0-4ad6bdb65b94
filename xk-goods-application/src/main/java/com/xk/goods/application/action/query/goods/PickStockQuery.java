package com.xk.goods.application.action.query.goods;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.goods.interfaces.dto.req.goods.PickStockReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = PickStockReq.class, convertGenerate = false)})
public class PickStockQuery implements IActionQueryMany {

    /**
     * 主键
     */
    private Long goodsId;

    /**
     * 父id
     */
    private Long parentId;
}
