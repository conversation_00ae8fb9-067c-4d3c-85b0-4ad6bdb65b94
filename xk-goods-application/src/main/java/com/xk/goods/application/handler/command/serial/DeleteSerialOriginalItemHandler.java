package com.xk.goods.application.handler.command.serial;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.serial.DeleteSerialOriginalItemCommand;
import com.xk.goods.domain.repository.serial.SerialItemRootRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteSerialOriginalItemHandler implements IActionCommandHandler<DeleteSerialOriginalItemCommand, Void> {

    private final SerialItemRootRepository serialItemRootRepository;

    @Override
    public Mono<Void> execute(Mono<DeleteSerialOriginalItemCommand> commandMono) {

        return commandMono.flatMap(command -> serialItemRootRepository.removeBySerialGroupId(command.getSerialGroupId()));
    }
}
