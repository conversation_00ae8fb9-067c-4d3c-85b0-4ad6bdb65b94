package com.xk.goods.application.handler.command.price;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.price.DeletePriceCommand;
import com.xk.goods.domain.model.price.PriceRoot;
import com.xk.goods.domain.model.price.id.PriceIdentifier;
import com.xk.goods.domain.service.price.PriceRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeletePriceHandler implements IActionCommandHandler<DeletePriceCommand, Void> {

    private final PriceRootService priceRootService;

    @Override
    public Mono<Void> execute(Mono<DeletePriceCommand> mono) {
        return mono.flatMap(command -> priceRootService.removeRoot(buildRoot(command)));
    }

    private Mono<PriceRoot> buildRoot(DeletePriceCommand command) {
        return priceRootService
                .getRoot(PriceIdentifier.builder().priceId(command.getPriceId()).build());
    }
}
