package com.xk.goods.application.handler.event.score;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.domain.event.score.QueryScoreEvent;
import com.xk.goods.domain.event.score.QueryScorePagerEvent;
import com.xk.goods.domain.service.merchant.MerchantProductRootService;
import com.xk.goods.infrastructure.data.po.merchant.GMerchantProduct;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class QueryScorePagerEventHandler extends AbstractEventVerticle<QueryScorePagerEvent> {

    private final MerchantProductRootService merchantProductRootService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<QueryScorePagerEvent> mono) {
        return mono.flatMap(event -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(event.getPageSize());
            pagination.setOffset((event.getPageNumber() - 1) * event.getPageSize());
            if (event.getProductType() != null) {
                pagination.setCriteria(CollectionHelper.converBeanToMap(GMerchantProduct.builder()
                        .productType(event.getProductType().getCode()).build()));
            }
            return merchantProductRootService.pagerSoldProductIdByProductType(pagination)
                    .flatMap(entity -> {
                        EventRoot root = EventRoot.builder().domainEvent(QueryScoreEvent.builder()
                                .identifier(EventRoot
                                        .getCommonsDomainEventIdentifier(QueryScoreEvent.class))
                                .scoreRuleType(event.getScoreRuleType())
                                .productType(entity.getProductType())
                                .ruleVersion(event.getRuleVersion()).goodsId(entity.getGoodsId())
                                .build()).isQueue(true).build();
                        return eventRootService.publisheByMono(root);
                    }).then().doOnSuccess(
                            v -> log.info("QueryScorePagerEvent事件发布完成: {}", event.getPageNumber()));
        }).onErrorResume(e -> {
            log.error("QueryScorePagerEvent事件处理失败: {}", e.getMessage(), e);
            return Mono.empty();
        });
    }
}
