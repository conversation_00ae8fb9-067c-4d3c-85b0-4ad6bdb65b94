package com.xk.goods.application.action.command.business;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.infrastructure.convertor.business.BusinessGroupTypeEnumConvertor;
import com.xk.goods.infrastructure.convertor.business.BusinessResTypeEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMappers({@AutoMapper(target = BaseBusinessRes.class, convertGenerate = false), @AutoMapper(
        target = BusinessResEntity.class,
        uses = {BusinessResTypeEnumConvertor.class, BusinessGroupTypeEnumConvertor.class})})
public class CreateBusinessResCommand extends AbstractActionCommand {
    /**
     * 队员图片资源映射
     */
    private BusinessResTypeEnum businessResType;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 资源id
     */
    private Integer resId;

    /**
     * 业务分组类型
     */
    private BusinessGroupTypeEnum businessGroupType;

    /**
     * 排序
     */
    private Integer sort;
}
