package com.xk.goods.application.handler.command.merchant;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.merchant.CreateMerchantProductCommand;
import com.xk.goods.domain.service.merchant.MerchantProductRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateMerchantProductHandler
        implements IActionCommandHandler<CreateMerchantProductCommand, Void> {

    private final MerchantProductRootService merchantProductRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateMerchantProductCommand> mono) {
        return mono.flatMap(command -> merchantProductRootService
                .saveRoot(Mono.just(command.getRoot(converter))));
    }
}
