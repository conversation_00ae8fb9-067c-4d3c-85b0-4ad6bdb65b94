package com.xk.goods.application.action.command.activity;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.domain.model.activity.entity.GoodsActivityConfigEntity;
import com.xk.goods.infrastructure.convertor.activity.ActivityTypeEnumConvertor;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMappers({@AutoMapper(target = GoodsActivityConfigEntity.class, reverseConvertGenerate = false,
        uses = {CommonStatusEnumConvertor.class, ActivityTypeEnumConvertor.class})})
public class CreateGoodsActivityConfigCommand extends AbstractActionCommand {
    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品活动类型
     */
    private Integer activityType;

    /**
     * 是否参与展示计算状态
     */
    private Integer showCalcStatus;

    /**
     * 是否参与购买计算状态
     */
    private Integer buyCalcStatus;

    /**
     * 开关状态
     */
    private Integer status;

    private Integer deleted;

    /**
     * 创建人id
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsActivityConfigEntity.class,
            target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsActivityConfigEntity.class,
            target = "createValObj.createTime")})
    private Date createTime;

    public void buildCreate(Long userId, Date date) {
        this.createId = userId;
        this.createTime = date;
        this.deleted = CommonStatusEnum.DISABLE.getCode();
        this.status = CommonStatusEnum.ENABLE.getCode();
        this.showCalcStatus = CommonStatusEnum.ENABLE.getCode();
        this.buyCalcStatus = CommonStatusEnum.ENABLE.getCode();
    }
}
