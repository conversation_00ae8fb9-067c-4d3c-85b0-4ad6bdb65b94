package com.xk.goods.application.handler.command.color;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.color.DeleteSerialItemColorCommand;
import com.xk.goods.domain.model.color.id.SerialItemColorIdentifier;
import com.xk.goods.domain.service.color.SerialItemColorRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 删除卡密特效命令处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteSerialItemColorCommandHandler
        implements IActionCommandHandler<DeleteSerialItemColorCommand, Void> {

    private final SerialItemColorRootService serialItemColorRootService;

    @Override
    public Mono<Void> execute(Mono<DeleteSerialItemColorCommand> mono) {
        return mono.flatMap(command -> {
            SerialItemColorIdentifier identifier = SerialItemColorIdentifier.builder()
                    .serialItemColorId(command.getSerialItemColorId())
                    .build();
                    
            return serialItemColorRootService.getRoot(identifier)
                    .flatMap(root -> serialItemColorRootService.removeRoot(Mono.just(root)));
        });
    }
}
