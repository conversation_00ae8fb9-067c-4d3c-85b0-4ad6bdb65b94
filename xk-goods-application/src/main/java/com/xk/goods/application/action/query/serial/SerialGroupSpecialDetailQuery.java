package com.xk.goods.application.action.query.serial;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupDetailReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@AutoMappers({@AutoMapper(target = SerialGroupDetailReqDto.class, convertGenerate = false)})
public class SerialGroupSpecialDetailQuery implements IActionQuery {

    /**
     * 卡密组ID
     */
    private Long serialGroupId;

}
