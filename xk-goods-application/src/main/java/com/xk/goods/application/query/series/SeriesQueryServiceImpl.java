package com.xk.goods.application.query.series;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.application.action.query.cate.CateListByGroupQuery;
import com.xk.application.action.query.tree.TreeNodeQueryByComposite;
import com.xk.domain.model.category.Category;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.domain.model.tree.TreeNode;
import com.xk.goods.application.action.query.business.BusinessResBusinessQuery;
import com.xk.goods.domain.model.series.entity.SeriesCategoryEntity;
import com.xk.goods.domain.model.series.entity.SeriesTreeNode;
import com.xk.goods.domain.model.series.id.SeriesCategoryIdentifier;
import com.xk.goods.domain.repository.series.SeriesCategoryRootQueryRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.interfaces.dto.req.series.SeriesCategoryTreeReqDto;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;
import com.xk.goods.interfaces.dto.res.series.SeriesTreeNodeDto;
import com.xk.goods.interfaces.query.series.SeriesQueryService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class SeriesQueryServiceImpl implements SeriesQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;
    private final Converter converter;
    private final SeriesCategoryRootQueryRepository seriesCategoryRootQueryRepository;

    @BusiCode
    @Override
    public Mono<List<SeriesTreeNodeDto>> findSeriesCateTree(Mono<SeriesCategoryTreeReqDto> mono) {
        return mono.flatMap(req -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            // 1. 构建分类查询对象
            Function<SeriesCategoryTreeReqDto, CateListByGroupQuery> buildCateQuery =
                    request -> CateListByGroupQuery.builder()
                            .compositeIdentifier(CompositeIdentifier.builder()
                                    .groupId(Long.valueOf(request.getBizType()))
                                    .type(GroupBusinessTypeEnum.SERIES.getValue()).build())
                            .build();

            // 2. 分类处理流水线
            Function<Category, Mono<SeriesTreeNode>> processCategory = category -> {
                // 2.1 构建树节点查询
                Function<Category, TreeNodeQueryByComposite> buildNodeQuery =
                        cat -> TreeNodeQueryByComposite.builder()
                                .compositeIdentifier(CompositeIdentifier.builder().groupId(0L)
                                        .type(GroupBusinessTypeEnum.CATE.getValue())
                                        .id(cat.getCateId()).build())
                                .build();

                // 2.2 树节点转换器
                Function<TreeNode, SeriesTreeNode> nodeConverter = treeNode -> {
                    SeriesTreeNode node = new SeriesTreeNode();
                    node.setSeriesCategoryId((Long) category.getCompositeIdentifiers().stream()
                            .findFirst().map(CompositeIdentifier::id).orElse(null));
                    node.setNodeId(treeNode.getNodeId());
                    node.setParentId(treeNode.getParentId());
                    node.setStatus(treeNode.getStatus());
                    node.setSort(treeNode.getSort());
                    node.setName(treeNode.getName());
                    return node;
                };

                return this.actionQueryDispatcher.process(Mono.just(buildNodeQuery.apply(category)),
                        TreeNodeQueryByComposite.class, TreeNode.class).map(nodeConverter);
            };

            // 4. 资源处理流水线
            Function<List<SeriesTreeNode>, Mono<List<SeriesTreeNodeDto>>> processResources =
                    roots -> Flux.fromIterable(roots).flatMap(node -> {
                        SeriesTreeNodeDto dto =
                                this.converter.convert(node, SeriesTreeNodeDto.class);

                        // 获取队伍类型
                        Mono<Integer> teamTypeMono = Mono.justOrEmpty(node.getSeriesCategoryId())
                                .flatMap(seriesCategoryId -> seriesCategoryRootQueryRepository
                                        .getById(SeriesCategoryIdentifier.builder().seriesCategoryId(seriesCategoryId).build())
                                        .map(SeriesCategoryEntity::getTeamType))
                                .doOnNext(dto::setTeamType)
                                .thenReturn(1);

                        // 4.1 构建资源查询
                        Mono<BusinessResBusinessQuery> buildResQuery =
                                Mono.just(BusinessResBusinessQuery.builder()
                                        .businessGroupTypeEnum(BusinessGroupTypeEnum.SERIES)
                                        .businessId(node.getSeriesCategoryId()).build());

                        // 并行执行队伍类型查询和资源查询
                        Mono<List<BusinessResDto>> resourcesMono = actionQueryManyDispatcher
                                .executeQuery(buildResQuery, BusinessResBusinessQuery.class,
                                        BusinessResDto.class)
                                .collect(Collectors.toList());

                        // 等待两个查询都完成，然后设置资源信息
                        return Mono.zip(teamTypeMono, resourcesMono)
                                .map(tuple -> {
                                    List<BusinessResDto> resources = tuple.getT2();

                                    dto.setSeriesPicSet(resources.stream()
                                            .filter(tmp -> BusinessResTypeEnum.SERIES_PICTURE
                                                    .getCode().equals(tmp.getBusinessResType()))
                                            .collect(Collectors.toSet()));
                                    dto.setGiftPicSet(resources.stream()
                                            .filter(tmp -> BusinessResTypeEnum.SERIES_GIFT_PICTURE
                                                    .getCode().equals(tmp.getBusinessResType()))
                                            .collect(Collectors.toSet()));
                                    return dto;
                                });
                    }).collectList();

            // 组合函数式处理链
            return this.actionQueryManyDispatcher
                    .executeQuery(Mono.just(buildCateQuery.apply(req)), CateListByGroupQuery.class,
                            Category.class)
                    .flatMap(processCategory).collectList().flatMap(processResources)
                    .flatMap(this::getTree);
        }));
    }

    private Mono<List<SeriesTreeNodeDto>> getTree(List<SeriesTreeNodeDto> treeNodes) {
        return Mono.fromCallable(() -> {
            // 构建 Map，确保引用一致
            Map<Long, SeriesTreeNodeDto> nodeMap = new HashMap<>();
            for (SeriesTreeNodeDto node : treeNodes) {
                nodeMap.put(node.getNodeId(), node); // 保持同一个实例
            }

            // 存储根节点
            List<SeriesTreeNodeDto> roots = new ArrayList<>();

            // 遍历节点集合，组织树结构
            for (SeriesTreeNodeDto node : treeNodes) {
                Long parentId = node.getParentId();
                if (parentId == null || parentId == 0) {
                    // 根节点
                    roots.add(node);
                } else {
                    // 找到父节点并添加子节点
                    SeriesTreeNodeDto parent = nodeMap.get(parentId);
                    if (parent != null) {
                        if (parent.getChildrenDto() == null) {
                            parent.setChildrenDto(new ArrayList<>());
                        }
                        parent.getChildrenDto().add(node);
                    }
                }
            }
            return roots;
        });
    }
}
