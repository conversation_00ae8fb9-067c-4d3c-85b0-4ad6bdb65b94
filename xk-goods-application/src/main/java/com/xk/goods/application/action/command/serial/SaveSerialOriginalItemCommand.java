package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.application.dto.serial.SerialGroupOriginalItemTemplate;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupTeamReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialGroupOriginalItemTemplate.class, convertGenerate = false),
        @AutoMapper(target = SerialGroupTeamReqDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = SerialOriginalItemEntity.class, reverseConvertGenerate = false)})
public class SaveSerialOriginalItemCommand extends AbstractActionCommand {

    private Long userId;

    /**
     * 卡密条目id
     */
    private Long serialItemId;

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 系列id
     */
    private Long seriesCategoryId;

    /**
     * 系列名称
     */
    private String categoryName;

    /**
     * 球员中文名
     */
    private String memberCnName;

    /**
     * 球员英文名
     */
    private String memberEnName;

    /**
     * 球队名
     */
    private String teamName;

    /**
     * 卡密组队伍类型
     */
    private Integer teamType;

    /**
     * 卡种
     */
    private String cardType;

    /**
     * 卡种编号
     */
    private String cardTypeNo;

    /**
     * 限编
     */
    private String limitEdition;

    /**
     * 特效
     */
    private String color;
}
