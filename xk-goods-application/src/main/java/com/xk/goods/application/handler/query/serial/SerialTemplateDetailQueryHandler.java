package com.xk.goods.application.handler.query.serial;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.goods.application.action.query.serial.SerialTemplateDetailQuery;
import com.xk.goods.domain.repository.serial.SerialTemplateRootQueryRepository;
import com.xk.goods.interfaces.dto.res.serial.SerialTemplateDetailResDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SerialTemplateDetailQueryHandler implements IActionQueryHandler<SerialTemplateDetailQuery, SerialTemplateDetailResDto> {

    private final SerialTemplateRootQueryRepository serialTemplateRootQueryRepository;

    private final Converter converter;

    @Override
    public Mono<SerialTemplateDetailResDto> execute(Mono<SerialTemplateDetailQuery> queryMono) {
        return queryMono.flatMap(query ->
                serialTemplateRootQueryRepository.getSerialTemplateDetailById(LongIdentifier.builder().id(query.getSerialTemplateId()).build())
                        .flatMap(entity -> {
                            SerialTemplateDetailResDto resDto = new SerialTemplateDetailResDto();
                            BeanUtils.copyProperties(entity, resDto);
                            resDto.setSerialTemplates(Arrays.stream(entity.getSerialTemplate().split(";")).toList());
                            return Mono.just(resDto);
                        })
        );
    }
}
