package com.xk.goods.application.action.command.series;

import java.util.Date;
import java.util.List;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.business.id.BusinessResIdentifier;
import com.xk.goods.domain.model.series.entity.SeriesCategoryEntity;
import com.xk.goods.interfaces.dto.req.series.SeriesCategoryReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SeriesCategoryReqDto.class),
        @AutoMapper(target = SeriesCategoryEntity.class),})
public class CreateSeriesCategoryCommand extends AbstractActionCommand {

    private Long seriesCategoryId;

    /**
     * 球队类型
     */
    private Integer teamType;

    /**
     * 父id,不支持更新
     */
    private Long parentId;

    /**
     * 类别名称。 用于表示当前类别的名称。
     */
    @AutoMapping(targetClass = SeriesCategoryEntity.class, source = "name", target = "categoryName")
    private String name;

    /**
     * 排序字段，用于类别的排序。
     */
    private Integer sort;

    /**
     * 类别状态，通常用整数值表示状态码。
     */
    private Integer status;

    /**
     * 业务类型标识符，用于区分不同的业务类型。
     */
    private Integer busiType;

    /**
     * 队员图片资源映射：选队图片
     */
    private List<BusinessResIdentifier> businessResIdentifierList;

    /**
     * 队员图片资源映射：赠品图片
     */
    private List<BusinessResIdentifier> giftResIdentifierList;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private Long updateId;
}
