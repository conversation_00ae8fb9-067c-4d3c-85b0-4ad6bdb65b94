package com.xk.goods.application.handler.query.goods;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.domain.service.cate.CateRootService;
import com.xk.domain.service.tree.TreeRootService;
import com.xk.goods.application.action.query.business.BusinessResIdentifierQuery;
import com.xk.goods.application.action.query.goods.MallByIdQuery;
import com.xk.goods.domain.model.category.entity.GoodsCategoryEntity;
import com.xk.goods.domain.model.goods.GoodsRoot;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.price.entity.PriceItemEntity;
import com.xk.goods.domain.model.price.id.PriceIdentifier;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.service.category.GoodsCategoryRootService;
import com.xk.goods.domain.service.goods.GoodsRootService;
import com.xk.goods.domain.service.price.PriceRootService;
import com.xk.goods.domain.service.specification.SpecificationRootService;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.price.PriceTypeEnum;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;
import com.xk.goods.interfaces.dto.res.goods.GoodsCategoryDto;
import com.xk.goods.interfaces.dto.res.goods.GoodsSearchMallResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class MallByIdQueryHandler
        implements IActionQueryHandler<MallByIdQuery, GoodsSearchMallResDto> {

    private final CateRootService cateRootService;
    private final TreeRootService treeRootService;
    private final GoodsCategoryRootService goodsCategoryRootService;
    private final GoodsRootService goodsRootService;
    private final SpecificationRootService specificationRootService;
    private final PriceRootService priceRootService;
    private final Converter converter;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public Mono<GoodsSearchMallResDto> execute(Mono<MallByIdQuery> query) {
        return query.flatMap(dto -> {
            // 1. 转换商品ID
            GoodsIdentifier goodsId = GoodsIdentifierConvertor.map(dto.getGoodsId());

            // 2. 异步并行获取商品Root和规格列表（减少串行等待）
            Mono<GoodsRoot> goodsRootMono = goodsRootService.getRoot(goodsId);
            Mono<List<SpecificationRoot>> specsMono =
                    specificationRootService.searchByGoodsId(goodsId);

            return Mono.zip(goodsRootMono, specsMono).flatMap(tuple -> {
                // 3. 转换基础DTO
                GoodsSearchMallResDto result =
                        converter.convert(tuple.getT1(), GoodsSearchMallResDto.class);

                Mono<Void> setPicture = Flux.fromIterable(tuple.getT1().getResIdentifierList())
                        .flatMap(res -> this.queryDispatcher.executeQuery(Mono.just(res),
                                BusinessResIdentifierQuery.class, BusinessResDto.class))
                        .collectList().doOnNext(result::setProductPicList).then();

                Mono<Void> setAmount = Mono.justOrEmpty(tuple.getT2())
                        .filter(list -> !list.isEmpty()).flatMap(list -> {
                            PriceIdentifier priceId =
                                    list.getFirst().getSpecificationEntity().getPriceIdentifier();
                            return priceRootService.getRoot(priceId).doOnNext(priceRoot -> {
                                List<PriceItemEntity> itemList = priceRoot.getPriceItemEntityList();
                                Optional<PriceItemEntity> salePrice = itemList.stream().filter(
                                        v -> PriceTypeEnum.SALE_PRICE.equals(v.getPriceType()))
                                        .findFirst();
                                Optional<PriceItemEntity> costPrice = itemList.stream().filter(
                                        v -> PriceTypeEnum.COST_PRICE.equals(v.getPriceType()))
                                        .findFirst();
                                salePrice.ifPresent(priceItemEntity -> result
                                        .setAmount(priceItemEntity.getAmount()));
                                costPrice.ifPresent(priceItemEntity -> result
                                        .setCostAmount(priceItemEntity.getAmount()));
                            }).then();
                        });

                Mono<Void> setCategory =
                        Mono.justOrEmpty(tuple.getT1().getGoodsCategoryIdentifierList())
                                .flatMapMany(Flux::fromIterable)
                                .flatMap(goodsCategoryRootService::getRoot).flatMap(category -> {
                                    GoodsCategoryEntity entity = category.getGoodsCategoryEntity();
                                    CompositeIdentifier identifier = CompositeIdentifier.builder()
                                            .id(entity.getGoodsCategoryId())
                                            .type(GroupBusinessTypeEnum.GOODS.getValue())
                                            .groupId(GoodsTypeEnum.MALL_PRODUCT.getCode()).build();
                                    return cateRootService.findCateByBusinessId(identifier);
                                }).flatMap(category -> {
                                    CompositeIdentifier identifier =
                                            CompositeIdentifier.builder().id(category.getCateId())
                                                    .type(GroupBusinessTypeEnum.CATE.getValue())
                                                    .groupId(0L).build();
                                    return treeRootService.searchAllParentByBusinessId(identifier);
                                }).collectList().doOnNext(treeNodes -> {
                                    List<GoodsCategoryDto> list = treeNodes.stream()
                                            .map(treeNode -> GoodsCategoryDto.builder()
                                                    .categoryName(treeNode.getName())
                                                    .nodeId(treeNode.getNodeId())
                                                    .isRoot(treeNode.getParentId() == null
                                                            || treeNode.getParentId() == 0)
                                                    .build())
                                            .toList();
                                    result.setGoodsCategoryDtoList(list);
                                }).then();

                // 4. 获取首个规格的价格（如果存在）
                return setPicture.then(setAmount).then(setCategory).thenReturn(result);
            });
        });
    }
}
