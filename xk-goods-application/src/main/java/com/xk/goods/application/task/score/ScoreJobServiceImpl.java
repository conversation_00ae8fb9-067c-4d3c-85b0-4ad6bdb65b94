package com.xk.goods.application.task.score;


import java.util.Optional;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.xk.goods.domain.event.score.UpdateScoreRuleEvent;
import com.xk.goods.domain.model.score.valobj.ScoreRuleVersionValObj;
import com.xk.goods.domain.repository.score.ScoreRuleRootQueryRepository;
import com.xk.goods.domain.repository.score.ScoreRuleRootRepository;
import com.xk.goods.domain.service.score.ScoreRuleRootService;
import com.xk.goods.enums.score.ScoreRuleTypeEnum;
import com.xk.goods.interfaces.task.score.ScoreJobService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScoreJobServiceImpl implements ScoreJobService {

    private final ScoreRuleRootQueryRepository queryRepository;
    private final ScoreRuleRootRepository scoreRuleRootRepository;
    private final ScoreRuleRootService scoreRuleRootService;
    private final LockRootService lockRootService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> updateScoreRule() {
        return queryRepository.findAllRuleTask().hasElement().flatMap(has -> {
            if (Boolean.FALSE.equals(has)) {
                return Mono.empty();
            }
            Mono<Boolean> getLock =
                    Mono.defer(() -> lockRootService.acquireTransactionObjectLockMono(
                            ZookeeperLockObject.LOCKS_GOODS, "updateRule"));

            Function<Boolean, Flux<UpdateScoreRuleEvent>> buildEvent = v -> {
                if (Boolean.FALSE.equals(v)) {
                    return Flux.empty();
                }
                return scoreRuleRootService.searchScoreRuleUpdate().collectList()
                        .flatMapMany(list -> {

                            // 如果基础的修改了只发基础事件
                            Optional<ScoreRuleVersionValObj> first = list.stream().filter(
                                    j -> ScoreRuleTypeEnum.BASIC.equals(j.getScoreRuleType()))
                                    .findFirst();
                            if (first.isPresent()) {
                                ScoreRuleVersionValObj valObj = first.get();
                                UpdateScoreRuleEvent build = UpdateScoreRuleEvent.builder()
                                        .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                UpdateScoreRuleEvent.class))
                                        .scoreRuleType(valObj.getScoreRuleType())
                                        .ruleVersion(valObj.getRuleVersion()).build();
                                return Flux.just(build);
                            }

                            return Flux.fromIterable(list)
                                    .map(valObj -> UpdateScoreRuleEvent.builder()
                                            .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                    UpdateScoreRuleEvent.class))
                                            .scoreRuleType(valObj.getScoreRuleType())
                                            .ruleVersion(valObj.getRuleVersion()).build());
                        });
            };

            Function<UpdateScoreRuleEvent, Mono<Boolean>> publishEvent = event -> {
                EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(false).build();
                return eventRootService.publisheByMono(eventRoot).doOnSuccess(
                        v -> log.info("UpdateScoreRuleEvent事件发布完成: {}", event.getScoreRuleType()));
            };

            return getLock.flatMapMany(buildEvent).flatMap(publishEvent)
                    .then(scoreRuleRootRepository.clearTask()).then();
        });
    }
}
