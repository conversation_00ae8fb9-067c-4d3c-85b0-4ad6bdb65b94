package com.xk.goods.application.handler.command.color;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.xk.goods.application.action.command.color.UpdateSerialItemColorCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.model.color.id.SerialItemColorIdentifier;
import com.xk.goods.domain.service.color.SerialItemColorRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 更新卡密特效命令处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateSerialItemColorCommandHandler
        implements IActionCommandHandler<UpdateSerialItemColorCommand, Void> {

    private final SerialItemColorRootService serialItemColorRootService;

    @Override
    public Mono<Void> execute(Mono<UpdateSerialItemColorCommand> mono) {
        return mono.flatMap(command -> serialItemColorRootService
                .getRoot(SerialItemColorIdentifier.builder()
                        .serialItemColorId(command.getSerialItemColorId()).build())
                .switchIfEmpty(Mono.error(
                        new XkGoodsApplicationException(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                .map(command::getRoot)
                .flatMap(root -> serialItemColorRootService.updateRoot(Mono.just(root))));
    }
}
