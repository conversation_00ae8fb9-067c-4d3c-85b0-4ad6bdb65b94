package com.xk.goods.application.handler.query.score;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.goods.application.action.query.score.ScoreSearchPagerQuery;
import com.xk.goods.application.dto.score.ScoreAppDto;
import com.xk.goods.domain.repository.score.ScoreRootQueryRepository;
import com.xk.goods.interfaces.dto.res.score.ScoreResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class ScoreSearchPagerQueryHandler
        implements IActionQueryHandler<ScoreSearchPagerQuery, Pagination> {

    private final ScoreRootQueryRepository scoreRootQueryRepository;
    private final SelectorRootService selectorRootService;
    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<ScoreSearchPagerQuery> mono) {
        return mono.flatMap(query -> {
            // 创建分页参数
            Pagination pagination = new Pagination();
            pagination.setLimit(query.getLimit());
            pagination.setOffset(query.getOffset());
            pagination.setCriteria(query.getGoodsId());

            return scoreRootQueryRepository.searchPagerGoodsId(pagination)
                    .flatMap(scoreRootQueryRepository::findGoodsScore)
                    .flatMap(entity -> selectorRootService.getGoodsObject(entity.getGoodsId())
                            .map(goodsObj -> {
                                ScoreAppDto appDto = converter.convert(entity, ScoreAppDto.class);
                                ScoreResDto resDto = converter.convert(appDto, ScoreResDto.class);
                                resDto.setGoodsName(goodsObj.getGoodsInfo().getGoodsName());
                                return resDto;
                            }))
                    .collectList().flatMap(resDtoList -> {
                        pagination.setRecords(resDtoList);
                        if (query.getGoodsId() != null) {
                            pagination.setTotalCount(resDtoList.size());
                            return Mono.just(pagination);
                        }
                        return scoreRootQueryRepository.countAll().map(count -> {
                            pagination.setTotalCount(Math.toIntExact(count));
                            return pagination;
                        });
                    });
        });
    }
}
