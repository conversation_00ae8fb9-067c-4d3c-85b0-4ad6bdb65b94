package com.xk.goods.application.action.command.stock;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.stock.entity.StockEntity;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = StockEntity.class, reverseConvertGenerate = false)})
public class DeleteStockCommand extends AbstractActionCommand {

    /**
     * 库存id
     */
    private Long stockId;
}
