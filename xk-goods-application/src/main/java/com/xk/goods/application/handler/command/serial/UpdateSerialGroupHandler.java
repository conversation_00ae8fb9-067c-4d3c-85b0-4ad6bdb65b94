package com.xk.goods.application.handler.command.serial;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.serial.UpdateSerialGroupCommand;
import com.xk.goods.domain.model.serial.SerialGroupRoot;
import com.xk.goods.domain.model.serial.entity.SerialGroupEntity;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.repository.serial.SerialGroupRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateSerialGroupHandler implements IActionCommandHandler<UpdateSerialGroupCommand, Void> {

    private final SerialGroupRootRepository serialGroupRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdateSerialGroupCommand> mono) {
        return mono.flatMap(command -> {
            SerialGroupEntity entity = new SerialGroupEntity();
            converter.convert(command, entity);
            entity.setUpdateId(command.getUserId());
            entity.setUpdateTime(new Date());
            SerialGroupIdentifier identifier = SerialGroupIdentifier.builder().serialGroupId(entity.getSerialGroupId()).build();
            return serialGroupRootRepository.update(SerialGroupRoot.builder()
            .identifier(identifier).serialGroupEntity(entity).build());
        });
    }
}
