package com.xk.goods.application.action.query.goods;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdReqDto;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdSearchReqDto;
import com.xk.search.interfaces.dto.rsp.goods.GoodsIdRspDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = GoodsIdReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsIdSearchReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsIdRspDto.class, convertGenerate = false),
})
public class CollectibleByIdQuery implements IActionQuery {
    /**
     * 主键
     */
    private Long goodsId;
}
