package com.xk.goods.application.handler.event.goods;

import java.util.Objects;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.event.stock.UpdateRemainStockEvent;
import com.xk.domain.model.stock.id.StockIdentifier;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.domain.model.specification.entity.SpecificationEntity;
import com.xk.goods.domain.model.specification.id.SpecificationIdentifier;
import com.xk.goods.domain.service.specification.SpecificationRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateRemainStockEventHandler extends AbstractEventVerticle<UpdateRemainStockEvent> {

    private final StockRootService stockRootService;
    private final SpecificationRootService specificationRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<UpdateRemainStockEvent> mono) {
        return mono.flatMap(event -> {
            if (Objects.equals(event.getStockBusinessType(), StockBusinessTypeEnum.SPECIFICATION)) {
                return processSpecificationStock(event);
            }

            if (Objects.equals(event.getStockBusinessType(), StockBusinessTypeEnum.GOODS)) {
                return processGoodsStock(event);
            }

            return Mono.empty();
        }).onErrorResume(e -> {
            log.error("UpdateRemainStockEvent事件处理失败: {}", e.getMessage(), e);
            return Mono.empty();
        }).then();
    }

    private Mono<Void> processSpecificationStock(UpdateRemainStockEvent event) {
        Mono<SpecificationEntity> getSpecEntity = stockRootService
                .getRootNoDeduction(StockIdentifier.builder().stockId(event.getStockId()).build())
                .flatMap(
                        root -> specificationRootService.getEntity(SpecificationIdentifier.builder()
                                .specificationId(
                                        root.getStockSpecificationEntity().getSpecificationId())
                                .build()));
        Function<SpecificationEntity, Mono<Void>> deductionGoodsStock = spec -> stockRootService
                .deductionStock(
                        StringIdentifier.builder()
                                .id(spec.getGoodsIdentifier().getGoodsId().toString()).build(),
                        event.getCount().intValue(), StockBusinessTypeEnum.GOODS)
                .flatMap(success -> {
                    if (Boolean.FALSE.equals(success)) {
                        log.error("更新商品缓存失败,goodsId:{}", spec.getGoodsIdentifier().getGoodsId());
                    }
                    return Mono.empty();
                });
        Mono<Void> doRefresh = stockRootService.refreshStockCache(
                StockIdentifier.builder().stockId(event.getStockId()).build(),
                event.getUpdateTime(), StockBusinessTypeEnum.SPECIFICATION);
        return getSpecEntity.flatMap(deductionGoodsStock).then(doRefresh);
    }

    private Mono<Void> processGoodsStock(UpdateRemainStockEvent event) {
        return stockRootService.refreshStockCache(
                StockIdentifier.builder().stockId(event.getStockId()).build(),
                event.getUpdateTime(), StockBusinessTypeEnum.GOODS);
    }
}
