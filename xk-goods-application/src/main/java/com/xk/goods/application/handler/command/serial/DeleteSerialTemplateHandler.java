package com.xk.goods.application.handler.command.serial;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.serial.DeleteSerialTemplateCommand;
import com.xk.goods.domain.model.template.SerialTemplateRoot;
import com.xk.goods.domain.model.template.entity.SerialTemplateEntity;
import com.xk.goods.domain.model.template.id.SerialTemplateIdentifier;
import com.xk.goods.domain.repository.serial.SerialTemplateRootRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteSerialTemplateHandler implements IActionCommandHandler<DeleteSerialTemplateCommand, Void> {

    private final SerialTemplateRootRepository serialTemplateRootRepository;

    @Override
    public Mono<Void> execute(Mono<DeleteSerialTemplateCommand> commandMono) {
        return commandMono.flatMap(command -> {
            SerialTemplateEntity entity = SerialTemplateEntity.builder()
                    .serialTemplateId(command.getSerialTemplateId())
                    .updateId(command.getUserId())
                    .updateTime(new Date()).build();
            SerialTemplateIdentifier identifier = SerialTemplateIdentifier.builder().serialTemplateId(command.getSerialTemplateId()).build();
            return serialTemplateRootRepository.remove(SerialTemplateRoot.builder().identifier(identifier)
                    .serialTemplateEntity(entity).build());
        });
    }
}
