package com.xk.goods.application.dto.merchant;

import com.xk.goods.domain.model.merchant.valobj.StatusCountValObj;
import com.xk.goods.interfaces.dto.res.goods.MerchantStatusCountResDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = MerchantStatusCountResDto.class, reverseConvertGenerate = false),
        @AutoMapper(target = StatusCountValObj.class, convertGenerate = false),})
public class StatusCountDto {

    /**
     * 已审核数量
     */
    private Integer auditedCount;

    /**
     * 审核中数量
     */
    private Integer auditingCount;

    /**
     * 未通过数量
     */
    private Integer unPassCount;

    /**
     * 已完成数量
     */
    private Integer finishCount;

    /**
     * 售罄数量
     */
    private Integer soldOutCount;

    /**
     * 未售罄/已退款数量
     */
    private Integer notSoldOutOrRefundCount;

    /**
     * 回收站数量
     */
    private Integer recycleCount;
}
