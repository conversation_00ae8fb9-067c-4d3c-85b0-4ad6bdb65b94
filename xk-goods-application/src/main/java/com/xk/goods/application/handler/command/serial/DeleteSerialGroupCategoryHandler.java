package com.xk.goods.application.handler.command.serial;

import java.util.Date;
import java.util.Set;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.enums.commons.DeleteFlagEnum;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.model.category.CateRoot;
import com.xk.domain.model.category.Category;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.domain.service.cate.CateRootService;
import com.xk.goods.application.action.command.serial.DeleteSerialGroupCategoryCommand;
import com.xk.goods.application.commons.XkGoodsApplicationErrorEnum;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.serial.DeleteSerialGroupCategoryCateEvent;
import com.xk.goods.domain.event.serial.SaveSerialGroupCategoryCateEvent;
import com.xk.goods.domain.model.serial.SerialGroupRoot;
import com.xk.goods.domain.model.serial.entity.SerialGroupCategoryEntity;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.repository.serial.SerialGroupCategoryRootRepository;
import com.xk.goods.domain.repository.serial.SerialGroupRootQueryRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteSerialGroupCategoryHandler implements IActionCommandHandler<DeleteSerialGroupCategoryCommand, Void> {

    private final SerialGroupCategoryRootRepository serialGroupCategoryRootRepository;

    private final SerialGroupRootQueryRepository serialGroupRootQueryRepository;

    private final Converter converter;

    private EventRootService eventRootService;

    private CateRootService cateRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Autowired
    @Lazy
    public void setCatRootService(CateRootService cateRootService) {
        this.cateRootService = cateRootService;
    }


    @Override
    public Mono<Void> execute(Mono<DeleteSerialGroupCategoryCommand> mono) {

        // 1、如果是一级，校验是否存在子级
        Function<DeleteSerialGroupCategoryCommand, Mono<Boolean>> checkChild = command -> {
            if (command.getParentId() != 0) {
                return Mono.empty();
            }

            CompositeIdentifier identifier = CompositeIdentifier.builder().groupId(1L)
                    .type(GroupBusinessTypeEnum.SERIAL.getValue()).id(command.getSerialGroupCategoryId()).build();
            CateRoot cateRoot = CateRoot.builder()
                    .identifier(LongIdentifier.builder().id(command.getSerialGroupCategoryId()).build())
                    .category(Category.builder().compositeIdentifiers(Set.of(identifier)).build()).build();
            // 添加日志记录（可选）
            return cateRootService.checkDeletedCateBusiness(cateRoot)
                    .doOnNext(result -> log.debug("Category {} child check result: {}",
                            command.getSerialGroupCategoryId(), result));
        };

        // 2、如果是二级，查询所有的卡密，是否存在非禁用卡密
        Function<DeleteSerialGroupCategoryCommand, Mono<Void>> checkGroupNum = command -> {
            if (command.getParentId() == 0) {
                return Mono.empty();
            }
            return serialGroupRootQueryRepository.countSerialGroupByCategoryId(command.getSerialGroupCategoryId()).flatMap(count -> {
                if (count > 0) {
                    return Mono.error(
                            new XkGoodsApplicationException(XkGoodsApplicationErrorEnum.CATEGORY_HAS_GROUP));
                }
                return Mono.empty();
            }).then();
        };


        // 3、删除
        Function<DeleteSerialGroupCategoryCommand, Mono<Void>> deleteCommand = command -> {
            SerialGroupCategoryEntity entity = this.converter.convert(command, SerialGroupCategoryEntity.class);
            entity.setDeleted(DeleteFlagEnum.DELETED.getValue());
            entity.setUpdateId(command.getUserId());
            entity.setUpdateTime(new Date());
            SerialGroupIdentifier serialGroupIdentifier = SerialGroupIdentifier.builder()
                    .serialGroupId(entity.getSerialGroupCategoryId()).build();
            return serialGroupCategoryRootRepository.update(SerialGroupRoot.builder().identifier(serialGroupIdentifier)
                    .serialGroupCategoryEntity(entity).build());
        };

        // 4、发事件
        Function<DeleteSerialGroupCategoryCommand, Mono<Void>> publishEvent = command -> {
            EventRoot eventRoot = EventRoot.builder().domainEvent(DeleteSerialGroupCategoryCateEvent.builder()
                    .identifier(EventRoot.getCommonsDomainEventIdentifier(SaveSerialGroupCategoryCateEvent.class))
                    .serialGroupCategoryId(command.getSerialGroupCategoryId())
                    .groupBusinessType(GroupBusinessTypeEnum.SERIAL.getValue()).build()).build();

            return eventRootService.publisheByMono(eventRoot).then()
                    .doOnSuccess(v -> log.info("DeleteSerialGroupCategoryCateEvent事件发布完成: {}", command.getSerialGroupCategoryId()));
        };
        return mono.flatMap(command -> checkChild.apply(command)
                .then(checkGroupNum.apply(command))
                .then(deleteCommand.apply(command))
                .then(publishEvent.apply(command).then()));
    }
}
