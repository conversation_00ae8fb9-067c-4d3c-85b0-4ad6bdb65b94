package com.xk.goods.application.handler.event.series;

import static com.xk.goods.application.commons.XkGoodsApplicationErrorEnum.APPLICATION_ERROR;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.application.action.command.business.CreateBusinessResCommand;
import com.xk.goods.application.action.command.business.DeleteBusinessResCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.domain.event.series.UpdateSeriesCateEvent;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.service.business.BusinessResRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateSeriesCateEventHandler extends AbstractEventVerticle<UpdateSeriesCateEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final BusinessResRootService businessResRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<UpdateSeriesCateEvent> mono) {
        return mono.flatMap(event -> {
            BaseBusinessRes firstRes = event.getBaseBusinessResList().getFirst();
            return Mono.justOrEmpty(firstRes)
                    .flatMap(first -> businessResRootService
                            .searchByBusinessGroup(BusinessResEntity.builder()
                                    .businessGroupType(first.getBusinessGroupType())
                                    .businessId(first.getBusinessId()).build())
                            .flatMap(res -> commandDispatcher.executeCommand(Mono.just(res),
                                    DeleteBusinessResCommand.class, Void.class))
                            .then())
                    // 创建阶段流程
                    .then(Flux.fromIterable(event.getBaseBusinessResList()).index().flatMap(
                            (tuple2 -> commandDispatcher.executeCommand(Mono.just(tuple2.getT2()),
                                    CreateBusinessResCommand.class, command -> {
                                        command.setSort(Math.toIntExact(tuple2.getT1()));
                                        return command;
                                    })
                                    .doOnSuccess(v -> log.info("UpdateSeriesCateEvent已创建业务资源: {}",
                                            tuple2.getT2().getResId()))))
                            .then())
                    .onErrorResume(e -> {
                        log.error("处理UpdateSeriesCateEvent事件失败: {}", event, e);
                        return Mono.error(new XkGoodsApplicationException(APPLICATION_ERROR));
                    }).then();
        });
    }
}
