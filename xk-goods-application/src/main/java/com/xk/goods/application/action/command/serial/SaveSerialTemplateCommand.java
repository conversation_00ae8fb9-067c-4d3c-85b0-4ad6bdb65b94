package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.template.entity.SerialTemplateEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialTemplateSaveReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialTemplateSaveReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialTemplateEntity.class, reverseConvertGenerate = false)})
public class SaveSerialTemplateCommand extends AbstractActionCommand {

    private Long userId;

    /**
     * 卡密模板id
     */
    private Long serialTemplateId;

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 卡密模板名称
     */
    private String name;

    /**
     * 卡密数量
     */
    private Integer num;

    /**
     * 卡密模板：卡密条目通过逗号拼接；不同组合通过分号拼接；
     */
    private List<String> serialTemplates;
}
