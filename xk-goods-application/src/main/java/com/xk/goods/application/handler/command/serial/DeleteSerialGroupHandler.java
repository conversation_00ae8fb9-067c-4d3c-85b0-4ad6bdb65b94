package com.xk.goods.application.handler.command.serial;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.serial.DeleteSerialGroupCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteSerialGroupHandler implements IActionCommandHandler<DeleteSerialGroupCommand, Void> {

    @Override
    public Mono<Void> execute(Mono<DeleteSerialGroupCommand> mono) {
        return null;
    }
}
