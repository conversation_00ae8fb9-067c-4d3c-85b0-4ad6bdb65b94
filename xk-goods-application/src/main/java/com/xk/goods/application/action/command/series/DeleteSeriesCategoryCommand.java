package com.xk.goods.application.action.command.series;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.series.entity.SeriesCategoryEntity;
import com.xk.goods.interfaces.dto.req.series.SeriesCategoryIdentifierDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SeriesCategoryIdentifierDto.class),
        @AutoMapper(target = SeriesCategoryEntity.class),})
public class DeleteSeriesCategoryCommand extends AbstractActionCommand {

    private Long seriesCategoryId;

    /**
     * 业务类型标识符，用于区分不同的业务类型。
     */
    private Integer busiType;
}
