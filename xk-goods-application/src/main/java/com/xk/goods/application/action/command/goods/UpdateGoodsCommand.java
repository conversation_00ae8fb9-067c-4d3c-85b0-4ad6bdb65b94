package com.xk.goods.application.action.command.goods;

import static com.xk.application.commons.CommonUtil.setIfNotNull;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.common.UpdateValObj;
import com.xk.goods.domain.model.business.id.BusinessResIdentifier;
import com.xk.goods.domain.model.category.id.GoodsCategoryIdentifier;
import com.xk.goods.domain.model.goods.GoodsRoot;
import com.xk.goods.domain.model.goods.entity.GoodsEntity;
import com.xk.goods.domain.model.goods.valobj.GoodsListingValObj;
import com.xk.goods.domain.model.goods.valobj.GoodsNameValObj;
import com.xk.goods.domain.model.specification.id.SpecificationIdentifier;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.infrastructure.convertor.goods.GoodsTypeEnumConvertor;
import com.xk.goods.infrastructure.convertor.goods.ListingStatusEnumConvertor;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdBatchReqDto;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdListingReqDto;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdShowReqDto;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdStatusReqDto;
import com.xk.goods.interfaces.dto.req.goods.collectible.CollectibleCardReqDto;
import com.xk.goods.interfaces.dto.req.goods.mall.MallProductReqDto;
import com.xk.goods.interfaces.dto.req.goods.materials.MaterialsProductReqDto;
import com.xk.goods.interfaces.dto.req.goods.merchant.MerchantProductCreateReqDto;
import com.xk.goods.interfaces.dto.req.goods.merchant.MerchantProductUpdateReqDto;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = MaterialsProductReqDto.class, convertGenerate = false),
        @AutoMapper(target = MallProductReqDto.class, convertGenerate = false),
        @AutoMapper(target = MerchantProductCreateReqDto.class, convertGenerate = false),
        @AutoMapper(target = MerchantProductUpdateReqDto.class, convertGenerate = false),
        @AutoMapper(target = CollectibleCardReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsIdShowReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsIdListingReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsIdBatchReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsIdStatusReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsEntity.class,
                uses = {GoodsTypeEnumConvertor.class, BusinessTypeEnumConvertor.class,
                        CommonStatusEnumConvertor.class, ListingStatusEnumConvertor.class})})
public class UpdateGoodsCommand extends AbstractActionCommand {

    /**
     * 商品主键
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    @AutoMappings({
            @AutoMapping(targetClass = GoodsEntity.class, target = "goodsNameValObj.goodsName")})
    private String goodsName;

    /**
     * 上架状态枚举值
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.listingStatus")})
    private Integer listingStatus;

    /**
     * 计划上架开始时间
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.planUpTime")})
    private Date planUpTime;

    /**
     * 计划上架结束时间
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.planDownTime")})
    private Date planDownTime;

    /**
     * 实际上架时间
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.actualUpTime")})
    private Date actualUpTime;

    /**
     * 实际下架时间
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.actualDownTime")})
    private Date actualDownTime;

    /**
     * 商品描述
     */
    private String goodsDescribe;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 显示状态
     */
    private Integer showStatus;

    /**
     * 总库存
     */
    private Long totalStock;

    /**
     * 商品库存id
     */
    private Long stockId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 商品主图id
     */
    private List<Integer> pictureResIdList;

    /**
     * 详情图片
     */
    private List<Integer> detailResIdList;

    /**
     * 来源凭证图片
     */
    private List<Integer> sourceCredentialResIdList;

    /**
     * 介绍图片
     */
    private List<Integer> describeResIdList;

    /**
     * 商品类目id
     */
    private List<Long> goodsCategoryIdList;

    /**
     * 规格id
     */
    private List<Long> specificationIdList;

    /**
     * 赠品图片id
     */
    private List<Integer> giftResIdList;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 更新人ID
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class, source = "updateId",
            target = "updateValObj.updateId")})
    private Long updateId;

    /**
     * 更新时间
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class, source = "updateTime",
            target = "updateValObj.updateTime")})
    private Date updateTime;

    public GoodsRoot getRoot(GoodsRoot root) {
        GoodsEntity entity = root.getGoodsEntity();
        // 拼接规格名称值对象
        GoodsNameValObj goodsNameValObj = entity.getGoodsNameValObj();
        GoodsNameValObj nameValObj = GoodsNameValObj.builder()
                .goodsName(setIfNotNull(this.goodsName, goodsNameValObj.getGoodsName()))
                .defaultName(setIfNotNull(this.goodsName, goodsNameValObj.getDefaultName()))
                .build();
        GoodsListingValObj goodsListingValObj = entity.getGoodsListingValObj();
        GoodsListingValObj listingValObj = GoodsListingValObj.builder()
                .listingStatus(setIfNotNull(ListingStatusEnumConvertor.map(this.listingStatus),
                        goodsListingValObj.getListingStatus()))
                .autoListingStatus(goodsListingValObj.getAutoListingStatus())
                .actualDownTime(
                        setIfNotNull(actualDownTime, goodsListingValObj.getActualDownTime()))
                .actualUpTime(setIfNotNull(actualUpTime, goodsListingValObj.getActualUpTime()))
                // 如果原来有计划上架时间则不予修改
                .planDownTime(setIfNotNull(goodsListingValObj.getPlanDownTime(), planDownTime))
                .planUpTime(setIfNotNull(goodsListingValObj.getPlanUpTime(), planUpTime)).build();

        UpdateValObj updateValObj = UpdateValObj.builder()
                .updateTime(setIfNotNull(updateTime, entity.getUpdateValObj().getUpdateTime()))
                .updateId(setIfNotNull(updateId, entity.getUpdateValObj().getUpdateId())).build();

        GoodsEntity goodsEntity = GoodsEntity.builder().goodsId(entity.getGoodsId())
                .goodsNameValObj(nameValObj).goodsType(entity.getGoodsType())
                .showStatus(setIfNotNull(CommonStatusEnumConvertor.map(showStatus),
                        entity.getShowStatus()))
                .goodsListingValObj(listingValObj).businessType(entity.getBusinessType())
                .goodsSourceValObj(entity.getGoodsSourceValObj())
                .goodsDescribe(setIfNotNull(this.goodsDescribe, entity.getGoodsDescribe()))
                .unitPrice(setIfNotNull(this.unitPrice, entity.getUnitPrice()))
                .totalStock(setIfNotNull(this.totalStock, entity.getTotalStock()))
                .status(setIfNotNull(CommonStatusEnumConvertor.map(this.status),
                        entity.getStatus()))
                .deleted(entity.getDeleted())
                .stockId(setIfNotNull(this.stockId, entity.getStockId()))
                .sort(setIfNotNull(this.sort, entity.getSort()))
                .createValObj(entity.getCreateValObj()).updateValObj(updateValObj).build();

        GoodsRoot.GoodsRootBuilder builder =
                GoodsRoot.builder().identifier(root.getIdentifier()).goodsEntity(goodsEntity);

        populateResIdentifiers(root, builder);
        populateCategoryIdentifiers(root, builder);
        populateSpecificationIdentifiers(root, builder);
        return builder.build();
    }

    private void populateResIdentifiers(GoodsRoot root, GoodsRoot.GoodsRootBuilder builder) {
        List<BusinessResIdentifier> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pictureResIdList)) {
            List<BusinessResIdentifier> resIdentifierList = pictureResIdList.stream()
                    .map(id -> BusinessResIdentifier.builder()
                            .businessResType(BusinessResTypeEnum.PRODUCT_PICTURE)
                            .businessGroupType(BusinessGroupTypeEnum.GOODS).businessId(goodsId)
                            .resId(id).build())
                    .toList();
            list.addAll(resIdentifierList);
        }
        if (CollectionUtils.isNotEmpty(detailResIdList)) {
            List<BusinessResIdentifier> resIdentifierList = detailResIdList.stream()
                    .map(id -> BusinessResIdentifier.builder()
                            .businessResType(BusinessResTypeEnum.PRODUCT_DETAIL).businessId(goodsId)
                            .businessGroupType(BusinessGroupTypeEnum.GOODS).resId(id).build())
                    .toList();
            list.addAll(resIdentifierList);
        }
        if (CollectionUtils.isNotEmpty(describeResIdList)) {
            List<BusinessResIdentifier> resIdentifierList = describeResIdList.stream()
                    .map(id -> BusinessResIdentifier.builder()
                            .businessResType(BusinessResTypeEnum.PRODUCT_INTRODUCTION)
                            .businessGroupType(BusinessGroupTypeEnum.GOODS).businessId(goodsId)
                            .resId(id).build())
                    .toList();
            list.addAll(resIdentifierList);
        }
        if (CollectionUtils.isNotEmpty(sourceCredentialResIdList)) {
            List<BusinessResIdentifier> resIdentifierList = sourceCredentialResIdList.stream()
                    .map(id -> BusinessResIdentifier.builder()
                            .businessResType(BusinessResTypeEnum.PRODUCT_SOURCE).businessId(goodsId)
                            .businessGroupType(BusinessGroupTypeEnum.GOODS).resId(id).build())
                    .toList();
            list.addAll(resIdentifierList);
        }
        if (CollectionUtils.isNotEmpty(giftResIdList)) {
            List<BusinessResIdentifier> resIdentifierList = giftResIdList.stream()
                    .map(id -> BusinessResIdentifier.builder()
                            .businessResType(BusinessResTypeEnum.GIFT_PICTURE).businessId(goodsId)
                            .businessGroupType(BusinessGroupTypeEnum.GOODS).resId(id).build())
                    .toList();
            list.addAll(resIdentifierList);
        }
        if (CollectionUtils.isEmpty(list)) {
            list.addAll(root.getResIdentifierList());
        }
        builder.resIdentifierList(list);
    }

    private void populateCategoryIdentifiers(GoodsRoot root, GoodsRoot.GoodsRootBuilder builder) {
        if (CollectionUtils.isNotEmpty(goodsCategoryIdList)) {
            List<GoodsCategoryIdentifier> identifierList = goodsCategoryIdList.stream()
                    .map(id -> GoodsCategoryIdentifier.builder().goodsCategoryId(id).build())
                    .toList();
            builder.goodsCategoryIdentifierList(identifierList);
        } else {
            builder.goodsCategoryIdentifierList(root.getGoodsCategoryIdentifierList());
        }
    }

    private void populateSpecificationIdentifiers(GoodsRoot root,
            GoodsRoot.GoodsRootBuilder builder) {
        if (CollectionUtils.isNotEmpty(specificationIdList)) {
            List<SpecificationIdentifier> identifierList = specificationIdList.stream()
                    .map(id -> SpecificationIdentifier.builder().specificationId(id).build())
                    .toList();
            builder.specificationIdentifierList(identifierList);
        } else {
            builder.specificationIdentifierList(root.getSpecificationIdentifierList());
        }
    }
}
