package com.xk.goods.application.action.command.goods;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.goods.entity.GoodsEntity;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = GoodsIdReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsEntity.class, reverseConvertGenerate = false)})
public class DeleteGoodsCommand extends AbstractActionCommand {

    /**
     * 商品主键
     */
    private Long goodsId;
}
