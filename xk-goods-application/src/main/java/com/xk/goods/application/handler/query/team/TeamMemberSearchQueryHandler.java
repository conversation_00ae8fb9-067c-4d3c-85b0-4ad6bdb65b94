package com.xk.goods.application.handler.query.team;

import static com.xk.goods.enums.business.BusinessResTypeEnum.MEMBER_AVATAR;
import static com.xk.goods.enums.business.BusinessResTypeEnum.MEMBER_PICTURE;

import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.config.interfaces.query.res.SysResourceQueryService;
import com.xk.goods.application.action.query.business.BusinessResBusinessQuery;
import com.xk.goods.application.action.query.team.TeamMemberSearchQuery;
import com.xk.goods.application.commons.XkGoodsApplicationErrorEnum;
import com.xk.goods.application.dto.team.TeamMemberSearchListAppDto;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.repository.team.TeamMemberRootQueryRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;
import com.xk.goods.interfaces.dto.res.team.TeamMemberResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class TeamMemberSearchQueryHandler
        implements IActionQueryHandler<TeamMemberSearchQuery, Pagination> {

    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;
    private final TeamMemberRootQueryRepository queryRepository;
    private final SysResourceQueryService sysResourceQueryService;
    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<TeamMemberSearchQuery> mono) {
        return mono.flatMap(query -> {
            // 创建分页参数
            Pagination pagination = new Pagination();
            pagination.setLimit(query.getLimit());
            pagination.setOffset(query.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(query));

            return queryRepository.searchTeamMember(pagination).flatMap(entity -> {
                // 构建资源查询命令
                BusinessResBusinessQuery resQuery =
                        BusinessResBusinessQuery.builder().businessId(entity.getTeamMemberId())
                                .businessGroupTypeEnum(BusinessGroupTypeEnum.MEMBER).build();

                return queryManyDispatcher.executeQuery(Mono.just(resQuery),
                        BusinessResBusinessQuery.class, BusinessResDto.class).collectList()
                        .map(resources -> {
                            TeamMemberSearchListAppDto appDto =
                                    converter.convert(entity, TeamMemberSearchListAppDto.class);
                            TeamMemberResDto resDto =
                                    converter.convert(appDto, TeamMemberResDto.class);
                            resDto.setMemberPicSet(resources.stream()
                                    .filter(tmp -> MEMBER_PICTURE.getCode()
                                            .equals(tmp.getBusinessResType()))
                                    .collect(Collectors.toSet()));
                            resDto.setMemberAvatorSet(resources.stream().filter(
                                    tmp -> MEMBER_AVATAR.getCode().equals(tmp.getBusinessResType()))
                                    .collect(Collectors.toSet()));
                            return resDto;
                        });
            }).collectList().map(list -> {
                pagination.setRecords(list);
                return pagination;
            });
        }).onErrorResume(e -> {
            log.error("TeamMemberSearchQuery命令执行异常", e);
            return Mono.error(
                    new XkGoodsApplicationException(XkGoodsApplicationErrorEnum.APPLICATION_ERROR));
        });
    }
}
