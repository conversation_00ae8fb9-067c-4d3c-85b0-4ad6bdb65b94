package com.xk.goods.application.action.command.price;

import java.util.List;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.application.dto.price.PriceItemDto;
import com.xk.goods.domain.model.price.entity.PriceEntity;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.specification.SpecificationIdentifierConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = PriceEntity.class,
        uses = {SpecificationIdentifierConvertor.class, GoodsIdentifierConvertor.class},
        reverseConvertGenerate = false),})
public class CreatePriceCommand extends AbstractActionCommand {

    /**
     * 价格id
     */
    private Long priceId;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 规格id
     */
    @AutoMapping(targetClass = PriceEntity.class, target = "specificationIdentifier")
    private Long specificationId;

    /**
     * 价格条目
     */
    private List<PriceItemDto> priceItemDtoList;
}
