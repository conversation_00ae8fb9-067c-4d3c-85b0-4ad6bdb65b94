package com.xk.goods.application.action.command.merchant;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.application.dto.merchant.DiscountDto;
import com.xk.goods.application.dto.merchant.PromotionDto;
import com.xk.goods.application.dto.merchant.RankingDto;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.merchant.MerchantProductRoot;
import com.xk.goods.domain.model.merchant.entity.MerchantProductConfigEntity;
import com.xk.goods.domain.model.merchant.entity.MerchantProductEntity;
import com.xk.goods.domain.model.merchant.id.MerchantProductIdentifier;
import com.xk.goods.domain.model.merchant.valobj.AddKeywordValObj;
import com.xk.goods.domain.model.merchant.valobj.DiscountValObj;
import com.xk.goods.domain.model.merchant.valobj.PromotionValObj;
import com.xk.goods.domain.model.merchant.valobj.RankingValObj;
import com.xk.goods.infrastructure.convertor.collectible.CollectibleCardIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.merchant.PreSaleTimeTypeEnumConvertor;
import com.xk.goods.infrastructure.convertor.merchant.ProductRandomTypeEnumConvertor;
import com.xk.goods.infrastructure.convertor.merchant.ProductTypeEnumConvertor;
import com.xk.goods.infrastructure.convertor.serial.SerialGroupIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.series.SeriesCategoryIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.specification.SpecificationIdentifierConvertor;
import com.xk.goods.interfaces.dto.req.goods.merchant.MerchantProductCreateReqDto;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.Converter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = MerchantProductCreateReqDto.class, convertGenerate = false),
        @AutoMapper(target = MerchantProductEntity.class,
                uses = {SpecificationIdentifierConvertor.class,
                        CollectibleCardIdentifierConvertor.class, ProductTypeEnumConvertor.class,
                        ProductRandomTypeEnumConvertor.class,
                        SeriesCategoryIdentifierConvertor.class, CommonStatusEnumConvertor.class,
                        SerialGroupIdentifierConvertor.class},
                reverseConvertGenerate = false),
        @AutoMapper(target = MerchantProductConfigEntity.class,
                uses = {CommonStatusEnumConvertor.class, PreSaleTimeTypeEnumConvertor.class},
                reverseConvertGenerate = false)})
public class CreateMerchantProductCommand extends AbstractActionCommand {

    /**
     * 主键ID
     */
    private Long goodsId;

    /**
     * 商户id
     */
    private Long corpInfoId;

    /**
     * 收藏卡标识符
     */
    @AutoMapping(targetClass = MerchantProductEntity.class, target = "collectibleCardIdentifier")
    private Long collectibleCardId;

    /**
     * 商品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;

    /**
     * 赠品系列配置
     */
    @AutoMapping(targetClass = MerchantProductEntity.class, target = "giftValObj.giftSeriesConfig")
    private String giftSeriesConfig;

    /**
     * 赠品规格
     */
    @AutoMapping(targetClass = MerchantProductEntity.class, target = "giftValObj.giftSpecification")
    private String giftSpecification;

    /**
     * 随机模式
     */
    private Integer randomType;

    /**
     * 系列id
     */
    @AutoMapping(targetClass = MerchantProductEntity.class, target = "seriesCategoryIdentifier")
    private Long seriesCategoryId;

    /**
     * 上架时长(天)
     */
    private Integer listingDate;

    /**
     * 产品视频地址
     */
    private String productVideoAddr;

    /**
     * 卡密组id
     */
    @AutoMapping(targetClass = MerchantProductEntity.class, target = "serialGroupIdentifier")
    private Long serialGroupId;

    /**
     * 首购优惠状态
     */
    private Integer firstBuyDiscountStatus;

    /**
     * 首购优惠金额
     */
    private Long firstBuyDiscountAmount;

    /**
     * 满减配置状态
     */
    private Integer discountStatus;

    /**
     * 活动配置状态
     */
    private Integer promotionStatus;

    /**
     * 是否开启限购
     */
    private Integer limitStatus;

    /**
     * 限购数量
     */
    private Integer limitAmount;

    /**
     * 是否开启定时限购
     */
    private Integer limitTimeStatus;

    /**
     * 限购1个间隔时长(小时)
     */
    private Integer limitTimeInterval;

    /**
     * 专属优惠券状态
     */
    private Integer exclusiveCouponStatus;

    /**
     * 是否开启预售
     */
    private Integer preSaleStatus;

    /**
     * 预售时间类型
     */
    private Integer preSaleTimeType;

    /**
     * 预售结束时间
     */
    private Date preSaleEndTime;

    /**
     * 需要关注才可购买
     */
    private Integer followBuyStatus;

    /**
     * 赠品配置状态
     */
    private Integer giftStatus;

    /**
     * 附加搜索词状态
     */
    private Integer addKeywordStatus;

    /**
     * 榜单状态
     */
    private Integer rankingStatus;

    /**
     * 关键词
     */
    private List<String> keywordList;

    /**
     * 活动值对象
     */
    private List<PromotionDto> promotionDtoList;

    /**
     * 榜单值对象
     */
    private List<RankingDto> rankingDtoList;

    /**
     * 满减值对象
     */
    private List<DiscountDto> discountDtoList;

    /**
     * 创建人ID
     */
    @AutoMappings({@AutoMapping(targetClass = MerchantProductEntity.class,
            target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({@AutoMapping(targetClass = MerchantProductEntity.class,
            target = "createValObj.createTime")})
    private Date createTime;

    public MerchantProductRoot getRoot(Converter converter) {
        MerchantProductEntity entity = converter.convert(this, MerchantProductEntity.class);
        List<DiscountValObj> discountValObjs =
                converter.convert(this.discountDtoList, DiscountValObj.class);
        if (CollectionUtils.isNotEmpty(discountValObjs)) {
            discountValObjs.forEach(v -> v.setGoodsId(goodsId));
        }
        List<PromotionValObj> promotionValObjs =
                converter.convert(this.promotionDtoList, PromotionValObj.class);
        if (CollectionUtils.isNotEmpty(promotionValObjs)) {
            promotionValObjs.forEach(v -> v.setGoodsId(goodsId));
        }
        List<RankingValObj> rankingValObjs =
                converter.convert(this.rankingDtoList, RankingValObj.class);
        if (CollectionUtils.isNotEmpty(rankingValObjs)) {
            rankingValObjs.forEach(v -> v.setGoodsId(goodsId));
        }
        MerchantProductConfigEntity configEntity =
                converter.convert(this, MerchantProductConfigEntity.class);
        MerchantProductRoot.MerchantProductRootBuilder builder = MerchantProductRoot.builder()
                .identifier(MerchantProductIdentifier.builder()
                        .goodsIdentifier(GoodsIdentifier.builder().goodsId(this.goodsId).build())
                        .build())
                .merchantProductEntity(entity).merchantProductConfigEntity(configEntity)
                .discountValObjList(discountValObjs).promotionValObjList(promotionValObjs)
                .rankingValObjList(rankingValObjs);

        if (CollectionUtils.isNotEmpty(keywordList)) {
            builder.addKeywordValObjList(keywordList.stream()
                    .map(v -> AddKeywordValObj.builder().goodsId(goodsId).keyword(v).build())
                    .toList());
        }
        return builder.build();
    }
}
