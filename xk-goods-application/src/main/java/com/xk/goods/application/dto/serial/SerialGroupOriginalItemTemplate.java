package com.xk.goods.application.dto.serial;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SerialGroupOriginalItemTemplate {

    @ExcelProperty(value = "球员中文名", index = 0)
    private String memberCnName;

    @ExcelProperty(value = "球员英文名", index = 1)
    private String memberEnName;

    @ExcelProperty(value = "球队", index = 2)
    private String teamName;

    @ExcelProperty(value = "卡种编号", index = 3)
    private String cardTypeNo;

    @ExcelProperty(value = "限编", index = 4)
    private String limitEdition;

    @ExcelProperty(value = "卡种", index = 5)
    private String cardType;

    @ExcelProperty(value = "特效", index = 6)
    private String color;

}
