package com.xk.goods.application.action.command.specification;

import static com.xk.application.commons.CommonUtil.setIfNotNull;

import java.util.Date;
import java.util.List;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.specification.SpecificationRoot;
import com.xk.goods.domain.model.specification.entity.SpecificationEntity;
import com.xk.goods.domain.model.specification.valobj.SpecificationNameValObj;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.price.PriceIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.series.SeriesCategoryIdentifierConvertor;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.infrastructure.convertor.stock.StockIdentifierConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SpecificationEntity.class,
        uses = {BusinessTypeEnumConvertor.class, PriceIdentifierConvertor.class,
                GoodsIdentifierConvertor.class, StockIdentifierConvertor.class,
                PlatformTypeEnumConvertor.class, SeriesCategoryIdentifierConvertor.class})})
public class UpdateSpecificationCommand extends AbstractActionCommand {

    /**
     * 商品规格id
     */
    private Long specificationId;

    /**
     * 分发id
     */
    private Long distributionId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 规格名称
     */
    @AutoMappings({@AutoMapping(target = "specificationNameValObj.specName")})
    private String specName;

    /**
     * 规格单位
     */
    private String unitType;

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 卡密明细id
     */
    private List<Long> serialItemIdList;

    /**
     * 系列id
     */
    private Long seriesCategoryId;

    /**
     * 更新人ID
     */
    @AutoMappings({@AutoMapping(targetClass = SpecificationEntity.class, source = "updateId",
            target = "updateValObj.updateId")})
    private Long updateId;

    /**
     * 更新时间
     */
    @AutoMappings({@AutoMapping(targetClass = SpecificationEntity.class, source = "updateTime",
            target = "updateValObj.updateTime")})
    private Date updateTime;

    public SpecificationRoot getRoot(SpecificationRoot root) {
        SpecificationEntity.SpecificationEntityBuilder builder = SpecificationEntity.builder();
        SpecificationEntity entity = root.getSpecificationEntity();
        // 拼接排序
        builder.specificationId(this.specificationId)
                .sort(setIfNotNull(this.sort, entity.getSort()));
        SpecificationNameValObj specificationNameValObj = entity.getSpecificationNameValObj();
        // 拼接规格名称值对象
        SpecificationNameValObj nameValObj = SpecificationNameValObj.builder()
                .specName(setIfNotNull(this.specName, specificationNameValObj.getSpecName()))
                .defaultName(setIfNotNull(this.specName, specificationNameValObj.getDefaultName()))
                .build();
        builder.specificationNameValObj(nameValObj)
                .unitType(setIfNotNull(this.unitType, entity.getUnitType()))
                .status(entity.getStatus()).deleted(entity.getDeleted());

        return SpecificationRoot.builder().identifier(root.getIdentifier())
                .specificationEntity(builder.build()).build();
    }
}
