package com.xk.goods.application.action.command.collectible;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.collectible.CollectibleCardRoot;
import com.xk.goods.domain.model.collectible.entity.CollectibleCardEntity;
import com.xk.goods.infrastructure.convertor.common.BlockTypeEnumConvertor;
import com.xk.goods.interfaces.dto.req.goods.collectible.CollectibleCardReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CollectibleCardReqDto.class, convertGenerate = false),
        @AutoMapper(target = CollectibleCardEntity.class, uses = {BlockTypeEnumConvertor.class},
                reverseConvertGenerate = false)})
public class CreateCollectibleCardCommand extends AbstractActionCommand {

    /**
     * 商品主键
     */
    private Long goodsId;

    /**
     * 版块
     */
    private Integer blockType;

    /**
     * 所属系列
     */
    private String seriesName;

    /**
     * 创建人ID
     */
    @AutoMappings({@AutoMapping(targetClass = CollectibleCardEntity.class,
            target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({@AutoMapping(targetClass = CollectibleCardEntity.class,
            target = "createValObj.createTime")})
    private Date createTime;

    public CollectibleCardRoot getRoot(CollectibleCardEntity entity) {
        return CollectibleCardRoot.builder().identifier(entity.getIdentifier())
                .collectibleCardEntity(entity).build();
    }
}
