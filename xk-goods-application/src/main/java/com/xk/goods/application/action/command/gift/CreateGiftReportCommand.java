package com.xk.goods.application.action.command.gift;

import java.util.Date;
import java.util.List;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.business.id.BusinessResIdentifier;
import com.xk.goods.domain.model.gift.entity.GiftReportEntity;
import com.xk.goods.infrastructure.cache.po.gift.GiftReportPo;
import com.xk.goods.infrastructure.convertor.goods.GoodsIdentifierConvertor;
import com.xk.goods.infrastructure.convertor.merchant.ProductTypeEnumConvertor;
import com.xk.goods.infrastructure.convertor.serial.SerialItemIdentifierConvertor;
import com.xk.goods.interfaces.dto.req.gift.GiftReportSingleReq;
import com.xk.goods.interfaces.dto.req.gift.GiftReportUpdateReq;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({@AutoMapper(target = GiftReportPo.class, convertGenerate = false),
        @AutoMapper(target = GiftReportSingleReq.class, convertGenerate = false),
        @AutoMapper(target = GiftReportSingleReq.GiftReportItemDto.class, convertGenerate = false),
        @AutoMapper(target = GiftReportUpdateReq.GiftReportItemDto.class, convertGenerate = false),
        @AutoMapper(target = GiftReportEntity.class, reverseConvertGenerate = false,
                uses = {ProductTypeEnumConvertor.class, GoodsIdentifierConvertor.class,
                        SerialItemIdentifierConvertor.class, CommonStatusEnumConvertor.class,
                        GoodsIdentifierConvertor.class})})
public class CreateGiftReportCommand extends AbstractActionCommand {

    /**
     * 赠品报告条目id
     */
    private Long giftReportId;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商户id
     */
    private Long corpId;

    /**
     * 卡密id
     */
    private Long serialItemId;

    /**
     * 卡密名称
     */
    private String serialItemName;

    /**
     * 业务资源映射
     */
    private List<BusinessResIdentifier> businessResIdentifierList;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 操作用户id
     */
    private Long operatorUserId;

    /**
     * 本期好卡状态
     */
    private Integer winnerStatus;

    /**
     * 创建人ID
     */
    @AutoMappings({
            @AutoMapping(targetClass = GiftReportEntity.class, target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = GiftReportEntity.class, target = "createValObj.createTime")})
    private Date createTime;
}
