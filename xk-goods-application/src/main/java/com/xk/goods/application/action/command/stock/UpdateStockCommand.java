package com.xk.goods.application.action.command.stock;

import static com.xk.application.commons.CommonUtil.setIfNotNull;
import static com.xk.goods.application.action.command.stock.CreateStockCommand.MAX_STOCK;

import java.util.Date;
import java.util.Objects;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.domain.model.common.UpdateValObj;
import com.xk.domain.model.stock.StockRoot;
import com.xk.domain.model.stock.entity.StockEntity;
import com.xk.domain.model.stock.entity.StockGoodsEntity;
import com.xk.domain.model.stock.entity.StockSpecificationEntity;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.infrastructure.convertor.stock.StockBusinessTypeEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = StockEntity.class,
        uses = {CommonStatusEnumConvertor.class, StockBusinessTypeEnumConvertor.class},
        reverseConvertGenerate = false)})
public class UpdateStockCommand extends AbstractActionCommand {

    /**
     * 库存id
     */
    private Long stockId;

    /**
     * 库存业务类型
     */
    private Integer stockBusinessType;

    /**
     * 业务标识符
     */
    private StringIdentifier businessIdentifier;

    /**
     * 总真实库存
     */
    private Long totalRealStock;

    /**
     * 总虚拟库存
     */
    private Long totalVirtualStock;

    /**
     * 创建人
     */
    @AutoMappings({@AutoMapping(targetClass = StockEntity.class, target = "updateValObj.updateId")})
    private Long updateId;

    /**
     * 创建时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = StockEntity.class, target = "updateValObj.updateTime")})
    private Date updateTime;

    public void buildStock() {
        this.totalRealStock = MAX_STOCK;
        this.totalVirtualStock = MAX_STOCK;
    }

    public void buildStock(Long stockAmount) {
        this.totalRealStock = stockAmount;
        this.totalVirtualStock = stockAmount;
    }

    public void buildGoods(Long goodsId) {
        this.businessIdentifier = StringIdentifier.builder().id(String.valueOf(goodsId)).build();
        this.stockBusinessType = StockBusinessTypeEnum.GOODS.getCode();
    }

    public Long getGoodsId() {
        return Long.valueOf(businessIdentifier.getIdentifier().id());
    }

    public StockGoodsEntity getStockGoodsEntity() {
        return StockGoodsEntity.builder().stockId(this.getStockId())
                .remainRealStock(this.getTotalRealStock())
                .remainVirtualStock(this.getTotalVirtualStock())
                .paidRealStock(this.getTotalRealStock())
                .paidVirtualStock(this.getTotalVirtualStock()).goodsId(this.getGoodsId())
                .deleted(CommonStatusEnum.DISABLE).build();
    }

    public void buildSpecification(Long specificationId) {
        this.businessIdentifier =
                StringIdentifier.builder().id(String.valueOf(specificationId)).build();
        this.stockBusinessType = StockBusinessTypeEnum.SPECIFICATION.getCode();
    }

    public Long getSpecificationId() {
        return Long.valueOf(businessIdentifier.getIdentifier().id());
    }

    public StockSpecificationEntity getStockSpecificationEntity() {
        return StockSpecificationEntity.builder().stockId(this.getStockId())
                .remainRealStock(this.getTotalRealStock())
                .remainVirtualStock(this.getTotalVirtualStock())
                .paidRealStock(this.getTotalRealStock())
                .paidVirtualStock(this.getTotalVirtualStock())
                .specificationId(this.getSpecificationId()).deleted(CommonStatusEnum.DISABLE)
                .build();
    }

    public StockRoot getRoot(StockRoot root) {
        StockEntity entity = root.getStockEntity();
        UpdateValObj updateValObj = UpdateValObj.builder()
                .updateTime(setIfNotNull(updateTime, entity.getUpdateValObj().getUpdateTime()))
                .updateId(setIfNotNull(updateId, entity.getUpdateValObj().getUpdateId())).build();
        StockEntity stockEntity = StockEntity.builder().stockId(entity.getStockId())
                .stockBusinessType(entity.getStockBusinessType())
                .totalRealStock(setIfNotNull(this.totalRealStock, entity.getTotalRealStock()))
                .totalVirtualStock(
                        setIfNotNull(this.totalVirtualStock, entity.getTotalVirtualStock()))
                .deleted(entity.getDeleted()).createValObj(entity.getCreateValObj())
                .updateValObj(updateValObj).build();

        StockRoot.StockRootBuilder builder =
                StockRoot.builder().identifier(root.getIdentifier()).stockEntity(stockEntity);
        if (Objects.requireNonNull(
                stockEntity.getStockBusinessType()) == StockBusinessTypeEnum.SPECIFICATION) {
            builder.stockSpecificationEntity(this.getStockSpecificationEntity());
        } else if (stockEntity.getStockBusinessType() == StockBusinessTypeEnum.GOODS) {
            builder.stockGoodsEntity(this.getStockGoodsEntity());
        }
        return builder.build();
    }
}
