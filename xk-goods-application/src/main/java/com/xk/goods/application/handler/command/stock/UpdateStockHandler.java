package com.xk.goods.application.handler.command.stock;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.xk.domain.model.stock.id.StockIdentifier;
import com.xk.domain.service.stock.StockRootService;
import com.xk.goods.application.action.command.stock.UpdateStockCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateStockHandler implements IActionCommandHandler<UpdateStockCommand, Void> {

    private final StockRootService stockRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdateStockCommand> mono) {
        return this.execute(mono, command -> stockRootService
                .getRootNoDeduction(StockIdentifier.builder().stockId(command.getStockId()).build())
                .switchIfEmpty(Mono.error(
                        new XkGoodsApplicationException(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                .map(command::getRoot), stockRootService::updateRoot);
    }
}
