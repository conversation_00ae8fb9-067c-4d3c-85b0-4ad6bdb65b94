package com.xk.goods.application.action.query.serial;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.interfaces.dto.req.serial.SerialTemplateDetailReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = SerialTemplateDetailReqDto.class, convertGenerate = false)
})
public class SerialTemplateDetailQuery implements IActionQuery {
    /**
     * 卡密模板id
     */
    private Long serialTemplateId;
}
