package com.xk.goods.application.action.command.goods;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.business.id.BusinessResIdentifier;
import com.xk.goods.domain.model.category.id.GoodsCategoryIdentifier;
import com.xk.goods.domain.model.goods.GoodsRoot;
import com.xk.goods.domain.model.goods.entity.GoodsEntity;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.specification.id.SpecificationIdentifier;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.infrastructure.convertor.goods.GoodsTypeEnumConvertor;
import com.xk.goods.infrastructure.convertor.goods.ListingStatusEnumConvertor;
import com.xk.goods.interfaces.dto.req.goods.collectible.CollectibleCardReqDto;
import com.xk.goods.interfaces.dto.req.goods.mall.MallProductReqDto;
import com.xk.goods.interfaces.dto.req.goods.materials.MaterialsProductReqDto;
import com.xk.goods.interfaces.dto.req.goods.merchant.MerchantProductCreateReqDto;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;

import io.github.linpeilie.Converter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = MaterialsProductReqDto.class, convertGenerate = false),
        @AutoMapper(target = MallProductReqDto.class, convertGenerate = false),
        @AutoMapper(target = CollectibleCardReqDto.class, convertGenerate = false),
        @AutoMapper(target = MerchantProductCreateReqDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsEntity.class,
                uses = {GoodsTypeEnumConvertor.class, BusinessTypeEnumConvertor.class,
                        CommonStatusEnumConvertor.class, ListingStatusEnumConvertor.class,
                        PlatformTypeEnumConvertor.class},
                reverseConvertGenerate = false)})
public class CreateGoodsCommand extends AbstractActionCommand {

    /**
     * 商品主键
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    @AutoMappings({
            @AutoMapping(targetClass = GoodsEntity.class, target = "goodsNameValObj.goodsName")})
    private String goodsName;

    /**
     * 商品类型: 1-商城商品, 2-物料商品, 3-收藏卡
     */
    private Integer goodsType;

    /**
     * 上架状态枚举值
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.listingStatus")})
    private Integer listingStatus;

    /**
     * 自动上架状态枚举值
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.autoListingStatus")})
    private Integer autoListingStatus;

    /**
     * 计划上架开始时间
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.planUpTime")})
    private Date planUpTime;

    /**
     * 计划上架结束时间
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.planDownTime")})
    private Date planDownTime;

    /**
     * 实际上架时间
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.actualUpTime")})
    private Date actualUpTime;

    /**
     * 实际下架时间
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsListingValObj.actualDownTime")})
    private Date actualDownTime;

    /**
     * 业务类型枚举值
     */
    private Integer businessType;

    /**
     * 商品库存id
     */
    private Long stockId;

    /**
     * 商品总库存
     */
    private Long totalStock;

    /**
     * 商户商品公司id 其他userId
     */
    @AutoMappings({
            @AutoMapping(targetClass = GoodsEntity.class, target = "goodsSourceValObj.sourceId")})
    private Long sourceId;

    /**
     * 来源 平台类型
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class,
            target = "goodsSourceValObj.platformType")})
    private Integer platformType;

    /**
     * 商品描述
     */
    private String goodsDescribe;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 商品主图id
     */
    private List<Integer> pictureResIdList;

    /**
     * 详情图片
     */
    private List<Integer> detailResIdList;

    /**
     * 来源凭证图片
     */
    private List<Integer> sourceCredentialResIdList;

    /**
     * 介绍图片
     */
    private List<Integer> describeResIdList;

    /**
     * 赠品图片
     */
    private List<Integer> giftResIdList;

    /**
     * 商品类目id
     */
    private List<Long> goodsCategoryIdList;

    /**
     * 规格id
     */
    private List<Long> specificationIdList;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人ID
     */
    @AutoMappings({@AutoMapping(targetClass = GoodsEntity.class, target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = GoodsEntity.class, target = "createValObj.createTime")})
    private Date createTime;

    public GoodsRoot getRoot(CreateGoodsCommand command, Converter converter) {
        GoodsEntity entity = converter.convert(command, GoodsEntity.class);
        GoodsRoot.GoodsRootBuilder builder = GoodsRoot.builder()
                .identifier(GoodsIdentifier.builder().goodsId(this.goodsId).build())
                .goodsEntity(entity);

        populatePictureIdentifiers(builder);
        populateCategoryIdentifiers(builder);
        populateSpecificationIdentifiers(builder);
        return builder.build();
    }

    private void populatePictureIdentifiers(GoodsRoot.GoodsRootBuilder builder) {
        List<BusinessResIdentifier> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pictureResIdList)) {
            List<BusinessResIdentifier> resIdentifierList = pictureResIdList.stream()
                    .map(id -> BusinessResIdentifier.builder()
                            .businessResType(BusinessResTypeEnum.PRODUCT_PICTURE)
                            .businessGroupType(BusinessGroupTypeEnum.GOODS).businessId(goodsId)
                            .resId(id).build())
                    .toList();
            list.addAll(resIdentifierList);
        }
        if (CollectionUtils.isNotEmpty(detailResIdList)) {
            List<BusinessResIdentifier> resIdentifierList = detailResIdList.stream()
                    .map(id -> BusinessResIdentifier.builder()
                            .businessResType(BusinessResTypeEnum.PRODUCT_DETAIL).businessId(goodsId)
                            .businessGroupType(BusinessGroupTypeEnum.GOODS).resId(id).build())
                    .toList();
            list.addAll(resIdentifierList);
        }
        if (CollectionUtils.isNotEmpty(describeResIdList)) {
            List<BusinessResIdentifier> resIdentifierList = describeResIdList.stream()
                    .map(id -> BusinessResIdentifier.builder()
                            .businessResType(BusinessResTypeEnum.PRODUCT_INTRODUCTION)
                            .businessGroupType(BusinessGroupTypeEnum.GOODS).businessId(goodsId)
                            .resId(id).build())
                    .toList();
            list.addAll(resIdentifierList);
        }
        if (CollectionUtils.isNotEmpty(sourceCredentialResIdList)) {
            List<BusinessResIdentifier> resIdentifierList = sourceCredentialResIdList.stream()
                    .map(id -> BusinessResIdentifier.builder()
                            .businessResType(BusinessResTypeEnum.PRODUCT_SOURCE).businessId(goodsId)
                            .businessGroupType(BusinessGroupTypeEnum.GOODS).resId(id).build())
                    .toList();
            list.addAll(resIdentifierList);
        }
        if (CollectionUtils.isNotEmpty(giftResIdList)) {
            List<BusinessResIdentifier> resIdentifierList = giftResIdList.stream()
                    .map(id -> BusinessResIdentifier.builder()
                            .businessResType(BusinessResTypeEnum.GIFT_PICTURE).businessId(goodsId)
                            .businessGroupType(BusinessGroupTypeEnum.GOODS).resId(id).build())
                    .toList();
            list.addAll(resIdentifierList);
        }
        builder.resIdentifierList(list);
    }

    private void populateCategoryIdentifiers(GoodsRoot.GoodsRootBuilder builder) {
        if (CollectionUtils.isNotEmpty(goodsCategoryIdList)) {
            List<GoodsCategoryIdentifier> identifierList = goodsCategoryIdList.stream()
                    .map(id -> GoodsCategoryIdentifier.builder().goodsCategoryId(id).build())
                    .toList();
            builder.goodsCategoryIdentifierList(identifierList);
        }
    }

    private void populateSpecificationIdentifiers(GoodsRoot.GoodsRootBuilder builder) {
        if (CollectionUtils.isNotEmpty(specificationIdList)) {
            List<SpecificationIdentifier> identifierList = specificationIdList.stream()
                    .map(id -> SpecificationIdentifier.builder().specificationId(id).build())
                    .toList();
            builder.specificationIdentifierList(identifierList);
        }
    }
}
