package com.xk.goods.application.action.query.gift;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.goods.interfaces.dto.req.gift.GiftReportInnerSearchReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = GiftReportInnerSearchReq.class, convertGenerate = false)})
public class GiftReportInnerSearchQuery implements IActionQueryMany {

    /**
     * 主键
     */
    private Long goodsId;

    /**
     * 赠品报告id
     */
    private Long giftReportId;
}
