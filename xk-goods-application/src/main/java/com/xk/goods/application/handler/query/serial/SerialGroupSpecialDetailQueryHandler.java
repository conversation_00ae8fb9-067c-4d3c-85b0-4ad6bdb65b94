package com.xk.goods.application.handler.query.serial;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.goods.application.action.query.serial.SerialGroupSpecialDetailQuery;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.repository.serial.SerialGroupRootQueryRepository;
import com.xk.goods.interfaces.dto.res.serial.SerialGroupSpecialDetailResDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SerialGroupSpecialDetailQueryHandler implements
        IActionQueryHandler<SerialGroupSpecialDetailQuery, SerialGroupSpecialDetailResDto> {

    private final SerialGroupRootQueryRepository serialGroupRootQueryRepository;

    @Override
    public Mono<SerialGroupSpecialDetailResDto> execute(
            Mono<SerialGroupSpecialDetailQuery> queryMono) {
        return queryMono
                .flatMap(query -> serialGroupRootQueryRepository
                        .findById(SerialGroupIdentifier.builder()
                                .serialGroupId(query.getSerialGroupId()).build())
                        .flatMap(entity -> {
                            SerialGroupSpecialDetailResDto dto =
                                    new SerialGroupSpecialDetailResDto();
                            BeanUtils.copyProperties(entity, dto);
                            return Mono.just(dto);
                        }));
    }
}
