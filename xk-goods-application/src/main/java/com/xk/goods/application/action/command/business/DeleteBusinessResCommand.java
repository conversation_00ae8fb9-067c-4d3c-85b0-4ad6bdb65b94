package com.xk.goods.application.action.command.business;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({@AutoMapper(target = BusinessResEntity.class),
        @AutoMapper(target = BaseBusinessRes.class)})
public class DeleteBusinessResCommand extends AbstractActionCommand {
    /**
     * 队员图片资源映射
     */
    private BusinessResTypeEnum businessResType;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 资源id
     */
    private Integer resId;

    /**
     * 业务分组类型
     */
    private BusinessGroupTypeEnum businessGroupType;
}
