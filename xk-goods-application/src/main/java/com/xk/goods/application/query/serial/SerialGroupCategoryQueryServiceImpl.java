package com.xk.goods.application.query.serial;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.application.action.query.cate.CateListByGroupQuery;
import com.xk.application.action.query.tree.TreeNodeQueryByComposite;
import com.xk.domain.model.category.Category;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.domain.model.tree.TreeNode;
import com.xk.domain.service.tree.TreeRootService;
import com.xk.goods.domain.model.category.entity.SerialGroupCategoryTreeNode;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupCategoryTreeReqDto;
import com.xk.goods.interfaces.dto.res.serial.SerialGroupCategoryTreeNodeDto;
import com.xk.goods.interfaces.query.serial.SerialGroupCategoryQueryService;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SerialGroupCategoryQueryServiceImpl implements SerialGroupCategoryQueryService {


    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;
    private final TreeRootService treeRootService;
    private final Converter converter;

    @BusiCode
    @Override
    public Mono<List<SerialGroupCategoryTreeNodeDto>> search(Mono<SerialGroupCategoryTreeReqDto> mono) {
        return mono.flatMap(req -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            // 1. 构建分类查询对象
            Function<SerialGroupCategoryTreeReqDto, CateListByGroupQuery> buildCateQuery =
                    request -> CateListByGroupQuery.builder()
                            .compositeIdentifier(CompositeIdentifier.builder()
                                    .type(GroupBusinessTypeEnum.SERIAL.getValue())
                                    .groupId((long) (req.getGroupType() * 10 + req.getBlockType())).build()).build();

            // 2. 分类处理流水线
            Function<Category, Mono<SerialGroupCategoryTreeNode>> processCategory = category -> {
                // 2.1 构建树节点查询
                Function<Category, TreeNodeQueryByComposite> buildNodeQuery =
                        cat -> TreeNodeQueryByComposite.builder()
                                .compositeIdentifier(CompositeIdentifier.builder().groupId(0L)
                                        .type(GroupBusinessTypeEnum.CATE.getValue())
                                        .id(cat.getCateId()).build())
                                .build();

                // 2.2 树节点转换器
                Function<TreeNode, SerialGroupCategoryTreeNode> nodeConverter = treeNode -> {
                    SerialGroupCategoryTreeNode node = new SerialGroupCategoryTreeNode();
                    node.setSerialGroupCategoryId((Long) category.getCompositeIdentifiers().stream()
                            .findFirst().map(CompositeIdentifier::id).orElse(null));
                    node.setNodeId(treeNode.getNodeId());
                    node.setParentId(treeNode.getParentId());
                    node.setStatus(treeNode.getStatus());
                    node.setSort(treeNode.getSort());
                    node.setName(treeNode.getName());
                    return node;
                };

                return this.actionQueryDispatcher.process(Mono.just(buildNodeQuery.apply(category)),
                        TreeNodeQueryByComposite.class, TreeNode.class).map(nodeConverter);
            };

            // 组合函数式处理链
            return this.actionQueryManyDispatcher
                    .executeQuery(Mono.just(buildCateQuery.apply(req)), CateListByGroupQuery.class, Category.class)
                    .flatMap(processCategory).collectList().flatMap(treeRootService::getTree)
                    .map(list -> converter.convert(list, SerialGroupCategoryTreeNodeDto.class))
                    .flatMap(dtoList -> {
                        if (StringUtils.isEmpty(req.getSerialCategoryName())) {
                            return Mono.just(dtoList);
                        }
                        return Mono.just(dtoList.stream()
                                .filter(node -> node.getName() != null &&
                                        (node.getName().contains(req.getSerialCategoryName()) ||
                                                (node.getChildrenDto() != null && !findNodesByName(node.getChildrenDto(), req.getSerialCategoryName()).isEmpty()))).toList());
                    });
        }));
    }

    /**
     * 递归查询类目
     */
    private List<SerialGroupCategoryTreeNodeDto> findNodesByName(List<SerialGroupCategoryTreeNodeDto> nodes, String nodeName) {
        if (nodes == null || nodeName == null) {
            return Collections.emptyList();
        }

        return nodes.stream()
                .filter(node -> node.getName() != null &&
                        (node.getName().contains(nodeName) || (node.getChildrenDto() != null
                                && !findNodesByName(node.getChildrenDto(), nodeName).isEmpty()))).toList();
    }

}
