package com.xk.goods.application.action.command.collectible;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.collectible.entity.CollectibleCardEntity;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = GoodsIdReqDto.class, convertGenerate = false),
        @AutoMapper(target = CollectibleCardEntity.class,
                reverseConvertGenerate = false)})
public class DeleteCollectibleCardCommand extends AbstractActionCommand {

    /**
     * 商品主键
     */
    private Long goodsId;
}
