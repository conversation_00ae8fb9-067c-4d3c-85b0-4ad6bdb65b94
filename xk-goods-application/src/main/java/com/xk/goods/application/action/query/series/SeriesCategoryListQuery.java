package com.xk.goods.application.action.query.series;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.goods.interfaces.dto.req.series.SeriesCategoryTreeReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = SeriesCategoryTreeReqDto.class)
})
public class SeriesCategoryListQuery implements IActionQueryMany {

    /**
     * 业务类型
     */
    @NotNull
    private Integer bizType;
}
