package com.xk.goods.application.handler.query.serial;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.goods.application.action.query.serial.SerialItemQuery;
import com.xk.goods.domain.repository.serial.SerialItemRootQueryRepository;
import com.xk.goods.enums.serial.GroupTypeEnum;
import com.xk.goods.interfaces.dto.res.serial.SerialItemResDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SerialItemQueryHandler implements IActionQueryHandler<SerialItemQuery, SerialItemResDto> {

    private final SerialItemRootQueryRepository serialItemRootQueryRepository;

    @Override
    public Mono<SerialItemResDto> execute(Mono<SerialItemQuery> queryMono) {
        return queryMono.flatMap(query -> {
            Mono<Integer> countMono;

            if (GroupTypeEnum.ORIGINAL.getCode().equals(query.getGroupType())) {
                countMono = serialItemRootQueryRepository.countOriginalItem(query.getSerialGroupId());
            } else {
                countMono = serialItemRootQueryRepository.countSpecialItem(query.getSerialGroupId());
            }

            return countMono.map(count -> SerialItemResDto.builder().num(count).build()
            );
        });
    }
}
