package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.domain.model.serial.entity.SerialSpecialItemEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialItemUpdateShowReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialItemUpdateShowReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialSpecialItemEntity.class, reverseConvertGenerate = false)})
public class UpdateSerialItemShowCommand extends AbstractActionCommand {

    private Long userId;

    /**
     * 卡密条目id
     */
    private Long serialItemId;

    /**
     * 是否显示（0：不显示，1：显示）
     */
    private Integer isShow;
}
