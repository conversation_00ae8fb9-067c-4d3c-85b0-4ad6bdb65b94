package com.xk.goods.application.query.object;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.goods.interfaces.query.object.GoodsObjectQueryHandlerService;
import com.xk.interfaces.dto.req.object.GoodsObjectReqDto;
import com.xk.interfaces.dto.rsp.object.goods.GoodsObjectRspDto;
import com.xk.interfaces.query.object.GoodsObjectQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsObjectQueryHandlerServiceImpl implements GoodsObjectQueryHandlerService {

    private final GoodsObjectQueryService goodsObjectQueryService;

    @BusiCode
    @Override
    public Mono<GoodsObjectRspDto> getGoodsObject(Mono<GoodsObjectReqDto> goodsObjectReqDtoMono) {
        return goodsObjectQueryService.getGoodsObject(goodsObjectReqDtoMono);
    }
}
