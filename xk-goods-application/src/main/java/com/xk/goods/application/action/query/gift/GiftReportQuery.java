package com.xk.goods.application.action.query.gift;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdRequirePagerReqDto;
import com.xk.goods.interfaces.dto.req.goods.ReportPagerReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = GoodsIdRequirePagerReqDto.class, convertGenerate = false),
        @AutoMapper(target = ReportPagerReqDto.class, convertGenerate = false)})
public class GiftReportQuery extends PagerQuery implements IActionQuery {

    /**
     * 主键
     */
    private Long goodsId;

    /**
     * 商家ID
     */
    private Long corpId;
}
