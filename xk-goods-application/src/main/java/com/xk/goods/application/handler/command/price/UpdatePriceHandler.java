package com.xk.goods.application.handler.command.price;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.xk.goods.application.action.command.price.UpdatePriceCommand;
import com.xk.goods.application.support.XkGoodsApplicationException;
import com.xk.goods.domain.model.price.entity.PriceItemEntity;
import com.xk.goods.domain.model.price.id.PriceIdentifier;
import com.xk.goods.domain.service.price.PriceRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdatePriceHandler implements IActionCommandHandler<UpdatePriceCommand, Void> {

    private final PriceRootService priceRootService;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdatePriceCommand> mono) {
        return this.execute(mono, command -> priceRootService
                .getRoot(PriceIdentifier.builder().priceId(command.getPriceId()).build())
                .switchIfEmpty(Mono.error(
                        new XkGoodsApplicationException(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                .map(root -> command.getRoot(root,
                        converter.convert(command.getPriceItemDtoList(), PriceItemEntity.class))),
                priceRootService::updateRoot);
    }
}
