package com.xk.goods.application.task.merchant;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.goods.domain.event.merchant.MerchantProductDownJobEvent;
import com.xk.goods.interfaces.task.merchant.MerchantProductJobService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantProductJobServiceImpl implements MerchantProductJobService {

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> commitMerchantProductEvent(String format) {
        MerchantProductDownJobEvent downJobEvent = MerchantProductDownJobEvent.builder()
                .identifier(EventRoot
                        .getCommonsDomainEventIdentifier(MerchantProductDownJobEvent.class))
                .timeFormat(format).build();
        EventRoot eventRoot = EventRoot.builder().domainEvent(downJobEvent).isTry(false).build();
        return eventRootService.publisheByMono(eventRoot).then();
    }
}
