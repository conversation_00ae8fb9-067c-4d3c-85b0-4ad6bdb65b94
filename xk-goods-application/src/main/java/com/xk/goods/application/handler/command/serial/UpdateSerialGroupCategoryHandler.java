package com.xk.goods.application.handler.command.serial;

import java.util.Date;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.goods.application.action.command.serial.UpdateSerialGroupCategoryCommand;
import com.xk.goods.domain.event.serial.UpdateSerialGroupCategoryCateEvent;
import com.xk.goods.domain.model.serial.SerialGroupRoot;
import com.xk.goods.domain.model.serial.entity.SerialGroupCategoryEntity;
import com.xk.goods.domain.model.serial.id.SerialGroupIdentifier;
import com.xk.goods.domain.repository.serial.SerialGroupCategoryRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateSerialGroupCategoryHandler implements IActionCommandHandler<UpdateSerialGroupCategoryCommand, Void> {

    private final SerialGroupCategoryRootRepository serialGroupCategoryRootRepository;

    private final Converter converter;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<UpdateSerialGroupCategoryCommand> mono) {
        // 1、更新
        Function<UpdateSerialGroupCategoryCommand, Mono<Void>> updateCommand = command -> {
            SerialGroupCategoryEntity entity = this.converter.convert(command, SerialGroupCategoryEntity.class);
            entity.setUpdateId(command.getUserId());
            entity.setUpdateTime(new Date());
            SerialGroupIdentifier serialGroupIdentifier = SerialGroupIdentifier.builder().serialGroupId(entity.getSerialGroupCategoryId()).build();
            return serialGroupCategoryRootRepository.update(SerialGroupRoot.builder().identifier(serialGroupIdentifier)
                    .serialGroupCategoryEntity(entity).build());
        };

        // 2、发事件
        Function<UpdateSerialGroupCategoryCommand, Mono<Void>> publishEvent = command -> {
            EventRoot eventRoot = EventRoot.builder().domainEvent(UpdateSerialGroupCategoryCateEvent.builder()
                    .identifier(EventRoot
                            .getCommonsDomainEventIdentifier(UpdateSerialGroupCategoryCateEvent.class))
                    .parentId(command.getParentId()).categoryName(command.getName())
                    .sort(0).status(1).serialGroupCategoryId(command.getSerialGroupCategoryId())
                    .groupBusinessType(GroupBusinessTypeEnum.SERIAL.getValue())
                    .groupId(1L).build()).build();

            return eventRootService.publisheByMono(eventRoot).then()
                    .doOnSuccess(v -> log.info("UpdateSerialGroupCategoryCateEvent事件发布完成: {}", command.getSerialGroupCategoryId()));
        };
        return mono.flatMap(command -> updateCommand.apply(command).then(publishEvent.apply(command).then()));
    }
}
