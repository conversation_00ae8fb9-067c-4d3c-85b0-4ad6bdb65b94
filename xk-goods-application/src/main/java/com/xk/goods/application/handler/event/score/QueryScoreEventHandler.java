package com.xk.goods.application.handler.event.score;

import org.springframework.stereotype.Component;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.domain.event.score.QueryScoreEvent;
import com.xk.goods.domain.model.goods.id.GoodsIdentifier;
import com.xk.goods.domain.model.score.id.ScoreIdentifier;
import com.xk.goods.domain.model.score.valobj.ScoreRuleVersionValObj;
import com.xk.goods.domain.service.score.ScoreRootService;
import com.xk.goods.domain.service.score.ScoreRuleRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class QueryScoreEventHandler extends AbstractEventVerticle<QueryScoreEvent> {

    private final ScoreRuleRootService scoreRuleRootService;
    private final ScoreRootService scoreRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<QueryScoreEvent> mono) {
        return mono
                .flatMap(event -> scoreRuleRootService
                        .checkScoreRuleVersion(
                                ScoreRuleVersionValObj.builder().ruleVersion(event.getRuleVersion())
                                        .scoreRuleType(event.getScoreRuleType()).build())
                        .flatMap(valObj -> scoreRootService.reCalculate(
                                ScoreIdentifier.builder().productType(event.getProductType())
                                        .goodsIdentifier(GoodsIdentifier.builder()
                                                .goodsId(event.getGoodsId()).build())
                                        .ruleVersion(event.getRuleVersion()).build()))
                        .doOnSuccess(
                                v -> log.info("QueryScoreEvent事件处理完成: {}", event.getGoodsId())))
                .onErrorResume(e -> {
                    log.error("QueryScoreEvent事件处理失败: {}", e.getMessage(), e);
                    return Mono.empty();
                }).then();
    }
}
