package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.application.dto.serial.SerialGroupOriginalItemTemplate;
import com.xk.goods.domain.model.serial.entity.SerialGroupTeamEntity;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupTeamReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = SerialGroupTeamReqDto.class, convertGenerate = false),
        @AutoMapper(target = SerialGroupTeamEntity.class, reverseConvertGenerate = false)})
public class SaveSerialGroupTeamCommand extends AbstractActionCommand {

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 系列id
     */
    private Long seriesCategoryId;

    /**
     * 系列名称
     */
    private String categoryName;

    /**
     * 卡密条目数量
     */
    private Integer serialItemNum;

    /**
     * 资源id
     */
    private Long resId;

    /**
     * 资源地址
     */
    private String addr;
}
