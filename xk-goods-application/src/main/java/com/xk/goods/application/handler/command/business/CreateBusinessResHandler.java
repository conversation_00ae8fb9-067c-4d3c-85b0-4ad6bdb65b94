package com.xk.goods.application.handler.command.business;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.goods.application.action.command.business.CreateBusinessResCommand;
import com.xk.goods.domain.model.business.BusinessResRoot;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.repository.business.BusinessResRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateBusinessResHandler
        implements IActionCommandHandler<CreateBusinessResCommand, Void> {

    private final Converter converter;
    private final BusinessResRootRepository businessResRootRepository;

    @Override
    public Mono<Void> execute(Mono<CreateBusinessResCommand> command) {
        return this.execute(command, data -> {
            BusinessResEntity entity = converter.convert(data, BusinessResEntity.class);
            return BusinessResRoot.builder().identifier(entity.getIdentifier())
                    .businessResEntity(entity).build();
        }, businessResRootRepository::save);
    }
}
