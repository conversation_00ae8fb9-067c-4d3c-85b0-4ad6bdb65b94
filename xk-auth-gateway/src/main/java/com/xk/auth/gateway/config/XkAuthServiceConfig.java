package com.xk.auth.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import com.xk.interfaces.query.object.CorpObjectQueryService;
import com.xk.interfaces.query.object.UserObjectQueryService;

/**
 * @author: killer
 **/
public class XkAuthServiceConfig {
    @Bean
    public UserObjectQueryService userObjectQueryService(
            HttpServiceProxyFactory xkAcctHttpServiceProxyFactory) {
        return xkAcctHttpServiceProxyFactory.createClient(UserObjectQueryService.class);
    }

    @Bean
    public CorpObjectQueryService corpObjectQueryService(
            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpObjectQueryService.class);
    }

}
