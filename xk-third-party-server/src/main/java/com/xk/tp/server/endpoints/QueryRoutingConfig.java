package com.xk.tp.server.endpoints;

import java.util.Objects;
import java.util.function.Function;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.*;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.myco.mydata.server.commons.NetUtil;
import com.myco.mydata.server.handler.WebFluxHandler;
import com.xk.tp.interfaces.dto.req.access.*;
import com.xk.tp.interfaces.dto.req.auth.AuthNotifyReqDto;
import com.xk.tp.interfaces.dto.req.log.SearchUseLogReqDto;
import com.xk.tp.interfaces.dto.req.logistics.LogisticsReqDto;
import com.xk.tp.interfaces.dto.req.pay.PayQuotaReqDto;
import com.xk.tp.interfaces.dto.req.pay.PayResultReqDto;
import com.xk.tp.interfaces.dto.req.pay.PlaceOrderReqDto;
import com.xk.tp.interfaces.dto.req.thirdcaptcha.ThirdCheckCaptchaReqDto;
import com.xk.tp.interfaces.query.access.AccessQueryService;
import com.xk.tp.interfaces.query.auth.AuthQueryService;
import com.xk.tp.interfaces.query.log.UseLogQueryService;
import com.xk.tp.interfaces.query.logistics.LogisticsQueryService;
import com.xk.tp.interfaces.query.pay.PayQueryService;
import com.xk.tp.interfaces.query.thirdcaptcha.ThirdCaptchaQueryService;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * date 2024/07/22
 */
@Configuration(proxyBeanMethods = false)
public class QueryRoutingConfig {

    private static final RequestPredicate ACCEPT_JSON;

    static {
        ACCEPT_JSON = RequestPredicates.accept(MediaType.APPLICATION_JSON);
    }

    @Bean
    public RouterFunction<ServerResponse> accessQueryRouter(AccessQueryService accessQueryService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/query/access"),
                RouterFunctions.route()
                        .POST("/searchAccess", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessPagerReqDto.class,
                                    accessQueryService::searchAccess);
                        })
                        .POST("/searchAccessList", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessListReqDto.class,
                                    accessQueryService::searchAccessList);
                        })
                        .POST("/searchAccessListByNoLogin", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessListNoLoginReqDto.class,
                                    accessQueryService::searchAccessListByNoLogin);
                        })
                        .POST("/searchAccessDetail", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessIdReqDto.class,
                                    accessQueryService::searchAccessDetail);
                        })
                        .POST("/searchAccessExtDetail", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessExtIdReqDto.class,
                                    accessQueryService::searchAccessExtDetail);
                        })
                        .POST("/searchAccessDetailByNoLogin", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessIdByNoLoginReqDto.class,
                                    accessQueryService::searchAccessDetailByNoLogin);
                        })
                        .POST("/searchAccessExtList", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessExtReqDto.class,
                                    accessQueryService::searchAccessExtList);
                        })
                        .POST("/searchAccessAccountList", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessAccountReqDto.class,
                                    accessQueryService::searchAccessAccountList);
                        })
                        .POST("/queryAccessInterfaceAuthInfoByCorp", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, QueryAccessInterfaceAuthInfoReqDto.class,
                                    accessQueryService::queryAccessInterfaceAuthInfoByCorp);
                        })
                        .POST("/queryAccessInterfaceAuthInfoByCorpNoLogin", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, QueryAccessInterfaceAuthInfoNoLoginReqDto.class,
                                    accessQueryService::queryAccessInterfaceAuthInfoByCorpNoLogin);
                        })
                        .POST("/searchAccessTpGame", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessTpGamePagerReqDto.class,
                                    accessQueryService::searchAccessTpGame);
                        })
                        .POST("/queryTagRelation", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, TagRelationListQueryReqDto.class,
                                    accessQueryService::queryTagRelation);
                        })
                        .POST("/searchAccessTag", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessTagPagerReqDto.class,
                                    accessQueryService::searchAccessTag);
                        })
                        .POST("/queryTagRelationByAccessTpTag", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, TagRelationByAccessTpTagReqDto.class,
                                    accessQueryService::queryTagRelationByAccessTpTag);
                        })
                        .POST("/queryAccessGameById", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessGameIdReqDto.class,
                                    accessQueryService::queryAccessGameById);
                        })
                        .POST("/queryAccessGameByDdGameId", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, DdGameIdReqDto.class,
                                    accessQueryService::queryAccessGameByDdGameId);
                        })
                        .POST("/queryAccessExtInfo", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessExtNoSessionReqDto.class,
                                    accessQueryService::queryAccessExtInfo);
                        })
                        .POST("/searchAccessAndAccountList", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessAccountListReqDto.class,
                                    accessQueryService::searchAccessAndAccountList);
                        })
                        .POST("/queryAccessInterfaceAuthInfoByAccessId", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, AccessIdReqDto.class,
                                    accessQueryService::queryAccessInterfaceAuthInfoByAccessId);
                        })
                        .POST("/searchBlackAccessList", ACCEPT_JSON, (request) -> {
                            return WebFluxHandler.handler(request, RequireSessionDto.class,
                                    accessQueryService::searchBlackAccessList);
                        })
                        .build());
    }

    @Bean
    public RouterFunction<ServerResponse> payQueryRouter(PayQueryService payQueryService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/query/pay"),
                RouterFunctions.route().POST("/findByParams", ACCEPT_JSON, (request) -> {
                    Objects.requireNonNull(payQueryService);
                    return WebFluxHandler.handler(request, PlaceOrderReqDto.class, payQueryService::findByParams);
                }).POST("/findPayResult",ACCEPT_JSON,(request -> {
                    return WebFluxHandler.handler(request, PayResultReqDto.class, payQueryService::findPayResult);
                })).POST("/findPayQuota",ACCEPT_JSON,request -> {
                    return WebFluxHandler.handler(request, PayQuotaReqDto.class, payQueryService::findPayQuota);
                }).build());
    }

    @Bean
    public RouterFunction<ServerResponse> authQueryRouter(AuthQueryService authQueryService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/query/auth"),
                RouterFunctions.route().POST("/findAuthNotifyByParams", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, AuthNotifyReqDto.class,
                            authQueryService::findAuthNotifyByParams);
                }).build());
    }

    @Bean
    public RouterFunction<ServerResponse> thirdCaptchaQueryRouter(ThirdCaptchaQueryService thirdCaptchaQueryService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/query/thirdCaptcha"),
                RouterFunctions.route()
                        .POST("/checkCaptcha", ACCEPT_JSON, (request) -> {
                            Function<Mono<ThirdCheckCaptchaReqDto>, Mono<Void>> service = thirdCheckCaptchaReqDtoMono -> thirdCheckCaptchaReqDtoMono
                                    .flatMap(thirdCheckCaptchaReqDto -> {
                                        thirdCheckCaptchaReqDto.setUserIp(NetUtil.getRemoteIp(request));
                                        return thirdCaptchaQueryService.checkCaptcha(
                                                Mono.just(thirdCheckCaptchaReqDto));
                                    });

                            return WebFluxHandler.handler(request, ThirdCheckCaptchaReqDto.class, service);
                        })
                        .build());
    }

    @Bean
    public RouterFunction<ServerResponse> useLogQueryRouter(UseLogQueryService useLogQueryService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/useLog/query"),
                RouterFunctions.route().POST("/searchUseLog", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, SearchUseLogReqDto.class, useLogQueryService::searchUseLog);
                }).build());
    }

    @Bean
    public RouterFunction<ServerResponse> logisticsQueryRouter(
            LogisticsQueryService logisticsQueryService) {
        return RouterFunctions.nest(RequestPredicates.path("/tp/logistics/query"),
                RouterFunctions.route().POST("/detail", ACCEPT_JSON, (request) -> {
                    return WebFluxHandler.handler(request, LogisticsReqDto.class,
                            logisticsQueryService::detail);
                }).build());
    }
}
