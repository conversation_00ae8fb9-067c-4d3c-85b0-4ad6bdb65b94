package com.xk.corp.interfaces.dto.res.user;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CorpUserIdentifierResDto {
    /**
     * 商户成员ID（主键）
     */
    private Long corpUserId;

    /**
     * 商户ID
     */
    private Long corpInfoId;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 用户ID（全局唯一）
     */
    private Long userId;

    /**
     * 成员权限角色
     */
    private Integer corpUserRole;

    /**
     * 员工身份名称
     */
    private String corpUserRoleName;

    /**
     * 成员姓名
     */
    private String corpUserName;

    /**
     * 成员手机号
     */
    private String corpUserPhone;

    /**
     * 成员状态（0-禁用 1-启用）
     */
    private Integer corpUserStatus;

    /**
     * 成员适用平台
     */
    private String corpUserPlatformType;

    /**
     * 成员业务权限角色
     */
    private String corpUserBusinessRole;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;
}
