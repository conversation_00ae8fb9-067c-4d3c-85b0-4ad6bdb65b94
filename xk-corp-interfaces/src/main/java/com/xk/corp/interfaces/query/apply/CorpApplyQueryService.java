package com.xk.corp.interfaces.query.apply;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.corp.interfaces.dto.req.apply.CorpApplyPagerReqDto;
import com.xk.corp.interfaces.dto.res.apply.CorpApplyCountResDto;
import com.xk.corp.interfaces.dto.res.apply.CorpApplySessionResDto;

import reactor.core.publisher.Mono;

/**
 * 商户入驻查询
 */
@HttpExchange("/corp/apply/query")
public interface CorpApplyQueryService {

    /**
     * 商户入驻申请列表
     *
     * @param pagination 分页参数
     * @return void
     */
    @PostExchange("/search/pager")
    Mono<Pagination> searchApplyPager(@RequestBody Mono<CorpApplyPagerReqDto> mono);

    /**
     * 根据用户查询最新申请信息
     * 
     * @param mono mono
     * @return 申请信息
     */
    @PostExchange("/session")
    Mono<CorpApplySessionResDto> searchBySession(@RequestBody Mono<RequireSessionDto> mono);

    /**
     * 统计申请数量
     * 
     * @param mono mono
     * @return 数量
     */
    @PostExchange("/search/count")
    Mono<CorpApplyCountResDto> searchCount(@RequestBody Mono<RequireSessionDto> mono);
}
