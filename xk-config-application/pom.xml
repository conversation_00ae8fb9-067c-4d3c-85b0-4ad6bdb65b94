<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.config</groupId>
        <artifactId>xk-config</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-config-application</artifactId>
    <packaging>jar</packaging>
    <name>xk-config-application</name>
    <description>xk-config-application</description>
    <properties>
        <mapstruct-plus.mapperPackage>com.xk.config.application.convertor.auto</mapstruct-plus.mapperPackage>
        <mapstruct-plus.adapterPackage>com.xk.config.application.convertor.adapter</mapstruct-plus.adapterPackage>
        <mapstruct-plus.autoConfigPackage>com.xk.config.application.config</mapstruct-plus.autoConfigPackage>
        <mapstruct-plus.adapterClassName>XkConfigApplicationConverterMapperAdapter</mapstruct-plus.adapterClassName>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.myco.mydata.config</groupId>
            <artifactId>mydata-config-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.config</groupId>
            <artifactId>xk-config-domain-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.config</groupId>
            <artifactId>xk-config-interfaces</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.config</groupId>
            <artifactId>xk-config-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.config</groupId>
            <artifactId>xk-config-infrastructure</artifactId>
        </dependency>
    </dependencies>
</project>
