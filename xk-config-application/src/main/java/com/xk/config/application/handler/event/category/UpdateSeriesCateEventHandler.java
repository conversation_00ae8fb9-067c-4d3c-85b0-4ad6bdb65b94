package com.xk.config.application.handler.event.category;

import com.xk.goods.domain.event.series.UpdateSeriesCateEvent;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.domain.model.lock.RedisLockObject;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.application.action.command.cate.CateUpdateCommand;
import com.xk.application.action.query.cate.CateByBusinessQuery;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.domain.model.category.Category;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateSeriesCateEventHandler extends AbstractEventVerticle<UpdateSeriesCateEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandActionCommandDispatcher;

    private final LockRootService lockRootService;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<UpdateSeriesCateEvent> event) {
        return event.flatMap(data -> {
            try {
                lockRootService.acquireTransactionNonReentrantXLock(
                        RedisLockObject.LOCKS_UPDATE_CATEGORY, data.getSeriesCategoryId());
                CompositeIdentifier compositeIdentifier = CompositeIdentifier.builder()
                        .id(data.getSeriesCategoryId()).type(data.getGroupBusinessType())
                        .groupId(data.getGroupId()).build();
                CateByBusinessQuery cateByBusinessQuery = CateByBusinessQuery.builder()
                        .compositeIdentifier(compositeIdentifier).build();

                return this.queryDispatcher.process(Mono.just(cateByBusinessQuery),
                        CateByBusinessQuery.class, Category.class).flatMap(category -> {
                            CateUpdateCommand command = CateUpdateCommand.builder()
                                    .cateId(category.getCateId()).cateName(data.getCategoryName())
                                    .sort(data.getSort()).status(data.getStatus()).build();

                            return this.commandActionCommandDispatcher.process(Mono.just(command),
                                    CateUpdateCommand.class, Void.class);
                        });
            } catch (Throwable e) {
                return Mono
                        .error(new XkApplicationException(XkApplicationErrorEnum.GET_LOCK_ERROR));
            }
        }).then(Mono.empty());
    }
}
