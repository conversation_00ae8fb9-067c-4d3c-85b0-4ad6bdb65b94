package com.xk.config.application.action.query.res;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.config.interfaces.dto.req.res.SysResourceIdReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers(value = {@AutoMapper(target = SysResourceIdReqDto.class, convertGenerate = false)})
public class SysResourceUsePagerQuery extends PagerQuery implements IActionQuery {

    private Integer resId;

}
