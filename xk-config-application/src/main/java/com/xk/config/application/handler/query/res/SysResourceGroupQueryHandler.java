package com.xk.config.application.handler.query.res;


import org.springframework.stereotype.Component;

import com.myco.framework.common.util.CglibUtil;
import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.xk.config.application.action.query.res.SysResourceGroupQuery;
import com.xk.config.interfaces.dto.rsp.res.SysResourceRspDto;
import com.xk.domain.model.res.SysResourceEntity;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class SysResourceGroupQueryHandler
        implements IActionQueryManyHandler<SysResourceGroupQuery, SysResourceRspDto> {

    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    private final Converter converter;

    @Override
    public Flux<SysResourceRspDto> execute(Mono<SysResourceGroupQuery> sysResourceGroupQueryMono) {
        return execute(sysResourceGroupQueryMono,
                sysResourceGroupQuery -> converter.convert(sysResourceGroupQuery,
                        SysResourceEntity.class),
                sysResourceRootQueryRepository::findByType, sysResourceEntity -> {
                    SysResourceRspDto sysResourceRspDto = new SysResourceRspDto();
                    CglibUtil.copy(sysResourceEntity, sysResourceRspDto);
                    return sysResourceRspDto;
                });
    }

}
