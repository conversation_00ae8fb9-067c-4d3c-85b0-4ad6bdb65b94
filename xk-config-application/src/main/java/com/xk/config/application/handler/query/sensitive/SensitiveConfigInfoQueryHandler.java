package com.xk.config.application.handler.query.sensitive;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.application.action.query.sensitive.SensitiveConfigInfoQuery;
import com.xk.application.action.res.SensitiveConfigAppDto;
import com.xk.domain.repository.sensitive.SensitiveRootQueryRepository;
import com.xk.interfaces.dto.rsp.sensitive.SensitiveConfigResDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SensitiveConfigInfoQueryHandler
        implements IActionQueryHandler<SensitiveConfigInfoQuery, SensitiveConfigResDto> {

    private final SensitiveRootQueryRepository sensitiveRootQueryRepository;
    private final Converter converter;


    @Override
    public Mono<SensitiveConfigResDto> execute(Mono<SensitiveConfigInfoQuery> query) {
        return query.map(infoQuery -> {
            return LongIdentifier.builder().id(infoQuery.getSensitiveId()).build();
        }).flatMap(sensitiveRootQueryRepository::findById).map(sensitiveConfigEntity -> {
            SensitiveConfigAppDto appDto = this.converter.convert(sensitiveConfigEntity, SensitiveConfigAppDto.class);
            return this.converter.convert(appDto, SensitiveConfigResDto.class);
        });
    }
}
