package com.xk.config.application.service.draft;

import java.util.Date;
import java.util.function.Function;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.config.application.action.command.draft.CreateDraftCommand;
import com.xk.config.application.action.command.draft.DeleteDraftCommand;
import com.xk.config.domain.model.draft.entity.DraftEntity;
import com.xk.config.domain.service.draft.DraftRootService;
import com.xk.config.infrastructure.convertor.draft.DraftBusinessTypeEnumConvertor;
import com.xk.config.interfaces.dto.req.draft.DraftReqDto;
import com.xk.config.interfaces.service.draft.DraftService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class DraftServiceImpl implements DraftService {

    private final DraftRootService draftRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @BusiCode
    @Override
    public Mono<Void> createDraft(Mono<DraftReqDto> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto -> {
            Flux<DraftEntity> searchCurrent =
                    draftRootService.searchByCondition(DraftEntity.builder().userId(userId)
                            .draftBusinessType(
                                    DraftBusinessTypeEnumConvertor.map(dto.getDraftBusinessType()))
                            .businessId(dto.getBusinessId()).build());
            Function<DraftEntity, Mono<Void>> doRemove =
                    entity -> commandDispatcher.executeCommand(Mono.just(new DeleteDraftCommand()),
                            DeleteDraftCommand.class, command -> {
                                command.setDraftId(entity.getDraftId());
                                return command;
                            });

            Mono<Void> doCreate = draftRootService.generateId().flatMap(draftId -> commandDispatcher
                    .executeCommand(mono, CreateDraftCommand.class, command -> {
                        command.setDraftId(draftId);
                        command.setUserId(userId);
                        command.setCreateId(userId);
                        command.setCreateTime(new Date());
                        return command;
                    }));
            return searchCurrent.flatMap(doRemove).then(doCreate);
        }));
    }
}
