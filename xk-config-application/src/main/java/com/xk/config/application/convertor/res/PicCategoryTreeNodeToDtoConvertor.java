package com.xk.config.application.convertor.res;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.commons.util.CollectionUtil;
import com.xk.config.interfaces.dto.rsp.res.PicCategoryTreeNodeDto;
import com.xk.domain.model.res.PicCategoryTreeNode;
import com.xk.domain.model.tree.TreeNode;

import io.github.linpeilie.BaseMapper;

@Component
public class PicCategoryTreeNodeToDtoConvertor
        implements BaseMapper<PicCategoryTreeNode, PicCategoryTreeNodeDto> {

    @Override
    public PicCategoryTreeNodeDto convert(PicCategoryTreeNode picCategoryTreeNode) {
        return convert(picCategoryTreeNode, new PicCategoryTreeNodeDto());
    }

    @Override
    public PicCategoryTreeNodeDto convert(PicCategoryTreeNode treeNode,
            PicCategoryTreeNodeDto picCategoryTreeNodeDto) {
        if (treeNode == null) {
            return null;
        }

        PicCategoryTreeNodeDto treeNodeDto = new PicCategoryTreeNodeDto();
        treeNodeDto.setPicCategoryId(treeNode.getPicCategoryId());
        treeNodeDto.setNodeId(treeNode.getNodeId());
        treeNodeDto.setName(treeNode.getName());
        treeNodeDto.setSort(treeNode.getSort());
        treeNodeDto
                .setStatus(treeNode.getStatus() == null ? null : treeNode.getStatus().getStatus());
        treeNodeDto.setParentId(treeNode.getParentId());
        treeNodeDto.setCompositeIdentifier(treeNode.getCompositeIdentifier());

        List<PicCategoryTreeNodeDto> childrenDto = new ArrayList<>();
        if (!CollectionUtil.isNullOrEmpty(treeNode.getChildren())) {
            for (TreeNode child : treeNode.getChildren()) {
                childrenDto.add(convert((PicCategoryTreeNode) child));
            }
        }
        treeNodeDto.setChildrenDto(childrenDto);
        return treeNodeDto;
    }
}
