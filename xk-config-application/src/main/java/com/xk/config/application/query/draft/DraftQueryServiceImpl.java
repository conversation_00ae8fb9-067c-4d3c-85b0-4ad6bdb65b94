package com.xk.config.application.query.draft;

import java.util.Comparator;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.config.domain.model.draft.entity.DraftEntity;
import com.xk.config.domain.service.draft.DraftRootService;
import com.xk.config.infrastructure.convertor.draft.DraftBusinessTypeEnumConvertor;
import com.xk.config.interfaces.dto.req.draft.DraftSearchReqDto;
import com.xk.config.interfaces.dto.rsp.draft.DraftSearchResDto;
import com.xk.config.interfaces.query.draft.DraftQueryService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class DraftQueryServiceImpl implements DraftQueryService {

    private final DraftRootService draftRootService;

    @BusiCode
    @Override
    public Mono<DraftSearchResDto> searchDraft(Mono<DraftSearchReqDto> mono) {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> mono.flatMap(req -> draftRootService
                        .searchByCondition(DraftEntity.builder()
                                .draftBusinessType(DraftBusinessTypeEnumConvertor
                                        .map(req.getDraftBusinessType()))
                                .businessId(req.getBusinessId()).userId(userId).build())
                        .sort(Comparator.comparing(DraftEntity::getDraftId).reversed())
                        .collectList().filter(list -> !list.isEmpty()).map(list -> {
                            DraftSearchResDto dto = new DraftSearchResDto();
                            dto.setContent(list.getFirst().getContent());
                            return dto;
                        })));
    }
}
