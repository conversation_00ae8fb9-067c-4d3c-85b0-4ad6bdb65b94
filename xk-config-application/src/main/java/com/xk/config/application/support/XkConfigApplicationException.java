package com.xk.config.application.support;

import com.xk.config.application.commons.XkConfigApplicationErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.wrapper.ApplicationWrapperThrowable;

/**
 * @author: killer
 **/
public class XkConfigApplicationException extends ApplicationWrapperThrowable {

    public XkConfigApplicationException(XkConfigApplicationErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public XkConfigApplicationException(XkConfigApplicationErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

}
