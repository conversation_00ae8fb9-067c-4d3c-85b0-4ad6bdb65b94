package com.xk.config.application.handler.query.config;

import java.util.Properties;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.application.action.query.config.BusinessConfigPropertyQuery;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.repository.config.BusinessConfigRootQueryRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class BusinessConfigPropertyQueryHandler
        implements IActionQueryHandler<BusinessConfigPropertyQuery, Properties> {

    private final BusinessConfigRootQueryRepository businessConfigRootQueryRepository;

    private final Converter converter;

    @Override
    public Mono<Properties> execute(Mono<BusinessConfigPropertyQuery> query) {
        return query
                .map(businessConfigInfoQuery -> converter.convert(businessConfigInfoQuery,
                        BusinessConfigEntity.class))
                .flatMapMany(businessConfigRootQueryRepository::findList).collectList()
                .map(businessConfigEntities -> {
                    Properties properties = new Properties();
                    for (BusinessConfigEntity entity : businessConfigEntities) {
                        properties.put(entity.getKey(), entity.getVal());
                    }
                    return properties;
                });
    }
}
