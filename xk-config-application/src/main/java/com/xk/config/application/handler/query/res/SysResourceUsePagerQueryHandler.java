package com.xk.config.application.handler.query.res;


import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.config.application.action.query.res.SysResourceUsePagerQuery;
import com.xk.config.interfaces.dto.rsp.res.SysResourceUseRspDto;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class SysResourceUsePagerQueryHandler
        implements IActionQueryHandler<SysResourceUsePagerQuery, Pagination> {

    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<SysResourceUsePagerQuery> query) {
        return execute(query, sysResourcePagerQuery -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(sysResourcePagerQuery.getLimit());
            pagination.setOffset(sysResourcePagerQuery.getOffset());
            Map<String, Object> criteria = CollectionHelper.converBeanToMap(sysResourcePagerQuery);
            pagination.setCriteria(criteria);
            return pagination;
        }, sysResourceRootQueryRepository::findResourceUsePage, SysResourceUseRspDto.class,
                sysResourceUseEntity -> {
                    SysResourceUseRspDto dto = new SysResourceUseRspDto();
                    BeanUtils.copyProperties(sysResourceUseEntity, dto);
                    return dto;
                });
    }

}
