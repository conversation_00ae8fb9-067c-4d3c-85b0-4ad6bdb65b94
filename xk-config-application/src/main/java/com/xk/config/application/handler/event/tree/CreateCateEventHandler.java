package com.xk.config.application.handler.event.tree;

import java.util.Objects;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.myco.mydata.domain.model.lock.RedisLockObject;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.application.action.command.tree.NodeCreateCommand;
import com.xk.application.action.query.tree.TreeNodeQueryByComposite;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.domain.event.cate.CreateCateEvent;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.domain.model.tree.TreeNode;
import com.xk.infrastructure.commons.tree.TreeSequenceEnum;
import com.xk.interfaces.dto.tree.TreeNodeDto;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CreateCateEventHandler extends AbstractEventVerticle<CreateCateEvent> {
    private final ActionCommandDispatcher<AbstractActionCommand> commandActionCommandDispatcher;
    private final IdentifierGenerateService identifierGenerateService;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final LockRootService lockRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CreateCateEvent> event) {
        return event.flatMap(data -> {
            try {
                lockRootService.acquireTransactionNonReentrantXLock(
                        RedisLockObject.LOCKS_UPDATE_TREE, data.getCateId());
                CompositeIdentifier compositeIdentifier =
                        CompositeIdentifier.builder().id(data.getCateId())
                                .type(GroupBusinessTypeEnum.CATE.getValue()).groupId(0L).build();
                TreeNodeQueryByComposite nodeQueryByComposite = TreeNodeQueryByComposite.builder()
                        .compositeIdentifier(compositeIdentifier).build();
                return this.queryDispatcher
                        .process(Mono.just(nodeQueryByComposite), TreeNodeQueryByComposite.class,
                                TreeNode.class)
                        .filter(Objects::nonNull).flatMap(category -> Mono.empty())
                        .switchIfEmpty(Mono.defer(() -> {
                            TreeNodeDto treeNodeDto = new TreeNodeDto();
                            treeNodeDto.setName(data.getCateName());
                            treeNodeDto.setStatus(data.getStatus());
                            // 暂时固定
                            treeNodeDto.setHeightLimit(4);
                            treeNodeDto.setSort(data.getSort());
                            treeNodeDto.setParentId(data.getParentId());
                            treeNodeDto.setCompositeIdentifier(compositeIdentifier);

                            IdentifierRoot identifierRoot = IdentifierRoot.builder()
                                    .identifier(TreeSequenceEnum.T_TREE_NODE)
                                    .type(IdentifierGenerateEnum.CACHE).build();
                            return Mono
                                    .fromCallable(() -> (Long) identifierGenerateService
                                            .generateIdentifier(identifierRoot))
                                    .flatMap(identifier -> {
                                        return this.commandActionCommandDispatcher.executeCommand(
                                                Mono.just(treeNodeDto), NodeCreateCommand.class,
                                                (commond) -> {
                                                    commond.setNodeId(identifier);
                                                    return commond;
                                                });
                                    });
                        }));
            } catch (Throwable e) {
                return Mono
                        .error(new XkApplicationException(XkApplicationErrorEnum.GET_LOCK_ERROR));
            }
        }).then(Mono.empty());
    }
}

