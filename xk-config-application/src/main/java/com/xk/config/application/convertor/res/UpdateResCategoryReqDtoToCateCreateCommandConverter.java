package com.xk.config.application.convertor.res;

import org.springframework.stereotype.Component;

import com.xk.application.action.command.cate.CateCreateCommand;
import com.xk.config.interfaces.dto.req.res.UpdateResCategoryReqDto;

import io.github.linpeilie.BaseMapper;

@Component
public class UpdateResCategoryReqDtoToCateCreateCommandConverter
        implements BaseMapper<UpdateResCategoryReqDto, CateCreateCommand> {
    @Override
    public CateCreateCommand convert(UpdateResCategoryReqDto dto) {
        return convert(dto, new CateCreateCommand());
    }

    @Override
    public CateCreateCommand convert(UpdateResCategoryReqDto dto, CateCreateCommand command) {
        if (dto == null) {
            return null;
        }
        command.setCateId(Long.valueOf(dto.getGroupId()));
        command.setCateName(dto.getName());
        command.setParentId(dto.getParentId());
        command.setSort(dto.getSort());
        command.setStatus(dto.getStatus());
        return command;
    }
}
