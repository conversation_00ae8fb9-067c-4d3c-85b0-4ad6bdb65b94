package com.xk.config.application.handler.event.corp;

import java.util.Map;
import java.util.function.Function;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.model.lock.RedisLockObject;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.application.action.command.config.CreateBusinessConfigCommand;
import com.xk.corp.domain.event.corp.CorpCreateEvent;
import com.xk.domain.support.config.SysSequenceEnum;
import com.xk.enums.config.BusinessConfigGroupTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CorpCreateEventHandler extends AbstractEventVerticle<CorpCreateEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    private final IdentifierGenerateService identifierGenerateService;

    private final LockRootService lockRootService;

    @Override
    public Mono<Void> handle(Mono<CorpCreateEvent> event) {
        return event.flatMap(corpCreateEvent -> {
            if (MapUtils.isEmpty(corpCreateEvent.getConfigMap())) {
                return Mono.empty();
            }

            Long corpInfoId = corpCreateEvent.getCorpInfoId();
            try {
                lockRootService.acquireTransactionNonReentrantXLock(
                        RedisLockObject.LOCKS_UPDATE_CONFIG, corpInfoId);
            } catch (Throwable e) {
                return Mono.error(e);
            }
            Function<Map.Entry<String, String>, Mono<CreateBusinessConfigCommand>> buildCommand =
                    value -> {
                        IdentifierRoot identifierRoot =
                                IdentifierRoot.builder().identifier(SysSequenceEnum.BUSINESS_CONFIG)
                                        .type(IdentifierGenerateEnum.CACHE).build();
                        Long identifier =
                                (Long) identifierGenerateService.generateIdentifier(identifierRoot);
                        return Mono.just(CreateBusinessConfigCommand.builder()
                                .businessConfigId(identifier).groupId(corpInfoId + "")
                                .groupType(BusinessConfigGroupTypeEnum.CORP.getCode())
                                .businessType(BusinessTypeEnum.XING_KA.getValue())
                                .key(value.getKey()).val(value.getValue()).build());
                    };
            return Flux.fromStream(corpCreateEvent.getConfigMap().entrySet().stream())
                    .flatMap(buildCommand)
                    .flatMap(command -> commandDispatcher.process(Mono.just(command),
                            CreateBusinessConfigCommand.class, Void.class))
                    .then();
        }).onErrorMap(d -> {
            log.error("创建公司配置错误,msg:" + d.getMessage(), d);
            return d;
        });
    }
}
