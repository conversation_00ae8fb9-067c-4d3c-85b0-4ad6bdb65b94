package com.xk.config.application.handler.query.res;


import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.config.application.action.query.res.SysResourcePagerQuery;
import com.xk.config.interfaces.dto.rsp.res.SysResourceRspDto;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class SysResourceSearchQueryHandler
        implements IActionQueryHandler<SysResourcePagerQuery, Pagination> {

    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<SysResourcePagerQuery> query) {
        return execute(query, sysResourcePagerQuery -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(sysResourcePagerQuery.getLimit());
            pagination.setOffset(sysResourcePagerQuery.getOffset());
            Map<String, Object> criteria = CollectionHelper.converBeanToMap(sysResourcePagerQuery);
            criteria.put("bizType", sysResourcePagerQuery.getBizType().getValue());
            pagination.setCriteria(criteria);
            return pagination;
        }, sysResourceRootQueryRepository::findPage, SysResourceRspDto.class, sysResourceEntity -> {
            SysResourceRspDto dto = new SysResourceRspDto();
            BeanUtils.copyProperties(sysResourceEntity, dto);
            return dto;
        });
    }
}
