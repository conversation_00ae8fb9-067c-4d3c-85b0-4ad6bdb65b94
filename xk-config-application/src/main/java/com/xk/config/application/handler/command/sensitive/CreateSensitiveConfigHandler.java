package com.xk.config.application.handler.command.sensitive;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.application.action.command.sensitive.CreateSensitiveConfigCommand;
import com.xk.domain.model.sensitive.SensitiveConfigEntity;
import com.xk.domain.model.sensitive.SensitiveRoot;
import com.xk.domain.repository.sensitive.SensitiveRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Component
@RequiredArgsConstructor
public class CreateSensitiveConfigHandler implements IActionCommandHandler<CreateSensitiveConfigCommand, Void> {

    private final SensitiveRootRepository sensitiveRootRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateSensitiveConfigCommand> command) {
        return this.execute(command, SensitiveConfigEntity.class,
                this.converter::convert,
                (sensitiveConfigEntity) -> SensitiveRoot.builder().identifier(LongIdentifier.builder().id(-1L).build())
                        .sensitiveConfigEntity(sensitiveConfigEntity).build(),
                sensitiveRootRepository::save
        );
    }
}
