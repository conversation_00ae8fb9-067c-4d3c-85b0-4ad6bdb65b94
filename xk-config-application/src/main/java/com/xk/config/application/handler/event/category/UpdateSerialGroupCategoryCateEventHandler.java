package com.xk.config.application.handler.event.category;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.lock.RedisLockObject;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.application.action.command.cate.CateUpdateCommand;
import com.xk.application.action.query.cate.CateByBusinessQuery;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.domain.model.category.Category;
import com.xk.goods.domain.event.serial.UpdateSerialGroupCategoryCateEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateSerialGroupCategoryCateEventHandler
        extends AbstractEventVerticle<UpdateSerialGroupCategoryCateEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    private final LockRootService lockRootService;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<UpdateSerialGroupCategoryCateEvent> event) {
        return event.flatMap(data -> {
            try {
                lockRootService.acquireTransactionNonReentrantXLock(
                        RedisLockObject.LOCKS_UPDATE_CATEGORY, data.getSerialGroupCategoryId());
                CompositeIdentifier compositeIdentifier = CompositeIdentifier.builder()
                        .id(data.getSerialGroupCategoryId()).type(data.getGroupBusinessType())
                        .groupId(data.getGroupId()).build();
                CateByBusinessQuery cateByBusinessQuery = CateByBusinessQuery.builder()
                        .compositeIdentifier(compositeIdentifier).build();

                return this.queryDispatcher.process(Mono.just(cateByBusinessQuery),
                        CateByBusinessQuery.class, Category.class).flatMap(category -> {
                            CateUpdateCommand command = CateUpdateCommand.builder()
                                    .cateId(category.getCateId()).cateName(data.getCategoryName())
                                    .sort(data.getSort()).status(data.getStatus()).build();

                            return this.commandDispatcher.process(Mono.just(command),
                                    CateUpdateCommand.class, Void.class);
                        });
            } catch (Throwable e) {
                return Mono
                        .error(new XkApplicationException(XkApplicationErrorEnum.GET_LOCK_ERROR));
            }
        }).then(Mono.empty());
    }
}
