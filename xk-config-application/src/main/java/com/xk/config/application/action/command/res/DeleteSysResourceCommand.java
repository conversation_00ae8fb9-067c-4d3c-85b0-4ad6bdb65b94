package com.xk.config.application.action.command.res;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.xk.config.interfaces.dto.req.res.SysResourceIdReqDto;
import com.xk.domain.model.res.SysResourceEntity;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers({@AutoMapper(target = SysResourceIdReqDto.class, convertGenerate = false),
        @AutoMapper(target = SysResourceEntity.class, uses = {BusinessTypeEnum.class},
                reverseConvertGenerate = false)})
public class DeleteSysResourceCommand extends AbstractSysResourceCommand {
}
