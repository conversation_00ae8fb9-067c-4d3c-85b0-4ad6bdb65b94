package com.xk.config.application.handler.query.sensitive;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.application.action.query.sensitive.SensitiveConfigSearchPagerQuery;
import com.xk.application.action.res.SensitiveConfigAppDto;
import com.xk.domain.repository.sensitive.SensitiveRootQueryRepository;
import com.xk.interfaces.dto.rsp.sensitive.SensitiveConfigResDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SensitiveConfigSearchPagerQueryHandler
        implements IActionQueryHandler<SensitiveConfigSearchPagerQuery, Pagination> {

    private final SensitiveRootQueryRepository sensitiveRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<SensitiveConfigSearchPagerQuery> query) {
        return this.execute(query,
                (searchGooodsSearchPager) -> {
                    Pagination pagination = new Pagination();
                    pagination.setLimit(searchGooodsSearchPager.getLimit());
                    pagination.setOffset(searchGooodsSearchPager.getOffset());
                    pagination.setCriteria(CollectionHelper.converBeanToMap(searchGooodsSearchPager));
                    return pagination;
                },
                sensitiveRootQueryRepository::searchPager,
                SensitiveConfigResDto.class,
                (goodsEntity, resDtoClass) -> {
                    SensitiveConfigAppDto appDto = converter.convert(goodsEntity, SensitiveConfigAppDto.class);
                    return converter.convert(appDto, resDtoClass);
                }
        );
    }
}
