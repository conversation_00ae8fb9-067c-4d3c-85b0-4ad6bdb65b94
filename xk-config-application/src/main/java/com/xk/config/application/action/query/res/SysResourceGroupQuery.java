package com.xk.config.application.action.query.res;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.xk.config.interfaces.dto.req.res.SysResourceGroupReqDto;
import com.xk.domain.model.res.SysResourceEntity;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers(value = {
        @AutoMapper(target = SysResourceGroupReqDto.class, uses = {BusinessTypeEnum.class},
                convertGenerate = false),
        @AutoMapper(target = SysResourceEntity.class, uses = {BusinessTypeEnum.class},
                convertGenerate = false)})
public class SysResourceGroupQuery implements IActionQueryMany {
    /**
     * 业务类型
     */
    @AutoMapping(targetClass = SysResourceGroupReqDto.class,
            expression = "java(source.getBizType() == null ? null : source.getBizType().getValue())")
    private BusinessTypeEnum bizType;
    /**
     * 分组id 数据字典配置
     */
    private Integer groupId;

    /**
     * 资源类型：数据字典配置
     */
    private Integer resType;

}
