package com.xk.config.application.service.recommend;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.config.application.action.command.recommend.DeleteRecommendCommand;
import com.xk.config.application.action.command.recommend.SaveRecommendCommand;
import com.xk.config.application.action.command.recommend.UpdateRecommendCommand;
import com.xk.config.domain.commons.XkConfigSequenceEnum;
import com.xk.config.interfaces.dto.req.recommend.RecommendDeleteReqDto;
import com.xk.config.interfaces.dto.req.recommend.RecommendSaveReqDto;
import com.xk.config.interfaces.dto.req.recommend.RecommendUpdateReqDto;
import com.xk.config.interfaces.dto.req.recommend.RecommendUpdateShowReqDto;
import com.xk.config.interfaces.service.recommend.RecommendService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Getter
@Slf4j
@Service
@RequiredArgsConstructor
public class RecommendServiceImpl implements RecommendService {

    private final ActionCommandDispatcher<AbstractActionCommand> actionCommandDispatcher;

    private final IdentifierGenerateService identifierGenerateService;

    @BusiCode
    @Override
    public Mono<Void> save(Mono<RecommendSaveReqDto> mono) {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> mono.flatMap(dto -> {
                    Mono<Long> identifierMono = identifierGenerateService.generateIdentifierToMono(
                            IdentifierRoot.builder()
                                    .identifier(XkConfigSequenceEnum.T_RECOMMEND_AD)
                                    .type(IdentifierGenerateEnum.CACHE)
                                    .build()
                    ).cast(Long.class);

                    return identifierMono.flatMap(identifier ->
                            actionCommandDispatcher.executeCommand(Mono.just(dto), SaveRecommendCommand.class, command -> {
                                        command.setUserId(userId);
                                        command.setRecommendAdId(identifier);
                                        return command;
                                    }
                            )
                    );
                }));
    }

    @BusiCode
    @Override
    public Mono<Void> update(Mono<RecommendUpdateReqDto> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto ->
                actionCommandDispatcher.executeCommand(mono, UpdateRecommendCommand.class,
                        command -> {
                            command.setUserId(userId);
                            return command;
                        })));
    }

    @BusiCode
    @Override
    public Mono<Void> updateShow(Mono<RecommendUpdateShowReqDto> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto ->
                actionCommandDispatcher.executeCommand(mono, UpdateRecommendCommand.class,
                        command -> {
                            command.setUserId(userId);
                            return command;
                        })));
    }

    @BusiCode
    @Override
    public Mono<Void> delete(Mono<RecommendDeleteReqDto> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto ->
                actionCommandDispatcher.executeCommand(mono, DeleteRecommendCommand.class,
                        command -> {
                            command.setUserId(userId);
                            return command;
                        })));
    }
}
