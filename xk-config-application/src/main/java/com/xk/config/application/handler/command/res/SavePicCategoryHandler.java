package com.xk.config.application.handler.command.res;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.application.action.command.cate.CateCreateCommand;
import com.xk.config.application.action.command.res.SavePicCategoryCommand;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.infrastructure.commons.cate.CateSequenceEnum;
import com.xk.interfaces.dto.cate.CreateCateDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SavePicCategoryHandler implements IActionCommandHandler<SavePicCategoryCommand, Void> {

    private final IdentifierGenerateService identifierGenerateService;

    private final ActionCommandDispatcher<AbstractActionCommand> commandActionCommandDispatcher;

    @Override
    public Mono<Void> execute(Mono<SavePicCategoryCommand> mono) {
        return mono.flatMap(command -> {
            CompositeIdentifier compositeIdentifier =
                    CompositeIdentifier.builder().id(command.getPicCategoryId()).groupId(1L).type(GroupBusinessTypeEnum.PICTURE.getValue()).build();

            CreateCateDto dto = new CreateCateDto();
            dto.setCateName(command.getCategoryName());
            dto.setSort(0);
            dto.setStatus(1);
            dto.setParentId(command.getParentId());
            dto.setCompositeIdentifiers(Set.of(compositeIdentifier));

            IdentifierRoot identifierRoot =
                    IdentifierRoot.builder().identifier(CateSequenceEnum.T_CATEGORY)
                            .type(IdentifierGenerateEnum.CACHE).build();

            return Mono.fromCallable(
                            () -> (Long) identifierGenerateService.generateIdentifier(identifierRoot))
                    .flatMap(identifier -> this.commandActionCommandDispatcher.executeCommand(
                            Mono.just(dto), CateCreateCommand.class, cateCreateCommand -> {
                                cateCreateCommand.setCateId(identifier);
                                return cateCreateCommand;
                            }));
        }).then(Mono.empty());
    }
}
