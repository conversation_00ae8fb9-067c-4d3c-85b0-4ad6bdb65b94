package com.xk.config.application.handler.query.config;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.xk.application.action.query.config.BusinessConfigSearchQuery;
import com.xk.config.application.convertor.config.BusinessConfigEntityToSysConfigDtoConvertor;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.repository.config.BusinessConfigRootQueryRepository;
import com.xk.interfaces.dto.config.BusinessConfigDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SysConfigSearchQueryHandler implements IActionQueryManyHandler<BusinessConfigSearchQuery, BusinessConfigDto> {

    private final BusinessConfigRootQueryRepository businessConfigRootQueryRepository;

    private final Converter converter;

    private final BusinessConfigEntityToSysConfigDtoConvertor businessConfigEntityToSysConfigDtoConvertor;

    @Override
    public Flux<BusinessConfigDto> execute(Mono<BusinessConfigSearchQuery> query) {
        return execute(query,
                searchQuery -> converter.convert(searchQuery, BusinessConfigEntity.class),
                businessConfigRootQueryRepository::findByParams,
                businessConfigEntityToSysConfigDtoConvertor::convert);
    }
}
