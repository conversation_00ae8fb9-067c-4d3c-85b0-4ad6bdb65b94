package com.xk.config.application.handler.event.category;

import java.util.Set;

import com.xk.goods.domain.event.series.CreateSeriesCateEvent;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.application.action.command.cate.CateCreateCommand;
import com.xk.infrastructure.commons.cate.CateSequenceEnum;
import com.xk.interfaces.dto.cate.CreateCateDto;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateSeriesCateEventHandler extends AbstractEventVerticle<CreateSeriesCateEvent> {

    private final IdentifierGenerateService identifierGenerateService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandActionCommandDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CreateSeriesCateEvent> event) {
        return event.flatMap(data -> {
            CompositeIdentifier compositeIdentifier =
                    CompositeIdentifier.builder().id(data.getSeriesCategoryId())
                            .groupId(data.getGroupId()).type(data.getGroupBusinessType()).build();

            CreateCateDto dto = new CreateCateDto();
            dto.setCateName(data.getCategoryName());
            dto.setSort(data.getSort());
            dto.setStatus(data.getStatus());
            dto.setParentId(data.getParentId());
            dto.setCompositeIdentifiers(Set.of(compositeIdentifier));

            IdentifierRoot identifierRoot =
                    IdentifierRoot.builder().identifier(CateSequenceEnum.T_CATEGORY)
                            .type(IdentifierGenerateEnum.CACHE).build();

            return Mono.fromCallable(
                    () -> (Long) identifierGenerateService.generateIdentifier(identifierRoot))
                    .flatMap(identifier -> {
                        return this.commandActionCommandDispatcher.executeCommand(Mono.just(dto),
                                CateCreateCommand.class, cateCreateCommand -> {
                                    cateCreateCommand.setCateId(identifier);
                                    return cateCreateCommand;
                                });
                    });
        }).then(Mono.empty());
    }
}
