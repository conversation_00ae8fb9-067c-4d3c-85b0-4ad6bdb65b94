package com.xk.config.application.query.config;

import java.util.List;

import com.xk.application.action.query.config.BusinessConfigSearchListQuery;
import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.application.action.query.config.BusinessConfigInfoSearchQuery;
import com.xk.config.interfaces.query.config.BusinessConfigQueryService;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.enums.config.BusinessConfigGroupTypeEnum;
import com.xk.interfaces.dto.config.BusinessConfigDto;
import com.xk.interfaces.dto.config.BusinessConfigInfoReqDto;
import com.xk.interfaces.dto.config.BusinessConfigListDto;
import com.xk.interfaces.dto.config.BusinessConfigSearchDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @Author: liucaihong
 */
@Service
@RequiredArgsConstructor
public class BusinessConfigQueryServiceImpl implements BusinessConfigQueryService {


    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    @BusiCode
    public Mono<BusinessConfigListDto> searchList(Mono<BusinessConfigDto> dto) {
        return actionQueryManyDispatcher
                .executeQuery(dto, BusinessConfigSearchListQuery.class, query -> {
                    if (query.getBusinessType() == null) {
                        query.setBusinessType(BusinessTypeEnum.GAME.getValue());
                    }
                    if (query.getGroupType() == null) {
                        query.setGroupType(BusinessConfigGroupTypeEnum.BASIC.getCode());
                    }
                    if (query.getGroupId() == null) {
                        query.setGroupId(BusinessConfigEntity.DEFAULT_GROUP);
                    }
                    return query;
                }, BusinessConfigDto.class).collectList()
                .map(list -> BusinessConfigListDto.builder().businessConfigList(list).build());
    }


    /**
     * 平台 查询配置
     *
     * @param dto
     * @return
     */
    @Override
    public Mono<String> getOne(Mono<BusinessConfigInfoReqDto> dto) {
        return queryDispatcher.executeQuery(dto, BusinessConfigInfoSearchQuery.class, query -> {
            query.setGroupId(BusinessConfigEntity.DEFAULT_GROUP);
            return query;
        }, BusinessConfigDto.class).map(BusinessConfigDto::getVal);
    }

    /**
     * saas查询配置列表
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<List<BusinessConfigDto>> getList(Mono<BusinessConfigSearchDto> dto) {
        return actionQueryManyDispatcher
                .executeQuery(dto, BusinessConfigSearchListQuery.class, query -> {
                    if (query.getBusinessType() == null) {
                        query.setBusinessType(BusinessTypeEnum.GAME.getValue());
                    }
                    query.setGroupId(BusinessConfigEntity.DEFAULT_GROUP);
                    return query;
                }, BusinessConfigDto.class).collectList();
    }
}
