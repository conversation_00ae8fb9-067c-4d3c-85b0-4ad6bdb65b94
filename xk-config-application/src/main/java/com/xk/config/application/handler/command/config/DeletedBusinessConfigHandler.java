package com.xk.config.application.handler.command.config;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.application.action.command.config.DeletedBusinessConfigCommand;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.model.config.BusinessConfigIdentifier;
import com.xk.domain.model.config.BusinessConfigRoot;
import com.xk.domain.repository.config.BusinessConfigRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;


@Component
@RequiredArgsConstructor
public class DeletedBusinessConfigHandler
        implements IActionCommandHandler<DeletedBusinessConfigCommand, Void> {

    private final BusinessConfigRootRepository businessConfigRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<DeletedBusinessConfigCommand> command) {
        return this.execute(command, BusinessConfigEntity.class, converter::convert,
                entity -> BusinessConfigRoot.builder()
                        .identifier(BusinessConfigIdentifier.builder()
                                .businessConfigId(entity.getBusinessConfigId()).build())
                        .build(),
                businessConfigRootRepository::remove);
    }

}
