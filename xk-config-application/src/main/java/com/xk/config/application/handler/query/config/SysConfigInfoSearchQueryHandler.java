package com.xk.config.application.handler.query.config;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.application.action.query.config.BusinessConfigInfoSearchQuery;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.config.application.convertor.config.BusinessConfigEntityToSysConfigDtoConvertor;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.repository.config.BusinessConfigRootQueryRepository;
import com.xk.interfaces.dto.config.BusinessConfigDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @Author: liucaihong
 */
@Component
@RequiredArgsConstructor
public class SysConfigInfoSearchQueryHandler implements IActionQueryHandler<BusinessConfigInfoSearchQuery, BusinessConfigDto> {

    private final BusinessConfigRootQueryRepository businessConfigRootQueryRepository;

    private final BusinessConfigEntityToSysConfigDtoConvertor businessConfigEntityToSysConfigDtoConvertor;

    private final Converter converter;

    @Override
    public Mono<BusinessConfigDto> execute(Mono<BusinessConfigInfoSearchQuery> query) {
        return query.flatMapMany(searchQuery -> {
                    return businessConfigRootQueryRepository.findList(converter.convert(searchQuery, BusinessConfigEntity.class))
                                                            .filter(sysConfigEntity -> sysConfigEntity.getKey().equals(searchQuery.getKey()));
                })
                .collectList() // 收集所有元素到List
                .flatMap(list -> {
                    if (list.size() > 1) {
                        // 如果List大小大于1，则报错
                        return Mono.error(new XkApplicationException(XkApplicationErrorEnum.DATA_ERROR));
                    } else if (list.isEmpty()) {
                        // 如果List为空，你可能想要返回null、Mono.empty()或抛出一个不同的错误
                        return Mono.empty(); // 示例：返回一个空的Mono
                    } else {
                        // 否则，返回List中的第一个元素
                        return Mono.just(list.getFirst());
                    }
                }).map(businessConfigEntityToSysConfigDtoConvertor::convert);
    }
}
