package com.xk.config.application.handler.event.tree;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.domain.model.lock.RedisLockObject;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.application.action.command.tree.NodeUpdateCommand;
import com.xk.application.action.query.tree.TreeNodeQueryByComposite;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.domain.event.cate.UpdateCateEvent;
import com.xk.domain.model.tree.TreeNode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CateUpdateEventHandler extends AbstractEventVerticle<UpdateCateEvent> {
    private final ActionCommandDispatcher<AbstractActionCommand> commandActionCommandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final LockRootService lockRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<UpdateCateEvent> event) {
        return event.flatMap(data -> {
            try {
                lockRootService.acquireTransactionNonReentrantXLock(
                        RedisLockObject.LOCKS_UPDATE_TREE, data.getCateId());
                CompositeIdentifier compositeIdentifier = CompositeIdentifier.builder()
                        .id(data.getCateId()).type(data.getGroupBusinessType()).groupId(0L).build();
                TreeNodeQueryByComposite nodeQueryByComposite = TreeNodeQueryByComposite.builder()
                        .compositeIdentifier(compositeIdentifier).build();
                return this.queryDispatcher.process(Mono.just(nodeQueryByComposite),
                        TreeNodeQueryByComposite.class, TreeNode.class).flatMap(treeNode -> {
                            NodeUpdateCommand command = NodeUpdateCommand.builder()
                                    .nodeId(treeNode.getNodeId()).sort(data.getSort())
                                    .status(data.getStatus()).name(data.getCateName()).build();


                            return this.commandActionCommandDispatcher.process(Mono.just(command),
                                    NodeUpdateCommand.class, Void.class);
                        });
            } catch (Throwable e) {
                return Mono
                        .error(new XkApplicationException(XkApplicationErrorEnum.GET_LOCK_ERROR));
            }
        }).then(Mono.empty());
    }
}

