package com.xk.config.application.action.command.draft;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.config.domain.model.draft.entity.DraftEntity;
import com.xk.config.interfaces.dto.req.draft.DraftReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = DraftReqDto.class, convertGenerate = false),
        @AutoMapper(target = DraftEntity.class, reverseConvertGenerate = false)})
public class DeleteDraftCommand extends AbstractActionCommand {

    /**
     * 草稿ID
     */
    private Long draftId;
}
