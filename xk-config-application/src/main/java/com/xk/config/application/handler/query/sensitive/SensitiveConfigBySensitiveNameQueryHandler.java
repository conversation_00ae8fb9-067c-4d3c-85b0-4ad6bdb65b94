package com.xk.config.application.handler.query.sensitive;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.application.action.query.sensitive.SensitiveConfigBySensitiveNameQuery;
import com.xk.application.action.res.SensitiveConfigAppDto;
import com.xk.domain.model.sensitive.SensitiveConfigEntity;
import com.xk.domain.repository.sensitive.SensitiveRootQueryRepository;
import com.xk.interfaces.dto.rsp.sensitive.SensitiveConfigResDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SensitiveConfigBySensitiveNameQueryHandler
        implements IActionQueryHandler<SensitiveConfigBySensitiveNameQuery, SensitiveConfigResDto> {

    private final SensitiveRootQueryRepository sensitiveRootQueryRepository;
    private final Converter converter;


    @Override
    public Mono<SensitiveConfigResDto> execute(Mono<SensitiveConfigBySensitiveNameQuery> query) {
        return query.map(infoQuery -> {
            return SensitiveConfigEntity.builder().sensitiveName(infoQuery.getSensitiveName()).build();
        }).flatMap(sensitiveRootQueryRepository::findBySensitiveName).map(sensitiveConfigEntity -> {
            SensitiveConfigAppDto appDto = this.converter.convert(sensitiveConfigEntity, SensitiveConfigAppDto.class);
            return this.converter.convert(appDto, SensitiveConfigResDto.class);
        });
    }
}
