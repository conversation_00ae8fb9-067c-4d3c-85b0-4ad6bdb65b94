package com.xk.config.application.handler.command.res;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.xk.config.application.action.command.res.CreateSysResourceCommand;
import com.xk.domain.model.res.SysResourceEntity;
import com.xk.domain.model.res.SysResourceRoot;
import com.xk.domain.repository.res.SysResourceRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class CreateSysResourceHandler
        implements IActionCommandHandler<CreateSysResourceCommand, Void> {

    private final SysResourceRootRepository sysResourceRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateSysResourceCommand> commandMono) {
        return execute(commandMono, SysResourceEntity.class, converter::convert,
                sysResourceEntity -> SysResourceRoot.builder()
                        .identifier(IntegerIdentifier.builder().id(sysResourceEntity.getResId())
                                .build())
                        .sysResourceEntity(sysResourceEntity).build(),
                sysResourceRootRepository::save);
    }
}
