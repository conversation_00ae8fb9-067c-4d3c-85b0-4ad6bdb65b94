package com.xk.config.application.handler.query.recommend;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.config.application.action.query.recommend.RecommendDetailQuery;
import com.xk.config.domain.repository.recommend.RecommendRootQueryRepository;
import com.xk.config.interfaces.dto.rsp.res.RecommendRspDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class RecommendDetailQueryHandler implements IActionQueryHandler<RecommendDetailQuery, RecommendRspDto> {

    private final RecommendRootQueryRepository recommendRootQueryRepository;

    @Override
    public Mono<RecommendRspDto> execute(Mono<RecommendDetailQuery> queryMono) {
        return queryMono.flatMap(query ->
                recommendRootQueryRepository.searchRecommendDetailById(LongIdentifier.builder().id(query.getRecommendAdId()).build())
                        .flatMap(entity -> {
                            RecommendRspDto resDto = new RecommendRspDto();
                            BeanUtils.copyProperties(entity, resDto);
                            return Mono.just(resDto);
                        })
        );
    }
}
