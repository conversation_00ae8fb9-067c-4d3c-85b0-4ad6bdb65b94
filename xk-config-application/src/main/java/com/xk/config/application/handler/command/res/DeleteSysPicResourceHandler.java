package com.xk.config.application.handler.command.res;

import java.util.List;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.xk.config.application.action.command.res.DeleteSysPicResourceCommand;
import com.xk.domain.model.res.SysResourceRoot;
import com.xk.domain.repository.res.SysResourceRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class DeleteSysPicResourceHandler
        implements IActionCommandHandler<DeleteSysPicResourceCommand, Void> {

    private final SysResourceRootRepository sysResourceRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<DeleteSysPicResourceCommand> commandMono) {
        return commandMono.flatMap(command -> {
            List<IntegerIdentifier> identifiers = command.getResIds().stream()
                    .map(id -> IntegerIdentifier.builder().id(id).build()).toList();
            return sysResourceRootRepository
                    .removePic(SysResourceRoot.builder().identifiers(identifiers)
                            .identifier(IntegerIdentifier.builder().id(1).build()).build());
        });
    }

}
