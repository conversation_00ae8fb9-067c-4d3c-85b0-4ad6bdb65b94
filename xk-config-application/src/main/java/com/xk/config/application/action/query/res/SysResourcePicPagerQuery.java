package com.xk.config.application.action.query.res;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.config.interfaces.dto.req.res.SysResourcePicPagerReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers(value = {@AutoMapper(target = SysResourcePicPagerReqDto.class, convertGenerate = false)})
public class SysResourcePicPagerQuery extends PagerQuery implements IActionQuery {

    /**
     * 资源中文名称
     */
    private String name;

    /**
     * 分类ID列表
     */
    private List<Integer> groupIds;

}
