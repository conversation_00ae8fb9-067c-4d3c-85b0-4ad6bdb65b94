package com.xk.config.application.query.recommend;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.config.application.action.query.recommend.RecommendDetailQuery;
import com.xk.config.application.action.query.recommend.RecommendQuery;
import com.xk.config.interfaces.dto.req.recommend.RecommendAppQueryReqDto;
import com.xk.config.interfaces.dto.req.recommend.RecommendQueryReqDto;
import com.xk.config.interfaces.dto.rsp.res.RecommendRspDto;
import com.xk.config.interfaces.query.recommend.RecommendQueryService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecommendQueryServiceImpl implements RecommendQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    private final Converter converter;

    @BusiCode
    @Override
    public Mono<Pagination> search(Mono<RecommendQueryReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, RecommendQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchByUser(Mono<RecommendAppQueryReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, RecommendQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<RecommendRspDto> searchDetail(Mono<RecommendQueryReqDto> mono) {
        return actionQueryDispatcher.executeQuery(mono, RecommendDetailQuery.class,
                RecommendRspDto.class);
    }
}
