package com.xk.config.application.handler.query.recommend;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.config.application.action.query.recommend.RecommendQuery;
import com.xk.config.domain.model.recommend.RecommendEntity;
import com.xk.config.domain.repository.recommend.RecommendRootQueryRepository;
import com.xk.config.interfaces.dto.rsp.res.RecommendDetailRspDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class RecommendQueryHandler implements IActionQueryHandler<RecommendQuery, Pagination> {

    private final RecommendRootQueryRepository recommendRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<RecommendQuery> queryMono) {
        return execute(queryMono, recommendQuery -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(recommendQuery.getLimit());
            pagination.setOffset(recommendQuery.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(recommendQuery));
            return pagination;
        }, recommendRootQueryRepository::searchRecommend, RecommendEntity.class).flatMap(pagination -> {
            List<RecommendEntity> recommendEntityList = pagination.getRecords();
            List<RecommendDetailRspDto> recommendDetailRspDtoList = recommendEntityList.stream().map(entity -> {
                RecommendDetailRspDto resDto = new RecommendDetailRspDto();
                BeanUtils.copyProperties(entity, resDto);
                return resDto;
            }).toList();
            pagination.setRecords(recommendDetailRspDtoList);
            return Mono.just(pagination);
        });
    }
}
