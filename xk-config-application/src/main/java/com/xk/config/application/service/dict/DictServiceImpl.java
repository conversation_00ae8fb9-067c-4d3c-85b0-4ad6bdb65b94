package com.xk.config.application.service.dict;

import java.util.Collections;
import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.util.MD5Util;
import com.myco.mydata.config.application.action.command.cfg.EditDictObjectCommand;
import com.myco.mydata.config.domain.service.cfg.DictObjectDomainService;
import com.myco.mydata.config.interfaces.dto.req.cfg.SystemConfigDto;
import com.myco.mydata.config.interfaces.dto.req.item.*;
import com.myco.mydata.config.interfaces.query.DictItemQueryService;
import com.myco.mydata.config.interfaces.service.DictItemService;
import com.myco.mydata.config.interfaces.service.DictObjectService;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.acct.interfaces.dto.rsp.manager.SearchAdminPhoneRspDto;
import com.xk.acct.interfaces.query.ManagerUserQueryService;
import com.xk.config.domain.commons.ConfigTypeEnum;
import com.xk.config.enums.dict.CorpRuleTypeEnum;
import com.xk.config.interfaces.dto.req.res.DictPasswordSaveReqDto;
import com.xk.config.interfaces.dto.req.res.DictReqDto;
import com.xk.config.interfaces.service.dict.DictService;
import com.xk.message.enums.validate.IdentifyTypeEnum;
import com.xk.message.enums.validate.ValidateCodeBusinessTypeEnum;
import com.xk.message.interfaces.dto.req.validate.CheckValidateCodeReqDto;
import com.xk.message.interfaces.query.validate.ValidateCodeQueryService;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Getter
@Slf4j
@Service
@RequiredArgsConstructor
public class DictServiceImpl implements DictService {

    private final DictItemQueryService dictItemQueryService;
    private final DictItemService dictItemService;
    private final DictObjectService dictObjectService;
    private final DictObjectDomainService dictObjectDomainService;
    private final ValidateCodeQueryService validateCodeQueryService;
    private final ManagerUserQueryService managerUserQueryService;
    private final ActionCommandDispatcher<AbstractActionCommand> actionCommandDispatcher;

    @Override
    public Mono<Void> updateOrSave(Mono<DictReqDto> dictReqDtoMono) {
        return dictReqDtoMono.flatMap(dictReqDto -> {
            // 准备查询参数
            ItemValueIdentifierReqDto identifierReqDto = new ItemValueIdentifierReqDto();
            CorpRuleTypeEnum itemKeyEnum = CorpRuleTypeEnum.getEnum(dictReqDto.getType());
            identifierReqDto.setItemKey(itemKeyEnum.name());
            identifierReqDto.setItemValue(String.valueOf(itemKeyEnum.getCode()));
            identifierReqDto.setLang(SystemLanguageLocale.ZH.name());
            identifierReqDto.setSessionId(dictReqDto.getSessionId());

            // 查询是否存在
            return dictItemQueryService.getItemValueById(Mono.just(identifierReqDto))
                    // 查询为空时插入
                    .switchIfEmpty(Mono.defer(() -> saveItemDefine(dictReqDto)
                            .then(saveItemValue(dictReqDto)).then(Mono.empty())))
                    // 不为空时更新
                    .flatMap(existingItem -> {
                        // 存在则更新
                        ItemValueUpdateReqDto updateReqDto = new ItemValueUpdateReqDto();
                        updateReqDto.setSessionId(dictReqDto.getSessionId());
                        updateReqDto.setItemKey(itemKeyEnum.name());
                        updateReqDto.setLang(SystemLanguageLocale.ZH.name());
                        updateReqDto.setItemValue(String.valueOf(itemKeyEnum.getCode()));
                        updateReqDto.setItemName(itemKeyEnum.getName());
                        updateReqDto.setItemJson(this.getJsonInfo(dictReqDto));

                        return dictItemService.updateItemValue(Mono.just(updateReqDto));
                    });
        });
    }

    @BusiCode
    @Override
    public Mono<Void> updatePassword(Mono<DictPasswordSaveReqDto> mono) {
        return mono.flatMap(dto -> {
            Supplier<Mono<Void>> checkOldPassword = () -> {
                String oldPassword = dto.getOldPassword();
                return dictObjectDomainService
                        .getSystemConfigValue(ConfigTypeEnum.MANAGER_TRADING_PASSWORD)
                        .filter(oldPasswordStr -> MD5Util.md5(oldPassword).equals(oldPasswordStr))
                        .switchIfEmpty(Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.VALIDATE_FAILURE)))
                        .then();
            };

            Supplier<Mono<String>> getPhone = () -> {
                RequireSessionDto requireSessionDto = new RequireSessionDto();
                requireSessionDto.setSessionId(dto.getSessionId());
                return managerUserQueryService.searchAdminPhone(Mono.just(requireSessionDto))
                        .map(SearchAdminPhoneRspDto::getMobile);
            };

            Function<String, Mono<Void>> checkCode = mobile -> {
                CheckValidateCodeReqDto reqDto = new CheckValidateCodeReqDto();
                reqDto.setSessionId(dto.getSessionId());
                reqDto.setCode(dto.getValidateCode());
                reqDto.setBusinessType(BusinessTypeEnum.XING_KA.getValue());
                reqDto.setValidateCodeBusinessType(
                        ValidateCodeBusinessTypeEnum.TRADING_PASSWORD_CHANGE.getCode());
                reqDto.setIdentifyType(IdentifyTypeEnum.MOBILE.getCode());
                reqDto.setIdentifyCode(mobile);
                reqDto.setMobileCode(dto.getMobileCode());
                return validateCodeQueryService.checkValidateCode(Mono.just(reqDto)).then();
            };

            Supplier<Mono<Void>> saveNewPassword = () -> {
                String newPassword = dto.getPassword();
                SystemConfigDto configDto = new SystemConfigDto();
                configDto.setParamId(ConfigTypeEnum.MANAGER_TRADING_PASSWORD.name());
                configDto.setParamValue(MD5Util.md5(newPassword));
                return this.actionCommandDispatcher.executeCommand(Mono.just(configDto),
                        EditDictObjectCommand.class);
            };

            return checkOldPassword.get().then(getPhone.get().flatMap(checkCode))
                    .then(saveNewPassword.get());
        });
    }

    // 提取插入定义方法
    private Mono<Void> saveItemDefine(DictReqDto dictReqDto) {
        CorpRuleTypeEnum itemKeyEnum = CorpRuleTypeEnum.getEnum(dictReqDto.getType());
        ItemDefineReqDto itemDefineReqDto = new ItemDefineReqDto();
        itemDefineReqDto.setSessionId(dictReqDto.getSessionId());
        itemDefineReqDto.setItemKey(itemKeyEnum.name());
        itemDefineReqDto.setLang(SystemLanguageLocale.ZH.name());
        itemDefineReqDto.setItemKeyDesc(itemKeyEnum.getName());
        itemDefineReqDto.setItemType(1);
        return dictItemService.saveItemDefine(Mono.just(itemDefineReqDto));
    }

    // 提取插入值方法
    private Mono<Void> saveItemValue(DictReqDto dictReqDto) {
        // 构造数据
        CorpRuleTypeEnum itemKeyEnum = CorpRuleTypeEnum.getEnum(dictReqDto.getType());

        BatchItemValueReqDto batchItemValueReqDto = new BatchItemValueReqDto();
        ItemValueDto itemValueDto = new ItemValueDto();
        itemValueDto.setItemKey(itemKeyEnum.name());
        itemValueDto.setLang(SystemLanguageLocale.ZH.name());
        itemValueDto.setItemValue(String.valueOf(itemKeyEnum.getCode()));
        itemValueDto.setItemName(itemKeyEnum.getName());
        itemValueDto.setItemJson(this.getJsonInfo(dictReqDto));

        batchItemValueReqDto.setSessionId(dictReqDto.getSessionId());
        batchItemValueReqDto.setItemValues(Collections.singletonList(itemValueDto));
        return dictItemService.saveItemValue(Mono.just(batchItemValueReqDto));
    }

    private String getJsonInfo(DictReqDto dictReqDto) {
        DictReqDto resultJson = new DictReqDto();
        if (dictReqDto.getType().equals(CorpRuleTypeEnum.ANNOUNCE_INFO.getCode())) {
            resultJson.setType(dictReqDto.getType());
            resultJson.setAnnouncePeriod(dictReqDto.getAnnouncePeriod());
            resultJson.setAnnounceType(dictReqDto.getAnnounceType());
            resultJson.setAcceptType(dictReqDto.getAcceptType());
            resultJson.setSellOutPeriod(dictReqDto.getSellOutPeriod());
        } else {
            resultJson.setContent(dictReqDto.getContent());
        }
        return JSON.toJSONString(resultJson);
    }
}
