package com.xk.config.application.action.command.recommend;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.config.domain.model.recommend.RecommendEntity;
import com.xk.config.interfaces.dto.req.recommend.RecommendDeleteReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMappers({@AutoMapper(target = RecommendDeleteReqDto.class, convertGenerate = false),
        @AutoMapper(target = RecommendEntity.class, reverseConvertGenerate = false)})
public class DeleteRecommendCommand extends AbstractActionCommand {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 推荐广告主键
     */
    private Long recommendAdId;
}
