package com.xk.config.application.action.command.res;

import java.util.Date;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.xk.config.interfaces.dto.req.res.SysResourceEditReqDto;
import com.xk.domain.model.res.SysResourceEntity;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = SysResourceEntity.class, uses = {BusinessTypeEnumConvertor.class}),
        @AutoMapper(target = SysResourceEditReqDto.class, uses = {BusinessTypeEnum.class},
                convertGenerate = false),})
public class EditSysResourceCommand extends AbstractSysResourceCommand {
    /**
     * 业务类型
     */
    @AutoMapping(targetClass = SysResourceEditReqDto.class,
            expression = "java(source.getBizType() == null ? null : source.getBizType().getValue())")
    private BusinessTypeEnum bizType;
    /**
     * 分组id 数据字典配置
     */
    private Integer groupId;

    /**
     * 资源类型：数据字典配置
     */
    private Integer resType;

    /**
     * 资源地址
     */
    private String addr;

    /**
     * 资源中文名称
     */
    private String name;

    /**
     * 资源英文名称
     */
    private String nameKey;

    /**
     * 大小 kb
     */
    private Integer size;

    /**
     * 尺寸
     */
    private String dimension;
    /**
     * 排序
     */
    private Integer idx;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 描述
     */
    private String remark;

    /**
     * 跳转类型
     */
    private Integer jumpType;
    /**
     * 跳转地址
     */
    private String jumpUrl;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 封面图
     */
    private String coverImg;
}
