package com.xk.config.application.handler.command.sensitive;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.application.action.command.sensitive.DeletedSensitiveConfigCommand;
import com.xk.domain.model.sensitive.SensitiveConfigEntity;
import com.xk.domain.model.sensitive.SensitiveRoot;
import com.xk.domain.repository.sensitive.SensitiveRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Component
@RequiredArgsConstructor
public class DeletedSensitiveConfigHandler implements IActionCommandHandler<DeletedSensitiveConfigCommand, Void> {

    private final SensitiveRootRepository sensitiveRootRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<DeletedSensitiveConfigCommand> command) {
        return command.flatMap(deletedSensitiveConfigCommand -> {
            return Flux.fromIterable(deletedSensitiveConfigCommand.getSensitiveIds())
                    .flatMap(sensitiveId -> {
                        return sensitiveRootRepository.remove(
                                SensitiveRoot.builder().identifier(LongIdentifier.builder()
                                                .id(sensitiveId).build())
                                        .sensitiveConfigEntity(
                                                SensitiveConfigEntity.builder().sensitiveId(sensitiveId).build())
                                        .build());
                    }).then();
        });
    }
}
