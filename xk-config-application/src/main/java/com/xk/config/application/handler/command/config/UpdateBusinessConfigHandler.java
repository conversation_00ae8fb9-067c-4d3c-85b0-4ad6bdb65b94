package com.xk.config.application.handler.command.config;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.application.action.command.config.UpdateBusinessConfigCommand;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.model.config.BusinessConfigIdentifier;
import com.xk.domain.model.config.BusinessConfigRoot;
import com.xk.domain.repository.config.BusinessConfigRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class UpdateBusinessConfigHandler
        implements IActionCommandHandler<UpdateBusinessConfigCommand, Void> {

    private final BusinessConfigRootRepository businessConfigRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdateBusinessConfigCommand> command) {
        Function<UpdateBusinessConfigCommand, BusinessConfigRoot> function =
                command12 -> BusinessConfigRoot.builder()
                        .identifier(BusinessConfigIdentifier.builder()
                                .businessConfigId(command12.getBusinessConfigId()).build())
                        .businessConfigEntity(
                                converter.convert(command12, BusinessConfigEntity.class))
                        .build();
        return execute(command, function, businessConfigRootRepository::update);
    }
}
