package com.xk.config.application.handler.command.draft;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.config.application.action.command.draft.CreateDraftCommand;
import com.xk.config.domain.model.draft.DraftRoot;
import com.xk.config.domain.model.draft.entity.DraftEntity;
import com.xk.config.domain.repository.draft.DraftRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateDraftHandler implements IActionCommandHandler<CreateDraftCommand, Void> {

    private final Converter converter;
    private final DraftRootRepository draftRootRepository;

    @Override
    public Mono<Void> execute(Mono<CreateDraftCommand> command) {
        return execute(
                command, DraftEntity.class, this.converter::convert, entity -> DraftRoot.builder()
                        .identifier(entity.getIdentifier()).draftEntity(entity).build(),
                draftRootRepository::save);
    }
}
