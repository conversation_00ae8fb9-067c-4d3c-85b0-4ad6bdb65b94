package com.xk.config.application.handler.query.sensitive;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.application.action.query.sensitive.SensitiveRecordInfoQuery;
import com.xk.application.action.res.SensitiveRecordAppDto;
import com.xk.domain.repository.sensitive.SensitiveRootQueryRepository;
import com.xk.interfaces.dto.rsp.sensitive.SensitiveRecordResDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SensitiveRecordInfoQueryHandler
        implements IActionQueryHandler<SensitiveRecordInfoQuery, SensitiveRecordResDto> {

    private final SensitiveRootQueryRepository sensitiveRootQueryRepository;
    private final Converter converter;


    @Override
    public Mono<SensitiveRecordResDto> execute(Mono<SensitiveRecordInfoQuery> query) {
        return query.map(infoQuery -> {
            return LongIdentifier.builder().id(infoQuery.getSensitiveSn()).build();
        }).flatMap(sensitiveRootQueryRepository::findSensitiveRecordBySensitiveSn).map(sensitiveRecordEntity -> {
            SensitiveRecordAppDto appDto = this.converter.convert(sensitiveRecordEntity, SensitiveRecordAppDto.class);
            return this.converter.convert(appDto, SensitiveRecordResDto.class);
        });
    }
}
