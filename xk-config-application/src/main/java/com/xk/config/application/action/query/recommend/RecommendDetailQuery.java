package com.xk.config.application.action.query.recommend;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.config.interfaces.dto.req.recommend.RecommendQueryReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = RecommendQueryReqDto.class, convertGenerate = false)})
public class RecommendDetailQuery implements IActionQuery {
    /**
     * 推荐广告主键
     */
    private Long recommendAdId;

}
