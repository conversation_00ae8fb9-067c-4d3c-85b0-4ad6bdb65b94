package com.xk.config.application.handler.event.corp;

import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

import com.xk.application.action.command.config.UpdateBusinessConfigCommand;
import com.xk.application.action.query.config.BusinessConfigInfoSearchQuery;
import com.xk.interfaces.dto.config.BusinessConfigDto;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.domain.model.lock.RedisLockObject;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.corp.domain.event.corp.CorpUpdateEvent;
import com.xk.enums.config.BusinessConfigGroupTypeEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateCorpEventHandler extends AbstractEventVerticle<CorpUpdateEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final LockRootService lockRootService;
    private final Converter converter;

    @Override
    public Mono<Void> handle(Mono<CorpUpdateEvent> event) {
        return event.flatMap(corpUpdateEvent -> {
            if (MapUtils.isEmpty(corpUpdateEvent.getConfigMap())) {
                return Mono.empty();
            }

            Long corpInfoId = corpUpdateEvent.getCorpInfoId();
            try {
                lockRootService.acquireTransactionNonReentrantXLock(
                        RedisLockObject.LOCKS_UPDATE_CONFIG, corpInfoId);
            } catch (Throwable e) {
                return Mono.error(e);
            }
            Map<String, String> configMap = corpUpdateEvent.getConfigMap();

            Function<BusinessConfigInfoSearchQuery, Mono<BusinessConfigDto>> executeQuery =
                    query -> actionQueryDispatcher.executeQuery(Mono.just(query),
                            BusinessConfigInfoSearchQuery.class, BusinessConfigDto.class);

            BiFunction<BusinessConfigInfoSearchQuery, BusinessConfigDto, UpdateBusinessConfigCommand> convertToCommand =
                    (query, dto) -> {
                        UpdateBusinessConfigCommand command =
                                converter.convert(query, UpdateBusinessConfigCommand.class);
                        command.setBusinessConfigId(dto.getBusinessConfigId());
                        command.setVal(configMap.get(command.getKey()));
                        return command;
                    };

            // 组合主 Function
            Function<Map.Entry<String, String>, Mono<UpdateBusinessConfigCommand>> buildCommand =
                    entry -> {
                        BusinessConfigInfoSearchQuery query =
                                BusinessConfigInfoSearchQuery.builder().groupId(corpInfoId + "")
                                                             .groupType(BusinessConfigGroupTypeEnum.CORP.getCode())
                                                             .businessType(BusinessTypeEnum.XING_KA.getValue())
                                                             .key(entry.getKey()).build();
                        return executeQuery.apply(query)
                                .map(dto -> convertToCommand.apply(query, dto));
                    };

            return Flux.fromStream(corpUpdateEvent.getConfigMap().entrySet().stream())
                    .flatMap(buildCommand).flatMap(command -> commandDispatcher
                            .process(Mono.just(command), UpdateBusinessConfigCommand.class, Void.class))
                    .then();
        }).onErrorMap(d -> {
            log.error("更新公司配置错误,msg:" + d.getMessage(), d);
            return d;
        });
    }
}
