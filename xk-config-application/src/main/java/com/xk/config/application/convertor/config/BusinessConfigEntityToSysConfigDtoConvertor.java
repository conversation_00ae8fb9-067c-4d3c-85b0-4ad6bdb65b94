package com.xk.config.application.convertor.config;

import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.interfaces.dto.config.BusinessConfigDto;
import io.github.linpeilie.BaseMapper;
import org.springframework.stereotype.Component;

/**
 * @Author: liucaihong
 */
@Component
public class BusinessConfigEntityToSysConfigDtoConvertor implements BaseMapper<BusinessConfigEntity, BusinessConfigDto> {
    @Override
    public BusinessConfigDto convert(BusinessConfigEntity entity) {
        return convert(entity, new BusinessConfigDto());
    }

    @Override
    public BusinessConfigDto convert(BusinessConfigEntity entity, BusinessConfigDto dto) {
        dto.setBusinessConfigId(entity.getBusinessConfigId());
        dto.setBusinessType(entity.getBusinessType());
        dto.setGroupType(entity.getGroupType());
        dto.setGroupId(entity.getGroupId());
        dto.setKey(entity.getKey());
        dto.setVal(entity.getVal());
        return dto;
    }
}
