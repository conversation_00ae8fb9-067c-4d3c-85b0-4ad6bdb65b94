package com.xk.config.application.action.command.res;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.config.interfaces.dto.req.res.PicCategorySaveReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import com.xk.domain.model.category.Category;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = PicCategorySaveReqDto.class, convertGenerate = false),
        @AutoMapper(target = Category.class, reverseConvertGenerate = false)})
public class SavePicCategoryCommand extends AbstractActionCommand {

    private Long picCategoryId;

    private Long userId;

    private Long parentId;

    private String categoryName;

}
