package com.xk.config.application.action.query.res;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.xk.config.interfaces.dto.req.res.SysResourcePagerReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers(value = {@AutoMapper(target = SysResourcePagerReqDto.class,
        uses = {BusinessTypeEnum.class}, convertGenerate = false)})
public class SysResourcePagerQuery extends PagerQuery implements IActionQuery {

    /**
     * 资源id
     */
    private Integer resId;
    /**
     * 业务类型
     */
    @AutoMapping(targetClass = SysResourcePagerReqDto.class,
            expression = "java(source.getBizType() == null ? null : source.getBizType().getValue())")
    private BusinessTypeEnum bizType;
    /**
     * 分组id 数据字典配置
     */
    private Integer groupId;

    /**
     * 资源类型：数据字典配置
     */
    private Integer resType;
    /**
     * 资源地址
     */
    private String addr;

    /**
     * 资源中文名称
     */
    private String name;
}
