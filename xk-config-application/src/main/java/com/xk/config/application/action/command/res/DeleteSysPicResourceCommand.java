package com.xk.config.application.action.command.res;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.config.interfaces.dto.req.res.SysResourceIdReqDto;
import com.xk.config.interfaces.dto.req.res.SysResourceIdsReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers({
        @AutoMapper(target = SysResourceIdsReqDto.class, convertGenerate = false)
})
public class DeleteSysPicResourceCommand extends AbstractActionCommand {
    /**
     * 资源id
     */
    @NotNull
    private List<Integer> resIds;
}
