package com.xk.config.application.handler.query.config;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.application.action.query.config.BusinessConfigInfoQuery;
import com.xk.config.application.convertor.config.BusinessConfigEntityToSysConfigDtoConvertor;
import com.xk.domain.model.config.BusinessConfigIdentifier;
import com.xk.domain.repository.config.BusinessConfigRootQueryRepository;
import com.xk.interfaces.dto.config.BusinessConfigDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SysConfigInfoQueryHandler implements IActionQueryHandler<BusinessConfigInfoQuery, BusinessConfigDto> {

    private final BusinessConfigRootQueryRepository businessConfigRootQueryRepository;

    private final BusinessConfigEntityToSysConfigDtoConvertor businessConfigEntityToSysConfigDtoConvertor;

    @Override
    public Mono<BusinessConfigDto> execute(Mono<BusinessConfigInfoQuery> query) {
        Function<BusinessConfigInfoQuery, BusinessConfigIdentifier> function = sysConfigInfoQuery ->
                BusinessConfigIdentifier.builder().businessConfigId(sysConfigInfoQuery.getBusinessConfigId()).build();
        return execute(query, function, businessConfigRootQueryRepository::findById,
                businessConfigEntityToSysConfigDtoConvertor::convert);
    }
}
