package com.xk.config.application.handler.command.sensitive;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.application.action.command.sensitive.CreateSensitiveRecordCommand;
import com.xk.domain.model.sensitive.SensitiveRecordEntity;
import com.xk.domain.model.sensitive.SensitiveRoot;
import com.xk.domain.repository.sensitive.SensitiveRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collections;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Component
@RequiredArgsConstructor
public class CreateSensitiveRecordHandler implements IActionCommandHandler<CreateSensitiveRecordCommand, Void> {

    private final SensitiveRootRepository sensitiveRootRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateSensitiveRecordCommand> command) {
        return this.execute(command, SensitiveRecordEntity.class,
                this.converter::convert,
                (sensitiveRecord) -> SensitiveRoot.builder().identifier(LongIdentifier.builder().id(-1L).build())
                        .sensitiveRecordEntities(Collections.singletonList(sensitiveRecord)).build(),
                sensitiveRootRepository::saveSensitiveRecord
        );
    }
}
