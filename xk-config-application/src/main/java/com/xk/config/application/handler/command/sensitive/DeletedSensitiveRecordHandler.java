package com.xk.config.application.handler.command.sensitive;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.application.action.command.sensitive.DeletedSensitiveRecordCommand;
import com.xk.domain.model.sensitive.SensitiveRecordEntity;
import com.xk.domain.model.sensitive.SensitiveRoot;
import com.xk.domain.repository.sensitive.SensitiveRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collections;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Component
@RequiredArgsConstructor
public class DeletedSensitiveRecordHandler implements IActionCommandHandler<DeletedSensitiveRecordCommand, Void> {

    private final SensitiveRootRepository sensitiveRootRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<DeletedSensitiveRecordCommand> command) {
        return this.execute(command, (data) -> {
            return SensitiveRoot.builder()
                    .identifier(LongIdentifier.builder().id(-1L).build())
                    .sensitiveRecordEntities(Collections.singletonList(
                            SensitiveRecordEntity.builder().sensitiveSn(data.getSensitiveSn()).build()
                    ))
                    .build();
        }, sensitiveRootRepository::removeSensitiveRecord);
    }
}
