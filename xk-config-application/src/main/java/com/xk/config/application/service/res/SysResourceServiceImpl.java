package com.xk.config.application.service.res;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.application.service.IApplication2Service;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.util.JodaTimeUtil;
import com.myco.mydata.commons.util.MD5Util;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.ApplicationWrapperThrowable;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.identifier.DomainSequenceEnum;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.os.ObjectStorageBusinessDefineEntity;
import com.myco.mydata.domain.model.os.ObjectStorageOperationTypeEnum;
import com.myco.mydata.domain.model.os.ObjectStoragePolicy;
import com.myco.mydata.domain.model.os.ObjectStorageRoot;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.myco.mydata.domain.service.os.ObjectStorageRootService;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.application.action.command.cate.CateCreateCommand;
import com.xk.application.action.command.cate.CateDeletedCommand;
import com.xk.application.action.command.cate.CateUpdateCommand;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.config.application.action.command.res.*;
import com.xk.config.application.action.query.res.SysResourceGroupQuery;
import com.xk.config.application.action.query.res.SysResourceIdentifierQuery;
import com.xk.config.application.commons.XkConfigApplicationErrorEnum;
import com.xk.config.application.support.XkConfigApplicationException;
import com.xk.config.domain.commons.XkConfigSequenceEnum;
import com.xk.config.interfaces.dto.req.res.*;
import com.xk.config.interfaces.dto.rsp.res.SysResourceRspDto;
import com.xk.config.interfaces.dto.rsp.res.SysResourceSaveCorpRspDto;
import com.xk.config.interfaces.service.res.SysResourceService;
import com.xk.domain.model.os.ObjectStorageBusinessTypeEnum;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.infrastructure.commons.cate.CateSequenceEnum;
import io.github.linpeilie.Converter;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Set;
import java.util.function.Function;

/**
 * @author: killer
 **/
@Getter
@Slf4j
@Service
@RequiredArgsConstructor
public class SysResourceServiceImpl implements SysResourceService, IApplication2Service {

    private final ActionCommandDispatcher<AbstractActionCommand> actionCommandDispatcher;

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    private final ActionQueryManyDispatcher<IActionQueryMany> manyDispatcher;

    private final IdentifierGenerateService identifierGenerateService;

    private final Converter converter;

    private final ObjectStorageRootService objectStorageRootService;

    private final SelectorRootService selectorRootService;

    @BusiCode
    @Override
    public Mono<Void> save(Mono<SysResourceReqDto> sysResourceReqDtoMono) {
        return sysResourceReqDtoMono.flatMap(
                sysResourceReqDto -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
                    return identifierGenerateService
                            .generateIdentifierToMono(IdentifierRoot.builder()
                                    .identifier(XkConfigSequenceEnum.T_SYS_RESOURCE)
                                    .type(IdentifierGenerateEnum.CACHE).build())
                            .cast(Long.class).flatMap(resId -> {
                                CreateSysResourceCommand command = converter
                                        .convert(sysResourceReqDto, CreateSysResourceCommand.class);
                                command.setResId(resId.intValue());
                                command.setCreateId(userId);
                                command.setCreateTime(JodaTimeUtil.getCurrentDateTime());
                                command.setUpdateId(userId);
                                command.setUpdateTime(command.getCreateTime());
                                return actionCommandDispatcher.executeCommand(Mono.just(command),
                                        CreateSysResourceCommand.class);
                            });
                }));
    }

    @BusiCode
    @Override
    public Mono<Void> saveByCorp(Mono<SysResourceCorpReqDto> mono) {
        return mono.flatMap(
                sysResourceReqDto -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId ->
                        selectorRootService.getUserObject(userId).flatMap(userObjectRoot -> {
                            return identifierGenerateService
                                    .generateIdentifierToMono(IdentifierRoot.builder()
                                            .identifier(XkConfigSequenceEnum.T_SYS_RESOURCE)
                                            .type(IdentifierGenerateEnum.CACHE).build())
                                    .cast(Long.class).flatMap(resId -> {
                                        CreateSysResourceCommand command = converter
                                                .convert(sysResourceReqDto, CreateSysResourceCommand.class);
                                        command.setGroupId(userObjectRoot.getUserDataObjectEntity().getCorpId().intValue());
                                        command.setResId(resId.intValue());
                                        command.setCreateId(userId);
                                        command.setCreateTime(JodaTimeUtil.getCurrentDateTime());
                                        command.setUpdateId(userId);
                                        command.setUpdateTime(command.getCreateTime());
                                        return actionCommandDispatcher.executeCommand(Mono.just(command),
                                                CreateSysResourceCommand.class);
                                    });
                        })));
    }

    @BusiCode
    @Override
    public Mono<Void> update(Mono<SysResourceEditReqDto> sysResourceEditReqDtoMono) {
        return update(sysResourceEditReqDtoMono, SysResourceIdentifierQuery.class,
                SysResourceRspDto.class, EditSysResourceCommand.class);
    }

    @BusiCode
    @Override
    public Mono<Void> remove(Mono<SysResourceIdReqDto> sysResourceIdReqDtoMono) {
        return actionQueryDispatcher
                .executeQuery(sysResourceIdReqDtoMono, SysResourceIdentifierQuery.class,
                        SysResourceRspDto.class)
                .switchIfEmpty(Mono.error(
                        new ApplicationWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                .flatMap(data -> actionCommandDispatcher.executeCommand(sysResourceIdReqDtoMono,
                        DeleteSysResourceCommand.class));
    }

    @BusiCode
    @Override
    public Mono<Void> removePic(Mono<SysResourceIdsReqDto> sysResourceIdsReqDtoMono) {
        return sysResourceIdsReqDtoMono.flatMap(dto -> actionCommandDispatcher
                .executeCommand(sysResourceIdsReqDtoMono, DeleteSysPicResourceCommand.class));
    }

    @BusiCode
    @Override
    public Mono<Void> saveResCategory(Mono<UpdateResCategoryReqDto> mono) {
        return mono.flatMap(data -> {
            // 生成唯一标识符
            IdentifierRoot identifierRoot =
                    IdentifierRoot.builder().identifier(CateSequenceEnum.T_CATEGORY)
                            .type(IdentifierGenerateEnum.CACHE).build();
            Long identifier = (Long) identifierGenerateService.generateIdentifier(identifierRoot);
            data.setGroupId(identifier.intValue());
            // 执行命令操作
            return this.actionCommandDispatcher.executeCommand(Mono.just(data),
                    CateCreateCommand.class, command -> {
                        CompositeIdentifier compositeIdentifier = CompositeIdentifier.builder()
                                .groupId(Long.valueOf(data.getBusiType()))
                                .type(GroupBusinessTypeEnum.PICTURE.getValue()).id(-1L).build();
                        command.setCateId(identifier);
                        command.setCompositeIdentifiers(Set.of(compositeIdentifier));
                        return command;
                    });
        }).then();
    }

    @BusiCode
    @Override
    public Mono<Void> updateResCategory(Mono<UpdateResCategoryReqDto> mono) {
        return mono.flatMap(data -> {
            if (data.getGroupId() == null) {
                return Mono.error(
                        new XkApplicationException(XkApplicationErrorEnum.CONTENT_NOT_EXIST));
            }
            // 执行命令操作
            return this.actionCommandDispatcher.executeCommand(Mono.just(data),
                    CateUpdateCommand.class, command -> {
                        CompositeIdentifier compositeIdentifier = CompositeIdentifier.builder()
                                .groupId(Long.valueOf(data.getBusiType()))
                                .type(GroupBusinessTypeEnum.PICTURE.getValue()).id(-1L).build();
                        command.setCompositeIdentifiers(Set.of(compositeIdentifier));
                        return command;
                    });
        }).then();
    }

    @BusiCode
    @Override
    public Mono<Void> deleteResCategory(Mono<ResCategoryIdentifierDto> mono) {
        return mono.flatMap(data -> {
            if (data.getGroupId() == null) {
                return Mono.error(
                        new XkApplicationException(XkApplicationErrorEnum.CONTENT_NOT_EXIST));
            }

            Function<ResCategoryIdentifierDto, Flux<SysResourceRspDto>> resExistClass = (dto) -> {
                SysResourceGroupQuery query = SysResourceGroupQuery.builder()
                        .bizType(BusinessTypeEnum.getByValue(dto.getBusiType()))
                        .groupId(dto.getGroupId()).build();
                return this.manyDispatcher.executeQuery(Mono.just(query),
                        SysResourceGroupQuery.class, SysResourceRspDto.class);
            };

            // 执行命令操作
            Function<Boolean, Mono<Void>> executeCommand = (hasElement) -> {
                if (hasElement) {
                    return Mono.error(new XkConfigApplicationException(
                            XkConfigApplicationErrorEnum.CATEGORY_RESOURCE_EXIST));
                }

                return this.actionCommandDispatcher.executeCommand(Mono.just(data),
                        CateDeletedCommand.class, (command) -> {
                            CompositeIdentifier compositeIdentifier = CompositeIdentifier.builder()
                                    .groupId(Long.valueOf(data.getBusiType()))
                                    .type(GroupBusinessTypeEnum.PICTURE.getValue()).id(-1L).build();
                            command.setCompositeIdentifiers(Set.of(compositeIdentifier));
                            return command;
                        });
            };

            return resExistClass.apply(data).hasElements().flatMap(executeCommand);
        }).then();
    }

    @BusiCode
    @Override
    public Mono<Void> savePicCategory(Mono<PicCategorySaveReqDto> mono) {
        return mono
                .flatMap(
                        dto -> ReadSynchronizationUtils.getUserIdMono()
                                .flatMap(
                                        userId -> identifierGenerateService
                                                .generateIdentifierToMono(IdentifierRoot.builder()
                                                        .identifier(XkConfigSequenceEnum.T_SYS_RESOURCE)
                                                        .type(IdentifierGenerateEnum.CACHE).build())
                                                .cast(Long.class)
                                                .flatMap(resId -> {
                                                    // 构建基础命令对象
                                                    SavePicCategoryCommand command = new SavePicCategoryCommand();
                                                    command.setUserId(userId);
                                                    command.setPicCategoryId(dto.getPicCategoryId() != null ? dto.getPicCategoryId() : resId);
                                                    command.setCategoryName(dto.getCategoryName());
                                                    command.setParentId(dto.getParentId());
                                                    return actionCommandDispatcher.executeCommand(
                                                            Mono.just(command), SavePicCategoryCommand.class);

                                                })));
    }

    @BusiCode
    @Override
    public Mono<Void> updatePicCategory(Mono<PicCategoryUpdateReqDto> mono) {
        return mono.flatMap(dto -> ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> actionCommandDispatcher.executeCommand(mono,
                        UpdatePicCategoryCommand.class, command -> command.setUserId(userId))));
    }

    @BusiCode
    @Override
    public Mono<Void> removePicCategory(Mono<PicCategoryRemoveReqDto> mono) {
        return mono.flatMap(dto -> ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> actionCommandDispatcher.executeCommand(mono,
                        RemovePicCategoryCommand.class, command -> command.setUserId(userId))));
    }

    @BusiCode
    @Override
    public Mono<SysResourceSaveCorpRspDto> saveCorp(Mono<SysResourceSaveCorpReqDto> mono) {
        return mono.flatMap(dto -> identifierGenerateService
                .generateIdentifierToMono(
                        IdentifierRoot.builder().identifier(DomainSequenceEnum.OBJECT_STORAGE)
                                .type(IdentifierGenerateEnum.CACHE).build())
                .cast(Long.class).map(id -> {
                    ObjectStorageBusinessTypeEnum objectStorageBusinessTypeEnum =
                            ObjectStorageBusinessTypeEnum
                                    .valueOf(dto.getObjectStorageBusinessType());
                    return ObjectStorageRoot.builder()
                            .identifier(LongIdentifier.builder().id(id).build())
                            .objectStoreDefine(objectStorageBusinessTypeEnum.getObjectStoreDefine())
                            .objectStorageBusiness(ObjectStorageBusinessDefineEntity.builder()
                                    .objectStorageBusinessType(objectStorageBusinessTypeEnum)
                                    .filename(dto.getFilename()).build())
                            .operationType(ObjectStorageOperationTypeEnum.TOKEN)
                            .objectStoragePolicy(new ObjectStoragePolicy()).build();
                })
                .flatMap(objectStorageRoot -> objectStorageRootService
                        .getToken(Mono.just(objectStorageRoot))
                        .map(root -> SysResourceSaveCorpRspDto.builder()
                                .id(root.getIdentifier().id())
                                .tmpSecretId(root.getObjectStorageToken().tmpSecretId())
                                .tmpSecretKey(root.getObjectStorageToken().tmpSecretKey())
                                .sessionToken(root.getObjectStorageToken().sessionToken())
                                .expiredTime(root.getObjectStorageToken().expiredTime())
                                .bucket(root.getObjectStoreDefine().getBucket())
                                .region(root.getObjectStoreDefine().getRegion())
                                .domain(root.getObjectStoreDefine().getDomain())
                                .filePath(root.getObjectStorageBusiness().getFilePathRename())
                                .fileName(root.getObjectStorageBusiness().getFileRename())
                                .md5(MD5Util.md5_16(root.getObjectStorageToken().sessionToken()))
                                .build()))
                .flatMap(res -> ReadSynchronizationUtils.getUserObjectMono(false)
                        .flatMap(userObj -> {
                            UserDataObjectEntity objectEntity = userObj.getUserDataObjectEntity();
                            Long corpId = objectEntity.getCorpId();
                            if (corpId == null) {
                                return Mono.error(new SystemWrapperThrowable(
                                        SystemErrorEnum.UNSUPPORTED_OPERATION));
                            }
                            return identifierGenerateService
                                    .generateIdentifierToMono(IdentifierRoot.builder()
                                            .identifier(XkConfigSequenceEnum.T_SYS_RESOURCE)
                                            .type(IdentifierGenerateEnum.CACHE).build())
                                    .cast(Long.class).flatMap(resId -> {
                                        res.setResId(resId);
                                        CreateSysResourceCommand command = CreateSysResourceCommand
                                                .builder().groupId(Math.toIntExact(corpId))
                                                .resType(dto.getResType())
                                                .addr(res.getFilePath() + "/" + res.getFileName())
                                                .name(dto.getFilename())
                                                .bizType(BusinessTypeEnum.XING_KA)
                                                .createId(objectEntity.getUserId())
                                                .createTime(JodaTimeUtil.getCurrentDateTime())
                                                .updateId(objectEntity.getUserId())
                                                .updateTime(JodaTimeUtil.getCurrentDateTime())
                                                .build();
                                        command.setResId(resId.intValue());
                                        return actionCommandDispatcher
                                                .executeCommand(Mono.just(command),
                                                        CreateSysResourceCommand.class)
                                                .thenReturn(res);
                                    });
                        })));
    }
}
