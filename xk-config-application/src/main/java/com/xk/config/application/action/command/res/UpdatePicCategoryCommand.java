package com.xk.config.application.action.command.res;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.config.interfaces.dto.req.res.PicCategoryUpdateReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.*;
import com.xk.domain.model.category.Category;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = PicCategoryUpdateReqDto.class, convertGenerate = false),
        @AutoMapper(target = Category.class, reverseConvertGenerate = false)})
public class UpdatePicCategoryCommand extends AbstractActionCommand {

    private Long userId;

    private Long parentId;

    private String categoryName;

    private Long picCategoryId;

}
