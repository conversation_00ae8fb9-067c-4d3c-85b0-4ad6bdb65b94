package com.xk.config.application.query.dict;

import java.util.Date;
import java.util.List;

import org.apache.commons.math3.util.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.config.interfaces.dto.req.item.BatchItemDefineReqDto;
import com.myco.mydata.config.interfaces.dto.req.item.ItemDefineIdentifierDto;
import com.myco.mydata.config.interfaces.dto.req.item.ItemValueIdentifierReqDto;
import com.myco.mydata.config.interfaces.dto.rsp.item.ItemDefineAndValueRspDto;
import com.myco.mydata.config.interfaces.query.DictItemQueryService;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.config.enums.dict.CorpRuleTypeEnum;
import com.xk.config.interfaces.dto.req.res.DictQueryReqDto;
import com.xk.config.interfaces.dto.rsp.res.DictLogisticsCorpRspDto;
import com.xk.config.interfaces.dto.rsp.res.DictRspDto;
import com.xk.config.interfaces.dto.rsp.res.GoodsRuleRspDto;
import com.xk.config.interfaces.query.dict.DictQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DictQueryServiceImpl implements DictQueryService {

    private final DictItemQueryService dictItemQueryService;

    @Override
    public Mono<DictRspDto> search(Mono<DictQueryReqDto> dictQueryReqDtoMono) {
        return dictQueryReqDtoMono.flatMap(dictQueryReqDto -> {
            // 准备查询参数
            ItemValueIdentifierReqDto identifierReqDto = new ItemValueIdentifierReqDto();
            CorpRuleTypeEnum itemKeyEnum = CorpRuleTypeEnum.getEnum(dictQueryReqDto.getTypes().get(0));
            identifierReqDto.setItemKey(itemKeyEnum.name());
            identifierReqDto.setItemValue(String.valueOf(itemKeyEnum.getCode()));
            identifierReqDto.setLang(SystemLanguageLocale.ZH.name());
            identifierReqDto.setSessionId(dictQueryReqDto.getSessionId());
            return dictItemQueryService.getItemValueById(Mono.just(identifierReqDto)).flatMap(
                    itemValueRspDto -> {
                        DictRspDto dictRspDto = JSON.parseObject(itemValueRspDto.getItemJson(), DictRspDto.class);
                        return Mono.just(dictRspDto);
                    }
            );
        });
    }

    @Override
    public Mono<DictLogisticsCorpRspDto> searchLogisticsCorp(
            Mono<RequireSessionDto> dictQueryReqDto) {
        return dictQueryReqDto.flatMap(session -> {
            // 准备查询参数
            ItemDefineIdentifierDto identifierReqDto = new ItemDefineIdentifierDto();
            identifierReqDto.setItemKey("LOGISTICS_CORP");
            identifierReqDto.setLang(SystemLanguageLocale.ZH.name());
            BatchItemDefineReqDto batchItemDefineReqDto = new BatchItemDefineReqDto();
            batchItemDefineReqDto.setIdentifiers(List.of(identifierReqDto));
            return dictItemQueryService.getByBatchItemDefine(Mono.just(batchItemDefineReqDto))
                    .flatMap(itemValueRspDto -> {
                        ItemDefineAndValueRspDto first = itemValueRspDto.getItems().getFirst();
                        List<DictLogisticsCorpRspDto.ItemValue> list = first.getList().stream()
                                .map(x -> DictLogisticsCorpRspDto.ItemValue.builder()
                                        .label(x.getItemName()).value(x.getItemIdx()).build())
                                .toList();
                        DictLogisticsCorpRspDto rsp =
                                DictLogisticsCorpRspDto.builder().ItemValueList(list).build();
                        return Mono.just(rsp);
                    });
        });

    }

    @Override
    public Mono<GoodsRuleRspDto> searchAll(Mono<DictQueryReqDto> dictQueryReqDtoMono) {
        return dictQueryReqDtoMono.flatMap(dictQueryReqDto ->
                Flux.fromArray(CorpRuleTypeEnum.values())
                        .flatMap(corpRuleType -> {
                            ItemValueIdentifierReqDto identifierReqDto = new ItemValueIdentifierReqDto();
                            identifierReqDto.setItemKey(corpRuleType.name());
                            identifierReqDto.setItemValue(String.valueOf(corpRuleType.getCode()));
                            identifierReqDto.setLang(SystemLanguageLocale.ZH.name());
                            identifierReqDto.setSessionId(dictQueryReqDto.getSessionId());

                            return dictItemQueryService.getItemValueById(Mono.just(identifierReqDto))
                                    .map(itemValueRspDto -> {
                                        DictRspDto dictRspDto = JSON.parseObject(itemValueRspDto.getItemJson(), DictRspDto.class);
                                        return new Pair<>(corpRuleType, dictRspDto);
                                    }).defaultIfEmpty(new Pair<>(corpRuleType, new DictRspDto()));
                        })
                        .collectMap(
                                Pair::getKey,    // 以CorpRuleTypeEnum为key
                                Pair::getValue   // 以DictRspDto为value
                        )
                        .map(resultMap -> {
                            GoodsRuleRspDto response = new GoodsRuleRspDto();

                            // 根据不同类型设置相应属性
                            for (Integer type : dictQueryReqDto.getTypes()) {
                                if (type == CorpRuleTypeEnum.ANNOUNCE_INFO.getCode()) {
                                    BeanUtils.copyProperties(resultMap.get(CorpRuleTypeEnum.ANNOUNCE_INFO), response);
                                } else if (type == CorpRuleTypeEnum.GIFT_RULE.getCode()) {
                                    response.setGiftRule(resultMap.get(CorpRuleTypeEnum.GIFT_RULE).getContent());
                                } else if (type == CorpRuleTypeEnum.PURCHASE_NOTE.getCode()) {
                                    response.setPurchaseNote(resultMap.get(CorpRuleTypeEnum.PURCHASE_NOTE).getContent());
                                } else if (type == CorpRuleTypeEnum.RISK_WARNING.getCode()) {
                                    response.setRiskWarning(resultMap.get(CorpRuleTypeEnum.RISK_WARNING).getContent());
                                } else if (type == CorpRuleTypeEnum.GIFT_INFO.getCode()) {
                                    response.setGiftInfo(resultMap.get(CorpRuleTypeEnum.GIFT_INFO).getContent());
                                }
                            }
                            response.setLastUpdateTime(new Date());
                            return response;
                        })
        );
    }
}
