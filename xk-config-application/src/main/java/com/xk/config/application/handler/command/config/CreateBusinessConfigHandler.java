package com.xk.config.application.handler.command.config;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.application.action.command.config.CreateBusinessConfigCommand;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.model.config.BusinessConfigIdentifier;
import com.xk.domain.model.config.BusinessConfigRoot;
import com.xk.domain.repository.config.BusinessConfigRootQueryRepository;
import com.xk.domain.repository.config.BusinessConfigRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;


@Component
@RequiredArgsConstructor
public class CreateBusinessConfigHandler
        implements IActionCommandHandler<CreateBusinessConfigCommand, Void> {

    private final Converter converter;

    private final BusinessConfigRootRepository businessConfigRootRepository;

    private final BusinessConfigRootQueryRepository businessConfigRootQueryRepository;


    @Override
    public Mono<Void> execute(Mono<CreateBusinessConfigCommand> command) {

        return command.map(command1 -> converter.convert(command1, BusinessConfigEntity.class))
                .flatMap(entity -> businessConfigRootQueryRepository.findByParams(entity).count()
                        .flatMap(count -> {
                            if (count > 0) {
                                return Mono.error(new XkApplicationException(
                                        XkApplicationErrorEnum.CONTENT_ALREADY_EXIST));
                            } else {
                                return Mono.just(entity);
                            }
                        }))
                .flatMap(entity -> businessConfigRootRepository.save(BusinessConfigRoot.builder()
                        .identifier(BusinessConfigIdentifier.builder()
                                .businessConfigId(entity.getBusinessConfigId()).build())
                        .businessConfigEntity(entity).build()))
                .then();
    }
}
