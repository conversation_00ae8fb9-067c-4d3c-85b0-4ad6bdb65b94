package com.xk.config.application.handler.command.recommend;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.config.application.action.command.recommend.DeleteRecommendCommand;
import com.xk.config.domain.model.recommend.RecommendEntity;
import com.xk.config.domain.model.recommend.RecommendRoot;
import com.xk.config.domain.repository.recommend.RecommendRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class DeleteRecommendHandler implements IActionCommandHandler<DeleteRecommendCommand, Void> {

    private final RecommendRootRepository recommendRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<DeleteRecommendCommand> commandMono) {
        return commandMono.flatMap(command -> {
            RecommendEntity recommendEntity = converter.convert(command, RecommendEntity.class);
            LongIdentifier identifier = new LongIdentifier(recommendEntity.getRecommendAdId());
            return recommendRootRepository.remove(RecommendRoot.builder().identifier(identifier)
                    .recommendEntity(recommendEntity).build());
        });
    }
}
