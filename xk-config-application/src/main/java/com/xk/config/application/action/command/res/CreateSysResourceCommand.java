package com.xk.config.application.action.command.res;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.xk.config.interfaces.dto.req.res.SysResourceCorpReqDto;
import com.xk.config.interfaces.dto.req.res.SysResourceReqDto;
import com.xk.domain.model.res.SysResourceEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.*;

import java.util.Date;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = SysResourceReqDto.class, uses = {BusinessTypeEnum.class}, convertGenerate = false),
        @AutoMapper(target = SysResourceCorpReqDto.class, uses = {BusinessTypeEnum.class}, convertGenerate = false),
        @AutoMapper(target = SysResourceEntity.class, uses = {BusinessTypeEnum.class},
                reverseConvertGenerate = false)})
public class CreateSysResourceCommand extends AbstractSysResourceCommand {
    /**
     * 业务类型
     */
    @AutoMapping(targetClass = SysResourceReqDto.class,
            expression = "java(source.getBizType() == null ? null : source.getBizType().getValue())")
    private BusinessTypeEnum bizType;
    /**
     * 分组id 数据字典配置
     */
    private Integer groupId;

    /**
     * 资源类型：数据字典配置
     */
    private Integer resType;

    /**
     * 资源地址
     */
    private String addr;

    /**
     * 资源中文名称
     */
    private String name;

    /**
     * 资源英文名称
     */
    private String nameKey;

    /**
     * 大小 kb
     */
    private Integer size;

    /**
     * 尺寸
     */
    private String dimension;
    /**
     * 排序
     */
    private Integer idx;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 描述
     */
    private String remark;

    /**
     * 跳转类型
     */
    private Integer jumpType;
    /**
     * 跳转地址
     */
    private String jumpUrl;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 封面图
     */
    private String coverImg;
}
