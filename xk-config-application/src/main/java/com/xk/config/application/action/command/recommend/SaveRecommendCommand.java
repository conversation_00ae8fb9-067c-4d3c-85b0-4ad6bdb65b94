package com.xk.config.application.action.command.recommend;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.config.domain.model.recommend.RecommendEntity;
import com.xk.config.interfaces.dto.req.recommend.RecommendSaveReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMappers({@AutoMapper(target = RecommendSaveReqDto.class, convertGenerate = false),
        @AutoMapper(target = RecommendEntity.class, reverseConvertGenerate = false)})
public class SaveRecommendCommand extends AbstractActionCommand {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 推荐广告主键
     */
    private Long recommendAdId;

    /**
     * 广告名称
     */
    private String name;

    /**
     * 推荐类型: 1首页banner 2商城商品banner
     */
    private Integer recommendType;

    /**
     * 图片链接
     */
    private String adPic;

    /**
     * 链接
     */
    private String link;

    /**
     * 跳转类型: 1内链 2外链
     */
    private Integer jumpType;

    /**
     * 排序
     */
    private Integer sort;
}
