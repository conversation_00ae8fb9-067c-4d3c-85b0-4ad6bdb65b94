package com.xk.config.application.query.sensitive;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.application.action.query.sensitive.SensitiveConfigInfoQuery;
import com.xk.application.action.query.sensitive.SensitiveConfigSearchPagerQuery;
import com.xk.config.interfaces.query.sensitive.SensitiveQueryService;
import com.xk.interfaces.dto.req.sensitive.SensitiveConfigIdReqDto;
import com.xk.interfaces.dto.req.sensitive.SensitiveConfigReqDto;
import com.xk.interfaces.dto.rsp.sensitive.SensitiveConfigResDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/24 15:47
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SensitiveQueryServiceImpl implements SensitiveQueryService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    /**
     * 根据id敏感词
     *
     * @param dtoMono
     * @return
     */
    @Override
    @BusiCode
    public Mono<SensitiveConfigResDto> getSensitiveConfigById(Mono<SensitiveConfigIdReqDto> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, SensitiveConfigInfoQuery.class, SensitiveConfigResDto.class);
    }

    /**
     * 敏感词分页列表
     *
     * @param dtoMono
     * @return
     */
    @Override
    @BusiCode
    public Mono<Pagination> getSensitiveConfigPager(Mono<SensitiveConfigReqDto> dtoMono) {
        return queryDispatcher.executeQuery(dtoMono, SensitiveConfigSearchPagerQuery.class, Pagination.class);
    }
}
