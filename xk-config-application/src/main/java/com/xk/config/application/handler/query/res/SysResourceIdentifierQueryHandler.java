package com.xk.config.application.handler.query.res;


import org.springframework.stereotype.Component;

import com.myco.framework.common.util.CglibUtil;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.xk.config.application.action.query.res.SysResourceIdentifierQuery;
import com.xk.config.interfaces.dto.rsp.res.SysResourceRspDto;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class SysResourceIdentifierQueryHandler
        implements IActionQueryHandler<SysResourceIdentifierQuery, SysResourceRspDto> {

    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    @Override
    public Mono<SysResourceRspDto> execute(Mono<SysResourceIdentifierQuery> query) {
        return execute(query,
                sysResourceIdentifierQuery -> IntegerIdentifier.builder()
                        .id(sysResourceIdentifierQuery.getResId()).build(),
                sysResourceRootQueryRepository::findById, source -> {
                    SysResourceRspDto sysResourceRspDto = new SysResourceRspDto();
                    CglibUtil.copy(source, sysResourceRspDto);
                    return sysResourceRspDto;
                });
    }
}
