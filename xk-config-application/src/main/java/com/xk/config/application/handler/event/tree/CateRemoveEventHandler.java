package com.xk.config.application.handler.event.tree;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.domain.model.lock.RedisLockObject;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.application.action.command.tree.NodeDeletedCommand;
import com.xk.application.action.query.tree.TreeNodeQueryByComposite;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.domain.event.cate.DeletedCateEvent;
import com.xk.domain.model.tree.TreeNode;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CateRemoveEventHandler extends AbstractEventVerticle<DeletedCateEvent> {
    private final ActionCommandDispatcher<AbstractActionCommand> commandActionCommandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;
    private final LockRootService lockRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<DeletedCateEvent> event) {
        return event.flatMap(data -> {
            try {
                lockRootService.acquireTransactionNonReentrantXLock(
                        RedisLockObject.LOCKS_UPDATE_TREE, data.getCateId());
                CompositeIdentifier compositeIdentifier = CompositeIdentifier.builder()
                        .id(data.getCateId()).type(data.getGroupBusinessType()).groupId(0L).build();
                TreeNodeQueryByComposite nodeQueryByComposite = TreeNodeQueryByComposite.builder()
                        .compositeIdentifier(compositeIdentifier).build();
                return this.queryDispatcher.process(Mono.just(nodeQueryByComposite),
                        TreeNodeQueryByComposite.class, TreeNode.class).flatMap(treeNode -> {
                            NodeDeletedCommand command = NodeDeletedCommand.builder()
                                    .nodeId(treeNode.getNodeId()).build();
                            return this.commandActionCommandDispatcher.process(Mono.just(command),
                                    NodeDeletedCommand.class, Void.class);
                        });
            } catch (Throwable e) {
                return Mono
                        .error(new XkApplicationException(XkApplicationErrorEnum.GET_LOCK_ERROR));
            }
        }).then(Mono.empty());
    }
}

