package com.xk.config.application.convertor.res;

import org.springframework.stereotype.Component;

import com.xk.application.action.command.cate.CateDeletedCommand;
import com.xk.config.interfaces.dto.req.res.ResCategoryIdentifierDto;

import io.github.linpeilie.BaseMapper;

@Component
public class ResCategoryIdentifierDtoToCateDeleteCommandConvertor
        implements BaseMapper<ResCategoryIdentifierDto, CateDeletedCommand> {
    @Override
    public CateDeletedCommand convert(ResCategoryIdentifierDto dto) {
        return convert(dto, new CateDeletedCommand());
    }

    @Override
    public CateDeletedCommand convert(ResCategoryIdentifierDto dto, CateDeletedCommand command) {
        if (dto == null) {
            return null;
        }
        return CateDeletedCommand.builder().cateId(Long.valueOf(dto.getGroupId())).build();
    }
}
