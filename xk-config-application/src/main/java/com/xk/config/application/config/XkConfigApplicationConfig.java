package com.xk.config.application.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author: killer
 **/
@Configuration
@ComponentScan({"com.xk.config.application.convertor"
        , "com.xk.config.application.query"
        , "com.xk.config.application.service"
        , "com.xk.config.application.handler"
        , "com.xk.config.application.task"})
public class XkConfigApplicationConfig {
}
