package com.xk.config.application.handler.command.res;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.xk.application.action.command.cate.CateUpdateCommand;
import com.xk.application.action.query.cate.CateByBusinessQuery;
import com.xk.config.application.action.command.res.UpdatePicCategoryCommand;
import com.xk.domain.model.category.Category;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UpdatePicCategoryHandler
        implements IActionCommandHandler<UpdatePicCategoryCommand, Void> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    private final LockRootService lockRootService;

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public Mono<Void> execute(Mono<UpdatePicCategoryCommand> mono) {
        return mono.flatMap(command -> {
            CompositeIdentifier compositeIdentifier = CompositeIdentifier.builder().id(command.getPicCategoryId())
                    .type(GroupBusinessTypeEnum.PICTURE.getValue()).groupId(1L).build();
            CateByBusinessQuery cateByBusinessQuery =
                    CateByBusinessQuery.builder().compositeIdentifier(compositeIdentifier).build();

            return this.queryDispatcher.process(Mono.just(cateByBusinessQuery),
                    CateByBusinessQuery.class, Category.class).flatMap(category -> {
                        CateUpdateCommand cateCommand = CateUpdateCommand.builder()
                                .cateId(category.getCateId()).cateName(command.getCategoryName())
                                .sort(0).status(1).build();

                        return this.commandDispatcher.process(Mono.just(cateCommand),
                                CateUpdateCommand.class, Void.class);
                    });

        }).then(Mono.empty());
    }
}
