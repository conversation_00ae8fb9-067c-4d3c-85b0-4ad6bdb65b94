package com.xk.config.application.commons;

import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * application错误码
 * 13000-13999
 */
@Getter
@AllArgsConstructor
public enum XkConfigApplicationErrorEnum implements ExceptionIdentifier {

    APPLICATION_ERROR(13000, "application错误"),
    CATEGORY_RESOURCE_EXIST(13007, "分类下存在未删除的资源"),
    PIC_RESOURCE_USED(13008, "图片资源已被使用，不能删除"),
    CORP_ID_IS_NULL(13009, "该用户不是任何商户的员工，不能查询商户图片资源分类树"),

    ;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}
