package com.xk.config.application.handler.command.res;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.xk.application.action.command.cate.CateDeletedCommand;
import com.xk.application.action.query.cate.CateByBusinessQuery;
import com.xk.config.application.action.command.res.RemovePicCategoryCommand;
import com.xk.domain.model.category.Category;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RemovePicCategoryHandler
        implements IActionCommandHandler<RemovePicCategoryCommand, Void> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandActionCommandDispatcher;

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @Override
    public Mono<Void> execute(Mono<RemovePicCategoryCommand> mono) {
        return mono.flatMap(command -> {
            CompositeIdentifier compositeIdentifier = CompositeIdentifier.builder().id(command.getPicCategoryId())
                    .groupId(1L).type(GroupBusinessTypeEnum.PICTURE.getValue()).build();
            CateByBusinessQuery cateByBusinessQuery =
                    CateByBusinessQuery.builder().compositeIdentifier(compositeIdentifier).build();

            return this.queryDispatcher.process(Mono.just(cateByBusinessQuery),
                    CateByBusinessQuery.class, Category.class).flatMap(category -> {
                        CateDeletedCommand deleteCommand = CateDeletedCommand.builder()
                                .cateId(category.getCateId())
                                .compositeIdentifiers(category.getCompositeIdentifiers()).build();

                        return this.commandActionCommandDispatcher.process(Mono.just(deleteCommand),
                                CateDeletedCommand.class, Void.class);
                    });

        }).then(Mono.empty());
    }
}
