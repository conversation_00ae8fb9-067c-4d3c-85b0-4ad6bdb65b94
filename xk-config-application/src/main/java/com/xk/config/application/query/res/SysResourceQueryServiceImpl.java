package com.xk.config.application.query.res;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.CompositeIdentifier;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.application.action.query.cate.CateListByGroupQuery;
import com.xk.application.action.query.tree.TreeNodeQueryByComposite;
import com.xk.config.application.action.query.res.SysResourceGroupQuery;
import com.xk.config.application.action.query.res.SysResourceIdentifierQuery;
import com.xk.config.application.action.query.res.SysResourcePagerQuery;
import com.xk.config.application.action.query.res.SysResourcePicPagerQuery;
import com.xk.config.interfaces.dto.req.res.*;
import com.xk.config.interfaces.dto.rsp.res.PicCategoryTreeNodeDto;
import com.xk.config.interfaces.dto.rsp.res.SysResourceRspDto;
import com.xk.config.interfaces.query.res.SysResourceQueryService;
import com.xk.domain.model.category.Category;
import com.xk.domain.model.res.PicCategoryTreeNode;
import com.xk.domain.model.tag.GroupBusinessTypeEnum;
import com.xk.domain.model.tree.TreeNode;
import com.xk.domain.service.res.SysResourceRootService;
import com.xk.domain.service.tree.TreeRootService;
import com.xk.interfaces.dto.tree.TreeNodeRspDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.function.Function;
import java.util.function.LongFunction;

/**
 * @author: killer
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class SysResourceQueryServiceImpl implements SysResourceQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;

    private final SysResourceRootService sysResourceRootService;

    private final TreeRootService treeRootService;

    private final Converter converter;

    private final SelectorRootService selectorRootService;

    @BusiCode
    @Override
    public Mono<SysResourceRspDto> getById(Mono<SysResourceIdReqDto> sysResourceIdReqDtoMono) {
        return actionQueryDispatcher.executeQuery(sysResourceIdReqDtoMono,
                SysResourceIdentifierQuery.class, SysResourceRspDto.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> search(Mono<SysResourcePagerReqDto> sysResourcePagerReqDtoMono) {
        return actionQueryDispatcher.executeQuery(sysResourcePagerReqDtoMono,
                SysResourcePagerQuery.class, Pagination.class);
    }


    @BusiCode
    @Override
    public Mono<Pagination> searchPic(
            Mono<SysResourcePicPagerReqDto> sysResourcePicPagerReqDtoMono) {
        return actionQueryDispatcher.executeQuery(sysResourcePicPagerReqDtoMono,
                SysResourcePicPagerQuery.class, Pagination.class);
    }

    @BusiCode
    @Override
    public Mono<List<PicCategoryTreeNodeDto>> searchPicCateTree(Mono<PicCategoryReqDto> mono) {
        return mono.flatMap(req -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            // 1. 构建分类查询对象
            Function<PicCategoryReqDto, CateListByGroupQuery> buildCateQuery =
                    request -> CateListByGroupQuery.builder()
                            .compositeIdentifier(CompositeIdentifier.builder()
                                    .type(GroupBusinessTypeEnum.PICTURE.getValue()).groupId(1L)
                                    .build())
                            .build();

            // 2. 分类处理流水线
            Function<Category, Mono<PicCategoryTreeNode>> processCategory = category -> {
                // 2.1 构建树节点查询
                Function<Category, TreeNodeQueryByComposite> buildNodeQuery =
                        cat -> TreeNodeQueryByComposite.builder()
                                .compositeIdentifier(CompositeIdentifier.builder().groupId(0L)
                                        .type(GroupBusinessTypeEnum.CATE.getValue())
                                        .id(cat.getCateId()).build())
                                .build();

                // 2.2 树节点转换器
                Function<TreeNode, PicCategoryTreeNode> nodeConverter = treeNode -> {
                    PicCategoryTreeNode node = new PicCategoryTreeNode();
                    node.setPicCategoryId((Long) category.getCompositeIdentifiers().stream()
                            .findFirst().map(CompositeIdentifier::id).orElse(null));
                    node.setNodeId(treeNode.getNodeId());
                    node.setParentId(treeNode.getParentId());
                    node.setStatus(treeNode.getStatus());
                    node.setSort(treeNode.getSort());
                    node.setName(treeNode.getName());
                    return node;
                };

                return this.actionQueryDispatcher.process(Mono.just(buildNodeQuery.apply(category)),
                        TreeNodeQueryByComposite.class, TreeNode.class).map(nodeConverter);
            };

            // 组合函数式处理链
            return this.actionQueryManyDispatcher
                    .executeQuery(Mono.just(buildCateQuery.apply(req)), CateListByGroupQuery.class,
                            Category.class)
                    .flatMap(processCategory).collectList().flatMap(treeRootService::getTree)
                    .map(nodeList -> converter.convert(nodeList, PicCategoryTreeNodeDto.class))
                    .flatMap(dtoList -> {
                        // 调试日志：记录corpId的值
                        log.debug("PicCategoryReqDto corpId value: {}, type: {}",
                                req.getCorpId(), req.getCorpId() != null ? req.getCorpId().getClass().getSimpleName() : "null");

                        // 过滤函数：根据corpId过滤分类树节点
                        LongFunction<Mono<List<PicCategoryTreeNodeDto>>> filterByCorpId = corpId -> {
                            if (dtoList != null && !dtoList.isEmpty()) {
                                List<PicCategoryTreeNodeDto> childrenDto = dtoList.getFirst().getChildrenDto();
                                // 检查子节点列表是否为空
                                if (childrenDto == null || childrenDto.isEmpty()) {
                                    return Mono.just(List.of());
                                }

                                List<PicCategoryTreeNodeDto> userDtoList = childrenDto.stream()
                                        .filter(userDto -> userDto.getPicCategoryId() != null &&
                                                userDto.getPicCategoryId().equals(corpId))
                                        .toList();
                                return Mono.just(userDtoList);
                            }
                            return Mono.empty();
                        };


                        // 则查询用户对象获取corpId
                        if (req.getCorpId() == null) {
                            return selectorRootService.getUserObject(userId)
                                    .flatMap(userObjectRoot -> {
                                        Long corpId = userObjectRoot.getUserDataObjectEntity().getCorpId();
                                        req.setCorpId(corpId);
                                        return filterByCorpId.apply(corpId);
                                    });
                            // 如果是运营平台图片查询，则全部返回
                        } else if (req.getCorpId() == -1) {
                            return Mono.just(dtoList);
                        }

                        // 否则根据查询的商户ID进行返回
                        return filterByCorpId.apply(req.getCorpId());
                    });
        }));
    }

    @BusiCode
    @Override
    public Mono<List<SysResourceRspDto>> getByType(Mono<SysResourceGroupReqDto> mono) {
        return actionQueryManyDispatcher
                .executeQuery(mono, SysResourceGroupQuery.class, SysResourceRspDto.class)
                .collectList();
    }

    @BusiCode
    @Override
    public Mono<List<TreeNodeRspDto>> findResCateTree(Mono<CateTreeReqDto> mono) {
        return mono.flatMap(dto -> {
            CompositeIdentifier identifier =
                    CompositeIdentifier.builder().groupId(Long.valueOf(dto.getBizType()))
                            .type(GroupBusinessTypeEnum.PICTURE.getValue()).build();
            return sysResourceRootService.queryResCateTree(identifier);
        }).map(treeNodes -> converter.convert(treeNodes, TreeNodeRspDto.class));
    }

    @Override
    public Mono<List<TreeNodeRspDto>> findSeriesCateTree(Mono<CateTreeReqDto> mono) {
        return mono.flatMap(dto -> {
            CompositeIdentifier identifier =
                    CompositeIdentifier.builder().groupId(Long.valueOf(dto.getBizType()))
                            .type(GroupBusinessTypeEnum.SERIES.getValue()).build();
            return sysResourceRootService.queryResCateTree(identifier);
        }).map(treeNodes -> converter.convert(treeNodes, TreeNodeRspDto.class));
    }
}
