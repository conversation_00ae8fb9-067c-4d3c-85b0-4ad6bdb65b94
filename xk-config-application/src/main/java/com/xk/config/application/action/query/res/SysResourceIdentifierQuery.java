package com.xk.config.application.action.query.res;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.config.interfaces.dto.req.res.SysResourceEditReqDto;
import com.xk.config.interfaces.dto.req.res.SysResourceIdReqDto;
import com.xk.config.interfaces.dto.req.res.SysResourceReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: killer
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers(value = {
        @AutoMapper(target = SysResourceIdReqDto.class, convertGenerate = false),
        @AutoMapper(target = SysResourceReqDto.class, convertGenerate = false),
        @AutoMapper(target = SysResourceEditReqDto.class, convertGenerate = false)
})
public class SysResourceIdentifierQuery implements IActionQuery {
    /**
     * 资源id
     */
    private Integer resId;

}
