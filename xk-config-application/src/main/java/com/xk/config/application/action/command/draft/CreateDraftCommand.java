package com.xk.config.application.action.command.draft;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.config.domain.model.draft.entity.DraftEntity;
import com.xk.config.infrastructure.convertor.draft.DraftBusinessTypeEnumConvertor;
import com.xk.config.interfaces.dto.req.draft.DraftReqDto;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = DraftReqDto.class, convertGenerate = false),
        @AutoMapper(target = DraftEntity.class,
                uses = {CommonStatusEnumConvertor.class, DraftBusinessTypeEnumConvertor.class},
                reverseConvertGenerate = false)})
public class CreateDraftCommand extends AbstractActionCommand {

    /**
     * 草稿ID
     */
    private Long draftId;

    /**
     * 草稿类型 1-商品
     */
    private Integer draftBusinessType;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 草稿内容
     */
    private String content;

    /**
     * 创建人ID
     */
    @AutoMappings({@AutoMapping(targetClass = DraftEntity.class, target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = DraftEntity.class, target = "createValObj.createTime")})
    private Date createTime;
}
