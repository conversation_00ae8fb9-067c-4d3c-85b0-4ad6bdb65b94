package com.xk.tp.infrastructure.utils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

/**
 * 基于连接池的HttpClient工具类
 */
public class HttpClientPoolUtils {

    private static final String DEFAULT_CHARSET = "UTF-8";
    private static final int MAX_TOTAL = 200; // 最大连接数
    private static final int DEFAULT_MAX_PER_ROUTE = 50; // 每个路由默认最大连接数
    private static final int CONNECT_TIMEOUT = 5000; // 连接超时时间(毫秒)
    private static final int SOCKET_TIMEOUT = 10000; // 读取超时时间(毫秒)
    private static final int CONNECTION_REQUEST_TIMEOUT = 2000; // 从连接池获取连接超时时间(毫秒)

    private static volatile CloseableHttpClient httpClient;
    private static volatile PoolingHttpClientConnectionManager connectionManager;
    private static IdleConnectionMonitorThread idleConnectionMonitor;

    static {
        // 初始化连接池
        init();
    }

    /**
     * 初始化连接池
     */
    private static void init() {
        try {
            SSLContext sslContext =
                    new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                        @Override
                        public boolean isTrusted(X509Certificate[] chain, String authType)
                                throws CertificateException {
                            return true; // 信任所有证书
                        }
                    }).build();

            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext,
                    SSLConnectionSocketFactory.getDefaultHostnameVerifier());

            Registry<ConnectionSocketFactory> socketFactoryRegistry =
                    RegistryBuilder.<ConnectionSocketFactory>create()
                            .register("http", PlainConnectionSocketFactory.getSocketFactory())
                            .register("https", sslSocketFactory).build();

            // 初始化连接管理器
            connectionManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            // 最大连接数
            connectionManager.setMaxTotal(MAX_TOTAL);
            // 每个路由默认最大连接数
            connectionManager.setDefaultMaxPerRoute(DEFAULT_MAX_PER_ROUTE);

            // 配置请求的超时设置
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT)
                    .setSocketTimeout(SOCKET_TIMEOUT)
                    .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT).build();

            // 创建HttpClient
            httpClient = HttpClients.custom().setConnectionManager(connectionManager)
                    .setDefaultRequestConfig(requestConfig).build();

            // 启动空闲连接回收线程
            idleConnectionMonitor = new IdleConnectionMonitorThread(connectionManager);
            idleConnectionMonitor.start();
        } catch (NoSuchAlgorithmException | KeyManagementException | KeyStoreException e) {
            throw new RuntimeException("初始化HttpClient连接池失败", e);
        }
    }

    /**
     * 发送GET请求
     */
    public static String doGet(String url, Map<String, String> params,
            Map<String, String> headers) {
        return doGet(url, params, headers, DEFAULT_CHARSET);
    }

    public static String doGet(String url, Map<String, String> params, Map<String, String> headers,
            String charset) {
        if (params != null && !params.isEmpty()) {
            url = buildUrlWithParams(url, params, charset);
        }

        HttpGet httpGet = new HttpGet(url);
        return executeRequest(httpGet, headers, charset);
    }

    /**
     * 发送POST请求(表单形式)
     */
    public static String doPost(String url, Map<String, String> params,
            Map<String, String> headers) {
        return doPost(url, params, headers, DEFAULT_CHARSET);
    }

    public static String doPost(String url, Map<String, String> params, Map<String, String> headers,
            String charset) {
        HttpPost httpPost = new HttpPost(url);

        if (params != null && !params.isEmpty()) {
            List<NameValuePair> pairs = new ArrayList<>();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                pairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            try {
                httpPost.setEntity(new UrlEncodedFormEntity(pairs, charset));
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException("不支持的编码格式: " + charset, e);
            }
        }

        return executeRequest(httpPost, headers, charset);
    }

    /**
     * 发送POST请求(JSON形式)
     */
    public static String doPostJson(String url, String json, Map<String, String> headers) {
        return doPostJson(url, json, headers, DEFAULT_CHARSET);
    }

    public static String doPostJson(String url, String json, Map<String, String> headers,
            String charset) {
        HttpPost httpPost = new HttpPost(url);

        if (json != null) {
            StringEntity entity = new StringEntity(json, charset);
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
        }

        return executeRequest(httpPost, headers, charset);
    }

    /**
     * 发送PUT请求(JSON形式)
     */
    public static String doPutJson(String url, String json, Map<String, String> headers) {
        return doPutJson(url, json, headers, DEFAULT_CHARSET);
    }

    public static String doPutJson(String url, String json, Map<String, String> headers,
            String charset) {
        HttpPut httpPut = new HttpPut(url);

        if (json != null) {
            StringEntity entity = new StringEntity(json, charset);
            entity.setContentType("application/json");
            httpPut.setEntity(entity);
        }

        return executeRequest(httpPut, headers, charset);
    }

    /**
     * 发送DELETE请求
     */
    public static String doDelete(String url, Map<String, String> headers) {
        return doDelete(url, headers, DEFAULT_CHARSET);
    }

    public static String doDelete(String url, Map<String, String> headers, String charset) {
        HttpDelete httpDelete = new HttpDelete(url);
        return executeRequest(httpDelete, headers, charset);
    }

    /**
     * 执行HTTP请求
     */
    private static String executeRequest(HttpRequestBase request, Map<String, String> headers,
            String charset) {
        // 设置请求头
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                request.setHeader(entry.getKey(), entry.getValue());
            }
        }

        HttpClientContext context = HttpClientContext.create();
        try {
            HttpResponse response = httpClient.execute(request, context);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                return EntityUtils.toString(entity, charset);
            }
        } catch (IOException e) {
            throw new RuntimeException("HTTP请求执行失败", e);
        } finally {
            request.releaseConnection();
        }
        return null;
    }

    /**
     * 构建带参数的URL
     */
    private static String buildUrlWithParams(String url, Map<String, String> params,
            String charset) {
        StringBuilder sb = new StringBuilder(url);
        if (url.indexOf('?') == -1) {
            sb.append('?');
        } else {
            sb.append('&');
        }

        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (first) {
                first = false;
            } else {
                sb.append('&');
            }
            sb.append(entry.getKey()).append('=').append(entry.getValue());
        }
        return sb.toString();
    }

    /**
     * 关闭HttpClient
     */
    public static void close() {
        if (idleConnectionMonitor != null) {
            idleConnectionMonitor.shutdown();
        }

        if (connectionManager != null) {
            connectionManager.close();
        }

        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (IOException e) {
                // ignore
            }
        }
    }

    /**
     * 空闲连接回收线程
     */
    private static class IdleConnectionMonitorThread extends Thread {
        private final PoolingHttpClientConnectionManager connMgr;
        private volatile boolean shutdown;

        public IdleConnectionMonitorThread(PoolingHttpClientConnectionManager connMgr) {
            super();
            this.connMgr = connMgr;
            this.setDaemon(true);
        }

        @Override
        public void run() {
            try {
                while (!shutdown) {
                    synchronized (this) {
                        wait(5000);
                        // 关闭过期的连接
                        connMgr.closeExpiredConnections();
                        // 关闭空闲超过30秒的连接
                        connMgr.closeIdleConnections(30, TimeUnit.SECONDS);
                    }
                }
            } catch (InterruptedException ex) {
                // terminate
            }
        }

        public void shutdown() {
            shutdown = true;
            synchronized (this) {
                notifyAll();
            }
        }
    }
}
