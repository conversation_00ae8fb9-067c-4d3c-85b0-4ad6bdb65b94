package com.xk.tp.infrastructure.convertor.api.config;

import com.xk.tp.enums.pay.PayPlatformTypeEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * date 2024/07/22
 */
public class PayPlatformTypeEnumConverter {

    public static PayPlatformTypeEnum map(Integer value) {
        return PayPlatformTypeEnum.getEnumByValue(value);
    }

    public static Integer map(PayPlatformTypeEnum platformTypeEnum) {
        return Objects.isNull(platformTypeEnum) ? null : platformTypeEnum.getValue();
    }
}
