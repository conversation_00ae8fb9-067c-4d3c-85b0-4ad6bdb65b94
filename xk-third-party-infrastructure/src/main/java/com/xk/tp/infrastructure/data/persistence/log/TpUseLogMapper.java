package com.xk.tp.infrastructure.data.persistence.log;

import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.tp.infrastructure.data.po.log.TpUseLog;

import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
@Table("tp_use_log")
public interface TpUseLogMapper {
    int deleteByPrimaryKey(Long logId);

    int insert(TpUseLog record);

    int insertSelective(TpUseLog record);

    TpUseLog selectByPrimaryKey(Long logId);

    int updateByPrimaryKeySelective(TpUseLog record);

    int updateByPrimaryKey(TpUseLog record);

    List<TpUseLog> queryPager(Pagination pagination);
}