package com.xk.tp.infrastructure.service.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xk.tp.domain.commons.response.ApiPayResultData;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.CallBackResultData;
import com.xk.tp.domain.commons.response.RefundAmountData;
import com.xk.tp.enums.access.AccessPlatformTypeEnum;
import com.xk.tp.domain.model.pay.*;
import com.xk.tp.enums.pay.PayChannelTypeEnum;
import com.xk.tp.enums.pay.PayDeviceEnum;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;
import com.xk.tp.enums.pay.RefundStatusEnum;
import com.xk.tp.domain.service.pay.ApiPayService;
import com.xk.tp.infrastructure.commons.XkThirdPartyInfrastructureErrorEnum;
import com.xk.tp.infrastructure.commons.entity.ApiConfig;
import com.xk.tp.infrastructure.commons.entity.TLianCallBack;
import com.xk.tp.infrastructure.commons.entity.TLianPay;
import com.xk.tp.infrastructure.commons.security.api.SybUtil;
import com.xk.tp.infrastructure.commons.util.BeanUtil;
import com.xk.tp.infrastructure.support.XkThirdPartyInfrastructureException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2024/07/17
 */
@Slf4j
@Service(TLianPayServiceImpl.BEAN_NAME)
@RequiredArgsConstructor
public class TLianPayServiceImpl implements ApiPayService {

    public static final String BEAN_NAME = "tLianPayService";


    private final RestClient restClient;

    public static void main(String[] args) throws Exception {


        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("cusid", "56333107392QDRD"); //商户号
        body.add("appid", "********"); //应用ID
        body.add("trxamt", "1"); //交易金额,单位为分
        body.add("reqsn", "1"); //商户退款订单号
        body.add("oldreqsn", "212501041"); //原交易订单号
        body.add("oldtrxid", "250104124912772640"); //原交易的收银宝平台流水
        body.add("remark", "售后退款"); //原交易的收银宝平台流水
        body.add("signtype", "RSA");  //签名方式:RSA、SM2
        TreeMap<String, String> treeMap = new TreeMap<>();
        for (String key : body.keySet()) {
            treeMap.put(key, body.getFirst(key));
        }
        body.add("sign", SybUtil.unionSign(treeMap,
                "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCzs8jj6CDgyA56L3Kn6KeFgj+J+jzKSOziRsZYiCWeCGzTqSqcqyl/2qBKtT73OemLdeZ88NTy2XkG3ODEO3HwYjQS3/x0+Qx+zG1fQNyX4DhXncKGF44KG3AwCx9/evAighS6OFn02M71po43yZSQ4OAuobty2n3/vJricoCySYzVsHV3ccrS6gqVluHd6xGc3QrYPT8iSoB9uZY59sHh1I5JYCz/Gz52Nc3leaUMzw2azAKEkiRUtWAGmtS6uqwq9xwlehmYaRpR/zR8Dcg55Vz/NcNw8dJAQRM+aLQxvgBLm2DVgP779Hcnwg7JNnUesUeXHZuu0dn7R221PbTjAgMBAAECggEAKqQLlEDdpFj0Tnv5yewD7EM2zofJavqY8yx2HkXYH0LCiAsJKHnY9BStmmydj053o3cLhTQkY6fE9yLsVsQASlkTIDN0FVGPbHsXrNWYQpWa6PdvVCA8vRs7qLrK85X5a0bQokuC1P2pRlMKRiSYEzS5HXeVIjDEWy2m1vrynNs/PFfiOJSp7lyqu/B+mO/boBT9SUw+kTmskIQFEzT2RU5h0O3/f0GRBTxoN8aXrZRV8GwrUgPnk9G+J/7GGw90y2O5b6Chhgq9vhTaHcL630n3ftNe5QAWkoHANQ4FPZlSQ9Z+9oQKgayslfgG2AMY0YUWoWopsWIDacpznWi64QKBgQDXj2lSKis5pwl0A4V1XsbNA6dPatKjOODV1IgOPqrH/OKekSJrcAVpHJCZ6pG/1FABpUYaImwfGsz8DLbWJ5tzyoh4EBer6NK1VJTpOWe13sae7tOS/aGg7tDQoWs35OHK57t0RuO+Ei2C5i5nazu2ehe13due+G643323aKZWswKBgQDVaj7MgbmxkXARrfvZjNBmxkXK/uYGztsLLJ2+DjHLNCpujdJiVrfuB6hfa5Mb2ujnsoVm0x+OvM7bwfhwQ4TkRmnrUzshmqS8hWXQJc5HIOnJsIcaAbWjZ/xwQ/XK/qPgwa3M92UE5cYgcimTco7Spos+rmVxx+yOohpzjFfBEQKBgQCUUgNgG8/ePYcNopaj6fDUhB0NUi1ZTfu2ZiSv2ILeVK0B+k86qbuvVY5RuctLuSTL3RLdTK4kwOYm0ADGXj5icAtywW/oQ60oQ+iqiWtJAt6b4S0HSSl7eJeJ1mfV79FFMioTmet+jzdtiECJvIk7x8qkwuEpAzv8TRtNpd4+bQKBgQDUalFjH+OBt66CClfYKu3qvYIOEDNHp3Ah6SCAYIRzKtLpAsPaGVIs2vZdMydS3EphAzEALTcFzStfU+tBYhLNWsBl94utYMyCz4uM53QvG7VWiBuQaQ8vO3rpCtuEVnsqY9vnXH3xp/nRpY94MNezFw0VMGPMIh9zwCjvxUo64QKBgQCGrgVpATMNHizZqlT6W9MJ/j9sccmAv8AI5RqTAms+S57QkSBP9JgmFpQjdIbj8LpBx01yaDW9fKoIuPBpKk3kfRVSzNWn6tkpDaEo8NORj73Q2EuxREs0ILVgutBUq+qnMP9ECBIwucHEbXeVpY+BKhr8f1HiIdrsX7mvxBOiyw==",
                "RSA"));


        TLianPay tLianPay = new TLianPay();
        tLianPay.setCusId("56333107392QDRD");
        tLianPay.setAppId("********");
        tLianPay.setAppKey(
                "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCzs8jj6CDgyA56L3Kn6KeFgj+J+jzKSOziRsZYiCWeCGzTqSqcqyl/2qBKtT73OemLdeZ88NTy2XkG3ODEO3HwYjQS3/x0+Qx+zG1fQNyX4DhXncKGF44KG3AwCx9/evAighS6OFn02M71po43yZSQ4OAuobty2n3/vJricoCySYzVsHV3ccrS6gqVluHd6xGc3QrYPT8iSoB9uZY59sHh1I5JYCz/Gz52Nc3leaUMzw2azAKEkiRUtWAGmtS6uqwq9xwlehmYaRpR/zR8Dcg55Vz/NcNw8dJAQRM+aLQxvgBLm2DVgP779Hcnwg7JNnUesUeXHZuu0dn7R221PbTjAgMBAAECggEAKqQLlEDdpFj0Tnv5yewD7EM2zofJavqY8yx2HkXYH0LCiAsJKHnY9BStmmydj053o3cLhTQkY6fE9yLsVsQASlkTIDN0FVGPbHsXrNWYQpWa6PdvVCA8vRs7qLrK85X5a0bQokuC1P2pRlMKRiSYEzS5HXeVIjDEWy2m1vrynNs/PFfiOJSp7lyqu/B+mO/boBT9SUw+kTmskIQFEzT2RU5h0O3/f0GRBTxoN8aXrZRV8GwrUgPnk9G+J/7GGw90y2O5b6Chhgq9vhTaHcL630n3ftNe5QAWkoHANQ4FPZlSQ9Z+9oQKgayslfgG2AMY0YUWoWopsWIDacpznWi64QKBgQDXj2lSKis5pwl0A4V1XsbNA6dPatKjOODV1IgOPqrH/OKekSJrcAVpHJCZ6pG/1FABpUYaImwfGsz8DLbWJ5tzyoh4EBer6NK1VJTpOWe13sae7tOS/aGg7tDQoWs35OHK57t0RuO+Ei2C5i5nazu2ehe13due+G643323aKZWswKBgQDVaj7MgbmxkXARrfvZjNBmxkXK/uYGztsLLJ2+DjHLNCpujdJiVrfuB6hfa5Mb2ujnsoVm0x+OvM7bwfhwQ4TkRmnrUzshmqS8hWXQJc5HIOnJsIcaAbWjZ/xwQ/XK/qPgwa3M92UE5cYgcimTco7Spos+rmVxx+yOohpzjFfBEQKBgQCUUgNgG8/ePYcNopaj6fDUhB0NUi1ZTfu2ZiSv2ILeVK0B+k86qbuvVY5RuctLuSTL3RLdTK4kwOYm0ADGXj5icAtywW/oQ60oQ+iqiWtJAt6b4S0HSSl7eJeJ1mfV79FFMioTmet+jzdtiECJvIk7x8qkwuEpAzv8TRtNpd4+bQKBgQDUalFjH+OBt66CClfYKu3qvYIOEDNHp3Ah6SCAYIRzKtLpAsPaGVIs2vZdMydS3EphAzEALTcFzStfU+tBYhLNWsBl94utYMyCz4uM53QvG7VWiBuQaQ8vO3rpCtuEVnsqY9vnXH3xp/nRpY94MNezFw0VMGPMIh9zwCjvxUo64QKBgQCGrgVpATMNHizZqlT6W9MJ/j9sccmAv8AI5RqTAms+S57QkSBP9JgmFpQjdIbj8LpBx01yaDW9fKoIuPBpKk3kfRVSzNWn6tkpDaEo8NORj73Q2EuxREs0ILVgutBUq+qnMP9ECBIwucHEbXeVpY+BKhr8f1HiIdrsX7mvxBOiyw==");
        tLianPay.setPublicKey(
                "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCm9OV6zH5DYH/ZnAVYHscEELdCNfNTHGuBv1nYYEY9FrOzE0/4kLl9f7Y9dkWHlc2ocDwbrFSm0Vqz0q2rJPxXUYBCQl5yW3jzuKSXif7q1yOwkFVtJXvuhf5WRy+1X5FOFoMvS7538No0RpnLzmNi3ktmiqmhpcY/1pmt20FHQQIDAQAB");


        // 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 创建请求实体
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(body, headers);

        // 发送 POST 请求
        String url = "https://vsp.allinpay.com/apiweb/tranx/refund";
        ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, String.class);

        System.out.println("111");
        // 返回响应

/*
        Map<String, String> stringStringMap = decodeUrlEncodedParams("acct=opn0buOg776BZS5KZ7knY6CAiiCE&accttype=99&appid=********&bankcode=OTHERS&chnlid=*********&chnltrxid=4200002427********8096006308&cmid=*********&cusid=56333107392QDRD&cusorderid=*****************&fee=0&initamt=1&outtrxid=*****************&paytime=********174552&sign=EWvMgjEV%2FTZPg0uCtWyz8MB80z0QsaVJRL42ZqCigKTnLNyZt9yAIJ6cuUgb11jjZYo2nFRNjMG4zD3eQbHap%2Bpfp30YjpWIXlv7NSZKfgXiceE2%2BxbRnsRuPWdfQPearguHu26t%2BKqwz1tc23lkl2uNKid1GrKYmqls4OgqaMU%3D&signtype=RSA&termauthno=OTHERS&termrefnum=4200002427********8096006308&termtraceno=0&trxamt=1&trxcode=VSP501&trxdate=********&trxid=240730127494436381&trxstatus=0000");





        String content = "appid=********&cusid=56333107392QDRD&payinfo=https://syb.allinpay.com/apiweb/h5unionpay/native?key=ANeLKDUwf%2F3JKMSgeCJG2ZI1&randomstr=************&reqsn=****************&retcode=SUCCESS&trxid=240729121194313267&trxstatus=0000";
        String sign = "LhjNxw9TJeZDubHi73+jTLA6uevPAcHKLy9DRO41iOPppKhcuJxwNhK2G1NZOzJudoLDTL4F8A/9Qchf+yAARhGIHv84wC8/G4Durr51KI5K9Mz9tdlF20aPc8ZotX6QphLkpsSzhthuKTl6WkeEQxl4HBoVzxLKANn2PPKa9I8=";

        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCm9OV6zH5DYH/ZnAVYHscEELdCNfNTHGuBv1nYYEY9FrOzE0/4kLl9f7Y9dkWHlc2ocDwbrFSm0Vqz0q2rJPxXUYBCQl5yW3jzuKSXif7q1yOwkFVtJXvuhf5WRy+1X5FOFoMvS7538No0RpnLzmNi3ktmiqmhpcY/1pmt20FHQQIDAQAB";


        //验签
        TreeMap<String, String> param = new TreeMap<>();
        for (String key : stringStringMap.keySet()) {
            param.put(key,stringStringMap.get(key));
        }
        boolean b1 = SybUtil.validSign(param, publicKey, "RSA");

//        boolean b = SybUtil.rsaVerifyPublickey(content, sign, publicKey, "utf-8");


//        boolean b = AlipaySignature.rsaCheckV1(stringStringMap, publicKey,
//                "utf-8", "RSA");

        System.out.println(b1);*/
    }

    private static Map<String, String> decodeUrlEncodedParams(String urlEncodedString) {
        // 使用Java的URLDecoder解码URL编码的字符串
        String decodedString = URLDecoder.decode(urlEncodedString, StandardCharsets.UTF_8);

        // 将解码后的字符串转换为Map
        return Arrays.stream(decodedString.split("&"))
                .map(s -> s.split("="))
                .collect(Collectors.toMap(split -> split[0], split -> split.length > 1 ? split[1] : ""));
    }

    @Override
    public Integer getPayType() {
        return AccessPlatformTypeEnum.TLIAN.getValue();
    }

    @Override
    public ApiResult<ApiPayResultData> preCreate(PayRoot apiPayRoot) {

        try {
            PreCreateEntity preCreateEntity = apiPayRoot.getPreCreateEntity();
            PayPlatformTypeEntity platformTypeEntity = apiPayRoot.getPayPlatformTypeEntity();
            ApiConfig tLianPay = BeanUtil.mapTo(platformTypeEntity.getConfig(), ApiConfig.class);
            log.info("通联支付预下单 dto[{}] config [{}]", preCreateEntity, tLianPay);
            ResponseEntity<String> entity = restClient.post()
                    .uri("https://vsp.allinpay.com/apiweb/unitorder/pay")
                    .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                    .body(generatePreCreateBody(apiPayRoot, tLianPay))
                    .retrieve()
                    .toEntity(String.class);
            return parsePreCreateResponse(entity, tLianPay);
        } catch (XkThirdPartyInfrastructureException e) {
            log.error("通联支付预下单类型错误，e", e);
            return ApiResult.error();
        } catch (Exception e) {
            log.error("通联支付预下单异常，e", e);
            return ApiResult.error();
        }
    }

    @Override
    public ApiResult<RefundAmountData> refundOrder(PayRoot apiPayRoot) {
        try {
            RefundOrderEntity refundOrderEntity = apiPayRoot.getRefundOrderEntity();
            PayPlatformTypeEntity platformTypeEntity = apiPayRoot.getPayPlatformTypeEntity();
            TLianPay tLianPay = BeanUtil.mapTo(platformTypeEntity.getConfig(), TLianPay.class);
            log.info("通联支付退款申请 dto[{}] config[{}]", refundOrderEntity, tLianPay);
            ResponseEntity<String> entity = restClient.post()
                    .uri("https://vsp.allinpay.com/apiweb/tranx/refund")
                    .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                    .body(generateRefundOrderBody(apiPayRoot, tLianPay))
                    .retrieve()
                    .toEntity(String.class);
            return parseRefundOrderResponse(entity);
        } catch (Exception e) {
            log.error("通联支付退款申请异常 e", e);
            return ApiResult.error();
        }
    }

    @Override
    public ApiResult revokeOrder(PayRoot apiPayRoot) {
        try {
            RevokeOrderEntity revokeOrderEntity = apiPayRoot.getRevokeOrderEntity();
            PayPlatformTypeEntity platformTypeEntity = apiPayRoot.getPayPlatformTypeEntity();
            TLianPay tLianPay = BeanUtil.mapTo(platformTypeEntity.getConfig(), TLianPay.class);
            log.info("通联支付撤销订单 dto[{}] config[{}]", revokeOrderEntity, tLianPay);
            ResponseEntity<String> entity = restClient.post()
                    .uri("https://vsp.allinpay.com/apiweb/tranx/cancel")
                    .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                    .body(generateRevokeOrderBody(revokeOrderEntity, tLianPay))
                    .retrieve()
                    .toEntity(String.class);
            return parseRevokeOrderResponse(entity);
        } catch (Exception e) {
            log.error("通联支付撤销订单异常 e", e);
            return ApiResult.error();
        }
    }

    @Override
    public ApiResult<Boolean> findPayResult(PayRoot apiPayRoot) {
        return null;
    }

    @Override
    public ApiResult<CallBackResultData> callBack(PayRoot apiPayRoot) {
        TLianCallBack tLianDto = null;
        try {
            CallBackValObj callBackValObj = apiPayRoot.getCallBackValObj();
            PayPlatformTypeEntity platformTypeEntity = apiPayRoot.getPayPlatformTypeEntity();
            TLianPay tLianPay = BeanUtil.mapTo(platformTypeEntity.getConfig(), TLianPay.class);
            log.info("通联支付回调 dto [{}] config [{}]", callBackValObj, tLianPay);
            tLianDto = parserCallBackRequest(callBackValObj, tLianPay);
        } catch (Exception e) {
            log.error("通联支付回调参数解析异常 e", e);
            return ApiResult.error();
        }
        log.info("通联支付回调 dto[{}]", tLianDto);

        return parserCallBackDto(tLianDto);
    }

    @Override
    public ApiResult<RefundAmountData> refundCallBack(PayRoot apiPayRoot) {
        //不支持
        return ApiResult.success();
    }

    @Override
    public ApiResult<Void> closeOrder(PayRoot apiPayRoot) {
        try {
            CloseOrderEntity closeOrderEntity = apiPayRoot.getCloseOrderEntity();
            PayPlatformTypeEntity platformTypeEntity = apiPayRoot.getPayPlatformTypeEntity();
            TLianPay tLianPay = BeanUtil.mapTo(platformTypeEntity.getConfig(), TLianPay.class);
            log.info("通联支付关闭订单 dto[{}] config [{}]", closeOrderEntity, tLianPay);
            ResponseEntity<String> entity = restClient.post()
                    .uri("https://vsp.allinpay.com/apiweb/tranx/close")
                    .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                    .body(generateCloseOrderBody(closeOrderEntity, tLianPay))
                    .retrieve()
                    .toEntity(String.class);
            return parseCloseOrderResponse(entity);
        } catch (Exception e) {
            log.error("通联支付关闭订单异常 e", e);
            return ApiResult.error();
        }
    }

    private MultiValueMap<String, String> generatePreCreateBody(PayRoot apiPayRoot, ApiConfig tLianPay)
            throws Exception, XkThirdPartyInfrastructureException {
        PreCreateEntity preCreateEntity = apiPayRoot.getPreCreateEntity();
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("cusid", tLianPay.getCusId()); //商户号
        body.add("appid", tLianPay.getAppId()); //应用ID
        body.add("trxamt", apiPayRoot.converPrice(preCreateEntity.getAmount())); //交易金额,单位为分
        body.add("reqsn", preCreateEntity.getOrderNo()); //商户交易单号
        body.add("validtime", tLianPay.getExpireTime().toString());

        //自定义参数
//        JSONObject extendparams = new JSONObject();
//        extendparams.put("encryData",preCreateEntity.getEncryData());
//        body.add("extendparams",extendparams.toJSONString());

        String payType = null;
        PayChannelTypeEnum channelType = apiPayRoot.getPayPlatformTypeEntity().getChannelType();
        if (channelType == PayChannelTypeEnum.ali) {
            if (preCreateEntity.getDevice().equals(PayDeviceEnum.pc.getValue())) {
                //pc支付
                payType = "A01";
            } else if (preCreateEntity.getDevice().equals(PayDeviceEnum.h5.getValue())) {
                //h5支付
                payType = "A01";
            } else if (preCreateEntity.getDevice().equals(PayDeviceEnum.app.getValue())) {
                //app支付
                payType = "A01";
            }
        } else if (channelType == PayChannelTypeEnum.weixin) {
            if (preCreateEntity.getDevice().equals(PayDeviceEnum.pc.getValue())) {
                //pc支付
                payType = "W01";
            } else if (preCreateEntity.getDevice().equals(PayDeviceEnum.h5.getValue())) {
                //h5支付
            } else if (preCreateEntity.getDevice().equals(PayDeviceEnum.app.getValue())) {
                //app支付
                payType = "W03";
            }
        }
        if (StringUtils.isBlank(payType)) {
            throw new XkThirdPartyInfrastructureException(XkThirdPartyInfrastructureErrorEnum.PAY_ERROR);
        }
        body.add("paytype", payType); //交易方式:W01-微信扫码支付 、A01-支付宝扫码支付
        body.add("randomstr", RandomStringUtils.randomAlphanumeric(15)); //随机字符串
        body.add("body", preCreateEntity.getDescription()); //订单标题
        body.add("notify_url", tLianPay.getNotifyUrl() + "/" + apiPayRoot.getIdentifier()
                .getAccessAccountId() + "/" + preCreateEntity.getDevice());
        body.add("signtype", "RSA");  //签名方式:RSA、SM2
        TreeMap<String, String> treeMap = new TreeMap<>();
        for (String key : body.keySet()) {
            treeMap.put(key, body.getFirst(key));
        }
        body.add("sign", SybUtil.unionSign(treeMap, tLianPay.getAppKey(), "RSA"));
        return body;
    }

    private ApiResult<ApiPayResultData> parsePreCreateResponse(ResponseEntity<String> entity, ApiConfig tLianPay)
            throws Exception {
        JSONObject resp = JSON.parseObject(entity.getBody());
        log.info("通联支付预下单 response [{}]", resp);
        if (Objects.isNull(resp)) {
            log.error("通联支付预下单失败，请求响应为空");
            return ApiResult.error();
        }
        if (!resp.get("retcode").equals("SUCCESS")) {
            log.error(String.format("通联支付预下单失败，code:%s, msg:%s", resp.get("retcode"), resp.get("retmsg")));
            return ApiResult.error();
        }
        if (!resp.get("trxstatus").equals("0000")) {
            log.error(String.format("通联支付预下单失败，trxstatus:%s, errmsg:%s", resp.get("trxstatus"),
                    resp.get("errmsg")));
            return ApiResult.error();
        }

        //验签
        TreeMap<String, String> param = new TreeMap<>();
        for (String key : resp.keySet()) {
            param.put(key, resp.getString(key));
        }
        if (!SybUtil.validSign(param, tLianPay.getPublicKey(), "RSA")) {
            log.error("通联支付预下单失败,验签失败");
            return ApiResult.error();
        }
        Object payinfo = resp.get("payinfo");
        if (payinfo == null || StringUtils.isBlank(payinfo.toString())) {
            log.error("通联支付预下单失败，payinfo为空");
            return ApiResult.error();
        }

        return ApiResult.success(ApiPayResultData.builder()
                .url(payinfo.toString()).payNo(resp.getString("trxid")).expireTime(tLianPay.getExpireTime()).build());
    }

    private MultiValueMap<String, String> generateRefundOrderBody(PayRoot apiPayRoot, TLianPay tLianPay)
            throws Exception {
        RefundOrderEntity refundOrderEntity = apiPayRoot.getRefundOrderEntity();
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("cusid", tLianPay.getCusId()); //商户号
        body.add("appid", tLianPay.getAppId()); //应用ID
        body.add("trxamt", apiPayRoot.converPrice(refundOrderEntity.getRefundAmount())); //交易金额,单位为分
        body.add("reqsn", refundOrderEntity.getRefundOrderId()); //商户退款订单号
        body.add("oldreqsn", refundOrderEntity.getOrderNo()); //原交易订单号
        body.add("oldtrxid", refundOrderEntity.getPayNo()); //原交易的收银宝平台流水
        body.add("remark", "售后退款"); //原交易的收银宝平台流水
        body.add("signtype", "RSA");  //签名方式:RSA、SM2
        TreeMap<String, String> treeMap = new TreeMap<>();
        for (String key : body.keySet()) {
            treeMap.put(key, body.getFirst(key));
        }
        body.add("sign", SybUtil.unionSign(treeMap, tLianPay.getAppKey(), "RSA"));
        return body;
    }

    private ApiResult<RefundAmountData> parseRefundOrderResponse(ResponseEntity<String> entity) {
        JSONObject resp = JSON.parseObject(entity.getBody());
        log.info("通联支付退款申请 response [{}]", resp);
        if (Objects.isNull(resp)) return ApiResult.error("通联支付退款申请失败，请求响应为空");
        if (!resp.getString("retcode").equals("SUCCESS"))
            return ApiResult.error(String.format("通联支付退款申请失败，code:%s, msg:%s", resp.getString("retcode"),
                    resp.getString("retmsg")));
        if (!resp.getString("trxstatus").equals("0000"))
            return ApiResult.error(
                    String.format("通联支付退款申请失败, trxstatus:%s, errmsg:%s", resp.getString("trxstatus"),
                            resp.getString("errmsg")));
        return ApiResult.success(RefundAmountData.builder()
                .refundNo(resp.getString("trxid"))
                .refundOrderId(resp.getString("reqsn"))
                .refundStatus(RefundStatusEnum.SUCCESS.getStatus())
                .build());
    }

    private MultiValueMap<String, String> generateRevokeOrderBody(RevokeOrderEntity revokeOrderEntity,
                                                                  TLianPay tLianPay) throws Exception {
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("cusid", tLianPay.getCusId());
        body.add("appid", tLianPay.getAppId());
        body.add("reqsn", revokeOrderEntity.getRefundOrderNo());
        body.add("trxamt", revokeOrderEntity.getTotalAmount()
                .multiply(new BigDecimal("100"))
                .stripTrailingZeros()
                .toPlainString()); //单位分
        body.add("oldreqsn", revokeOrderEntity.getOrderNo());
        body.add("randomstr", RandomStringUtils.randomAlphanumeric(15));
        body.add("signtype", "RSA");
        TreeMap<String, String> treeMap = new TreeMap<>();
        for (String key : body.keySet()) {
            treeMap.put(key, body.getFirst(key));
        }
        body.add("sign", SybUtil.unionSign(treeMap, tLianPay.getAppKey(), "RSA"));
        return body;
    }

    private ApiResult parseRevokeOrderResponse(ResponseEntity<String> entity) {
        JSONObject resp = JSON.parseObject(entity.getBody());
        log.info("通联支付撤销订单 response [{}]", resp);
        if (Objects.isNull(resp)) return ApiResult.error("通联支付撤销订单失败，请求响应为空");
        if (!resp.getString("retcode").equals("SUCCESS"))
            return ApiResult.error(String.format("通联支付撤销订单失败， code:%s, msg:%s", resp.getString("retcode"),
                    resp.getString("retmsg")));
        if (!resp.getString("trxstatus").equals("0000"))
            return ApiResult.error(
                    String.format("通联支付撤销订单失败，trxstatus:%s, errmsg:%s", resp.getString("trxstatus"),
                            resp.getString("errmsg")));
        return ApiResult.success();
    }

    private MultiValueMap<String, String> generateCloseOrderBody(CloseOrderEntity closeOrderEntity, TLianPay tLianPay)
            throws Exception {
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        //body.add("orgid", dto.getOrgid()); 该参数暂时不用
        body.add("cusid", tLianPay.getCusId());
        body.add("appid", tLianPay.getAppId());
        body.add("oldreqsn", closeOrderEntity.getOrderNo());
        body.add("randomstr", RandomStringUtils.randomAlphanumeric(15));
        body.add("signtype", "RSA");
        TreeMap<String, String> treeMap = new TreeMap<>();
        for (String key : body.keySet()) {
            treeMap.put(key, body.getFirst(key));
        }
        body.add("sign", SybUtil.unionSign(treeMap, tLianPay.getAppKey(), "RSA"));
        return body;
    }

    private ApiResult<Void> parseCloseOrderResponse(ResponseEntity<String> entity) {
        JSONObject resp = JSON.parseObject(entity.getBody());
        log.info("通联支付关闭订单 response [{}]", resp);
//        if (Objects.isNull(resp)) return ApiResult.error("通联支付关闭订单失败，请求响应为空");
//        if (!resp.getString("retcode").equals("SUCCESS"))
//            return ApiResult.error(String.format("通联支付关闭订单失败， code:%s, msg:%s", resp.getString("retcode"), resp.getString("retmsg")));
//        if (!resp.getString("trxstatus").equals("0000"))
//            return ApiResult.error(String.format("通联支付关闭订单失败， trxstatus:%s, errmsg:%s", resp.getString("trxstatus"), resp.getString("errmsg")));
        return ApiResult.success();
    }

    private TLianCallBack parserCallBackRequest(CallBackValObj callBackValObj, TLianPay tLianPay) throws Exception {
        Map<String, String> requestParam = callBackValObj.getRequestParam();
        TLianCallBack tLianCallBack = BeanUtil.mapTo(requestParam, TLianCallBack.class);
        TreeMap<String, String> treeMap = new TreeMap<>();
        for (String key : requestParam.keySet()) {
            treeMap.put(key, requestParam.get(key));
        }
        tLianCallBack.setDevice(callBackValObj.getDevice());
        boolean isSignCorrect = SybUtil.validSign(treeMap, tLianPay.getPublicKey(), tLianCallBack.getSigntype());
        if (!isSignCorrect) throw new RuntimeException("通联支付回调，验签失败 isSignCorrect: " + isSignCorrect);
        return tLianCallBack;
    }

    private ApiResult<CallBackResultData> parserCallBackDto(TLianCallBack tLianDto) {
        if (Objects.isNull(tLianDto)) return ApiResult.error("通联支付回调通知，请求参数为空");
        if (!tLianDto.getTrxstatus().equals("0000"))
            return ApiResult.error(String.format("通联支付回调通知失败，trxstatus:%s", tLianDto.getTrxstatus()));
        if (StringUtils.isBlank(tLianDto.getCusorderid()))
            return ApiResult.error("通联支付回调通知失败，cusorderid为空");

        long amount = 0L;
        if (!StringUtils.isBlank(tLianDto.getInitamt())) {
            //计算
            amount = new BigDecimal(tLianDto.getInitamt()).multiply(
                    BigDecimal.valueOf(Math.pow(10, PayPlatformTypeEnum.TLIAN.getBit()))).longValue();
        }

        return ApiResult.success(CallBackResultData.builder()
                .orderNo(tLianDto.getCusorderid())
                .payNo(tLianDto.getTrxid())
                .orderStatus("0000".equals(tLianDto.getTrxstatus()) ? 1 : 2)
                .tradeStatus(tLianDto.getTrxstatus())
                .amount(amount)
                .device(tLianDto.getDevice())
                .build());
    }
}
