package com.xk.tp.infrastructure.adapter.logistics;


import java.util.ArrayList;
import java.util.HashMap;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson2.JSONObject;
import com.myco.mydata.commons.util.MD5Util;
import com.xk.tp.domain.model.logistics.LogisticsDetailValObj;
import com.xk.tp.domain.model.logistics.LogisticsRoot;
import com.xk.tp.domain.service.logistics.LogisticsAdapterService;
import com.xk.tp.enums.logistics.LogisticsApiEnum;
import com.xk.tp.enums.logistics.LogisticsCorpEnum;
import com.xk.tp.infrastructure.commons.entity.KuaiDiRspDto;
import com.xk.tp.infrastructure.utils.HttpClientPoolUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class KuaiDi100LogisticsAdapterServiceImpl implements LogisticsAdapterService {

    private final String url = "https://poll.kuaidi100.com";
    private final String queryPoll = "/poll/query.do";
    private final String customer = "********************************";
    private final String key = "MslJFwgY8470";
    private final String lang = "zh";

    @Override
    public Integer getAccountType() {
        return LogisticsApiEnum.KUAI_DI_100.getCode();
    }

    @Override
    public Flux<LogisticsDetailValObj> detail(Mono<LogisticsRoot> queryMono) {
        return queryMono.flatMapMany(logisticsRoot -> {
            HashMap<String, String> reqMap = new HashMap<>();
            reqMap.put("customer", customer);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("com",
                    LogisticsCorpEnum
                            .getByCode(
                                    logisticsRoot.getLogisticsCorpEntity().getLogisticsCorpName())
                            .getMsg());
            jsonObject.put("num", logisticsRoot.getLogisticsCorpEntity().getLogisticsNo());
            jsonObject.put("lang", lang);
            jsonObject.put("phone", logisticsRoot.getLogisticsCorpEntity().getReceivingMobile());

            String param = jsonObject.toString();
            reqMap.put("param", param);
            reqMap.put("sign",
                    MD5Util.md5(String.format("%s%s%s", param, key, customer)).toUpperCase());

            String data =
                    HttpClientPoolUtils.doPost(String.format("%s%s", url, queryPoll), reqMap, null);
            KuaiDiRspDto kuaiDiRspDto = JSONObject.parseObject(data, KuaiDiRspDto.class);
            if (kuaiDiRspDto.getResult() != null && !kuaiDiRspDto.getResult()) {
                return Flux.empty();
            }

            ArrayList<LogisticsDetailValObj> valObjArrayList = new ArrayList<>();
            kuaiDiRspDto.getData().forEach(logisticsMsg -> {
                LogisticsDetailValObj logisticsDetailValObj =
                        LogisticsDetailValObj.builder().trackDescribe(logisticsMsg.getContext())
                                .trackTime(logisticsMsg.getTime()).build();
                valObjArrayList.add(logisticsDetailValObj);
            });
            return CollectionUtils.isEmpty(valObjArrayList) ? Flux.empty()
                    : Flux.fromIterable(valObjArrayList);
        });
    }
}
