package com.xk.tp.infrastructure.data.persistence.auth;


import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.tp.infrastructure.data.po.auth.TpAuthNotify;

@Repository
@Table("tp_auth_notify")
public interface TpAuthNotifyMapper {
    int deleteByPrimaryKey(TpAuthNotify record);

    int insert(TpAuthNotify record);

    int insertSelective(TpAuthNotify record);

    TpAuthNotify selectByPrimaryKey(TpAuthNotify record);

    int updateByPrimaryKeySelective(TpAuthNotify record);

    int updateByPrimaryKey(TpAuthNotify record);

    TpAuthNotify selectOneByParams(TpAuthNotify record);
}