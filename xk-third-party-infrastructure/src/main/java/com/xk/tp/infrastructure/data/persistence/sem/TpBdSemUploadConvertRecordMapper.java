package com.xk.tp.infrastructure.data.persistence.sem;

import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.tp.infrastructure.data.po.sem.TpBdSemUploadConvertRecord;


/**
 * <AUTHOR>
 */
@Repository
@Table("tp_bd_sem_upload_convert_record")
public interface TpBdSemUploadConvertRecordMapper {
    int deleteByPrimaryKey(TpBdSemUploadConvertRecord record);

    int insert(TpBdSemUploadConvertRecord record);

    int insertSelective(TpBdSemUploadConvertRecord record);

    TpBdSemUploadConvertRecord selectByPrimaryKey(TpBdSemUploadConvertRecord record);

    int updateByPrimaryKeySelective(TpBdSemUploadConvertRecord record);

    int updateByPrimaryKey(TpBdSemUploadConvertRecord record);
}