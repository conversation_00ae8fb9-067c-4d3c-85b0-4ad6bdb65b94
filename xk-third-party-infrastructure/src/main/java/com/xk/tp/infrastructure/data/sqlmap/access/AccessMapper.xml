<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.tp.infrastructure.data.persistence.access.AccessMapper">
    <resultMap id="BaseResultMap" type="com.xk.tp.infrastructure.data.po.access.TpAccess">
        <id column="access_id" jdbcType="BIGINT" property="accessId"/>
        <result column="access_name" jdbcType="VARCHAR" property="accessName"/>
        <result column="platform_type" jdbcType="INTEGER" property="platformType"/>
        <result column="channel_type" jdbcType="INTEGER" property="channelType"/>
        <result column="business_type" jdbcType="INTEGER" property="businessType"/>
        <result column="business_group" jdbcType="INTEGER" property="businessGroup"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_id" jdbcType="BIGINT" property="updateId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        access_id, access_name, platform_type, channel_type, business_type, business_group,
        create_id, create_time, update_time, update_id,status
    </sql>
    <select id="selectByPrimaryKey" parameterType="com.xk.tp.infrastructure.data.po.access.TpAccess"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tp_access
        where access_id = #{accessId,jdbcType=BIGINT}
    </select>
    <select id="selectByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tp_access
        <where>
            <if test="accessName != null and accessName != ''">
                and access_name like concat('%', #{accessName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="businessType != null">
                and business_type = #{businessType,jdbcType=INTEGER}
            </if>
            <if test="platformType != null">
                and platform_type = #{platformType,jdbcType=INTEGER}
            </if>
            <if test="channelType != null">
                and channel_type = #{channelType, jdbcType=INTEGER}
            </if>
            <if test="businessGroup != null">
                and business_group = #{businessGroup, jdbcType=INTEGER}
            </if>
            <if test="status != null">
                and `status` = #{status, jdbcType=INTEGER}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="selectList" parameterType="com.xk.tp.infrastructure.data.po.access.TpAccess" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tp_access
        <where>
            <if test="accessName != null and accessName != ''">
                and access_name like concat('%', #{accessName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="businessType != null">
                and business_type = #{businessType,jdbcType=INTEGER}
            </if>
            <if test="platformType != null">
                and platform_type = #{platformType,jdbcType=INTEGER}
            </if>
            <if test="channelType != null">
                and channel_type = #{channelType, jdbcType=INTEGER}
            </if>
            <if test="businessGroup != null">
                and business_group = #{businessGroup, jdbcType=INTEGER}
            </if>
            <if test="status != null">
                and `status` = #{status, jdbcType=INTEGER}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="selectByGroupList" parameterType="com.xk.tp.infrastructure.data.po.access.TpAccess"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tp_access
        where business_type = #{businessType,jdbcType=INTEGER}
        and business_group = #{businessGroup, jdbcType=INTEGER}
        and `status` = #{status, jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.xk.tp.infrastructure.data.po.access.TpAccess">
        delete from tp_access
        where access_id = #{accessId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="access_id" keyProperty="accessId"
            parameterType="com.xk.tp.infrastructure.data.po.access.TpAccess" useGeneratedKeys="true">
        insert into tp_access (access_id,access_name, platform_type, channel_type,
        business_type, business_group, create_id,
        create_time, update_time, update_id,`status`
        )
        values (#{accessId},#{accessName,jdbcType=VARCHAR}, #{platformType,jdbcType=INTEGER},
        #{channelType,jdbcType=INTEGER},
        #{businessType,jdbcType=INTEGER}, #{businessGroup,jdbcType=INTEGER}, #{createId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=BIGINT},#{status}
        )
    </insert>
    <insert id="insertSelective" keyColumn="access_id" keyProperty="accessId"
            parameterType="com.xk.tp.infrastructure.data.po.access.TpAccess" useGeneratedKeys="true">
        insert into tp_access
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accessId != null">
                access_id,
            </if>
            <if test="accessName != null">
                access_name,
            </if>
            <if test="platformType != null">
                platform_type,
            </if>
            <if test="channelType != null">
                channel_type,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="businessGroup != null">
                business_group,
            </if>
            <if test="createId != null">
                create_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateId != null">
                update_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accessId != null">
                #{accessId},
            </if>
            <if test="accessName != null">
                #{accessName,jdbcType=VARCHAR},
            </if>
            <if test="platformType != null">
                #{platformType,jdbcType=INTEGER},
            </if>
            <if test="channelType != null">
                #{channelType,jdbcType=INTEGER},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="businessGroup != null">
                #{businessGroup,jdbcType=INTEGER},
            </if>
            <if test="createId != null">
                #{createId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateId != null">
                #{updateId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.tp.infrastructure.data.po.access.TpAccess">
        update tp_access
        <set>
            <if test="accessName != null">
                access_name = #{accessName,jdbcType=VARCHAR},
            </if>
            <if test="platformType != null">
                platform_type = #{platformType,jdbcType=INTEGER},
            </if>
            <if test="channelType != null">
                channel_type = #{channelType,jdbcType=INTEGER},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="businessGroup != null">
                business_group = #{businessGroup,jdbcType=INTEGER},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
        </set>
        where access_id = #{accessId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.tp.infrastructure.data.po.access.TpAccess">
        update tp_access
        set access_name = #{accessName,jdbcType=VARCHAR},
        platform_type = #{platformType,jdbcType=INTEGER},
        channel_type = #{channelType,jdbcType=INTEGER},
        business_type = #{businessType,jdbcType=INTEGER},
        business_group = #{businessGroup,jdbcType=INTEGER},
        create_id = #{createId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_id = #{updateId,jdbcType=BIGINT}
        `status` = #{status}
        where access_id = #{accessId,jdbcType=BIGINT}
    </update>
</mapper>