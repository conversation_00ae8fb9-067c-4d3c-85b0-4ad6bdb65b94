package com.xk.tp.infrastructure.commons.entity;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KuaiDiRspDto {

    private String message;
    private String nu;
    private String ischeck;
    private String com;
    private String status;
    private String condition;
    private String state;
    private Boolean result;
    private String returnCode;
    private List<logisticsMsg> data;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class logisticsMsg {
        private String time;
        private String context;
        private String ftime;
        private String areaCode;
        private String areaName;
        private String status;
        private String location;
        private String areaCenter;
        private String areaPinYin;
        private String statusCode;
    }

}
